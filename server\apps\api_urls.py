from apps.admin.apis.auth import app as admin_auth_router
from apps.admin.apis.organ import app as admin_organ_router
from apps.admin.apis.system import app as admin_system_router
from apps.admin.apis.record import app as admin_record_router
from apps.admin.apis.workplace import app as admin_workplace_router
from apps.admin.apis.analysis import app as admin_analysis_router
from apps.admin.apis.help import app as admin_help_router
from apps.admin.apis.resource import app as admin_resource_router

from apps.front.auth.views import app as app_auth_router
from apps.front.organ.views import app as app_organ_router


# 引入应用中的Admin路由
urlpatterns = [
    {"ApiRouter": admin_auth_router, "prefix": "/admin/auth", "tags": ["认证管理（admin）"]},
    {"ApiRouter": admin_organ_router, "prefix": "/admin/organ", "tags": ["体系管理（admin）"]},
    {"ApiRouter": admin_system_router, "prefix": "/admin/system", "tags": ["系统管理（admin）"]},
    {"ApiRouter": admin_record_router, "prefix": "/admin/record", "tags": ["记录管理（admin）"]},
    {"ApiRouter": admin_workplace_router, "prefix": "/admin/workplace", "tags": ["工作区管理（admin）"]},
    {"ApiRouter": admin_analysis_router, "prefix": "/admin/analysis", "tags": ["数据分析管理（admin）"]},
    {"ApiRouter": admin_help_router, "prefix": "/admin/help", "tags": ["帮助中心管理（admin）"]},
    {"ApiRouter": admin_resource_router, "prefix": "/admin/resource", "tags": ["资源管理（admin）"]},
]

# 引入应用中的Apps基础路由
urlpatterns += [
    {"ApiRouter": app_auth_router, "prefix": "/app/auth", "tags": ["认证管理（app）"]},
    {"ApiRouter": app_organ_router, "prefix": "/app/organ", "tags": ["体系管理（app）"]},
]
