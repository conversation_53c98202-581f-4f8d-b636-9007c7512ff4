<template>
  <div class="message-list">
    <!-- 消息列表 -->
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="fetchMessages"
      class="message-items"
    >
      <van-swipe-cell
        v-for="message in messages"
        :key="message.id"
        class="message-item"
        stop-propagation
      >
        <van-cell
          :title="message.title"
          :class="['message-cell', { 'unread': !message.read }]"
          @click="viewDetail(message)"
        >
          <template #icon>
            <div class="message-icon" :class="getIconClass(message.type)">
              <van-icon :name="getIconName(message.type)" size="20" />
            </div>
          </template>
          <template #label>
            <div class="message-content">
              <p class="content-text">{{ formatContent(message.content) }}</p>
              <div class="message-footer">
                <span class="time">{{ formatTime(message.time) }}</span>
                <van-tag v-if="!message.read" type="primary" size="small" round class="unread-tag">未读</van-tag>
              </div>
            </div>
          </template>
        </van-cell>
        <template #right>
          <van-button
            square
            type="danger"
            text="删除"
            class="delete-button"
            @click="deleteMessage(message.id)"
          />
        </template>
      </van-swipe-cell>
    </van-list>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { showConfirmDialog } from 'vant'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps({
  messages: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['read', 'delete', 'view-detail'])

const loading = ref(false)
const finished = ref(true)

// 获取消息图标
const getIconName = (type) => {
  const iconMap = {
    system: 'info-o',
    interaction: 'friends-o',
    strategy: 'chart-trending-o'
  }
  return iconMap[type] || 'bell'
}

// 获取图标样式类
const getIconClass = (type) => {
  const classMap = {
    system: 'icon-system',
    interaction: 'icon-interaction',
    strategy: 'icon-strategy'
  }
  return classMap[type] || ''
}

// 格式化消息内容（截断过长内容）
const formatContent = (content) => {
  if (!content) return ''
  return content.length > 50 ? content.substring(0, 50) + '...' : content
}

// 格式化时间显示
const formatTime = (time) => {
  if (!time) return ''

  const now = new Date()
  const messageDate = new Date(time)
  const diffMs = now - messageDate
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  // 今天
  if (diffDays === 0) {
    const hours = messageDate.getHours().toString().padStart(2, '0')
    const minutes = messageDate.getMinutes().toString().padStart(2, '0')
    return `${hours}:${minutes}`
  }

  // 昨天
  if (diffDays === 1) {
    return '昨天'
  }

  // 一周内
  if (diffDays < 7) {
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return days[messageDate.getDay()]
  }

  // 更早
  const year = messageDate.getFullYear()
  const month = (messageDate.getMonth() + 1).toString().padStart(2, '0')
  const day = messageDate.getDate().toString().padStart(2, '0')

  // 如果是今年，不显示年份
  if (year === now.getFullYear()) {
    return `${month}-${day}`
  }

  return `${year}-${month}-${day}`
}

// 加载消息
const fetchMessages = () => {
  loading.value = true

  // 实际项目中应从服务器加载数据
  setTimeout(() => {
    loading.value = false
    finished.value = true
  }, 500)
}

// 查看消息详情
const viewDetail = (message) => {
  // 标记为已读
  if (!message.read) {
    emit('read', message.id)
  }

  // 发送查看详情事件
  emit('view-detail', message)
}

// 删除消息
const deleteMessage = (id) => {
  showConfirmDialog({
    title: '删除消息',
    message: '确定要删除这条消息吗？',
  }).then(() => {
    emit('delete', id)
  }).catch(() => {
    // 取消删除
  })
}

// 去回测广场
const goToSquare = () => {
  router.push('/square')
}
</script>

<style scoped>
.message-list {
  padding: 0;
}

.message-items {
  margin-top: 12px;
}

.message-item {
  margin-bottom: 16px;
}

.message-cell {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  background-color: #fff;
  transition: transform 0.2s, box-shadow 0.2s;
}

.message-cell:active {
  transform: scale(0.98);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}

.message-cell :deep(.van-cell__title) {
  flex: 1;
  font-weight: 500;
  font-size: 16px;
}

.message-cell :deep(.van-cell__label) {
  margin-top: 6px;
}

.unread {
  background-color: rgba(25, 137, 250, 0.05);
}

.unread :deep(.van-cell__title) {
  font-weight: 600;
}

.message-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.icon-system {
  background-color: var(--primary-color);
}

.icon-interaction {
  background-color: var(--warning-color);
}

.icon-strategy {
  background-color: var(--success-color);
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.content-text {
  color: var(--text-color-regular);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.message-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2px;
}

.time {
  font-size: 13px;
  color: var(--text-color-secondary);
}

.unread-tag {
  font-size: 11px;
  padding: 0 8px;
  height: 20px;
  line-height: 18px;
  font-weight: 500;
}

.delete-button {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  font-size: 15px;
  font-weight: 500;
}
</style>
