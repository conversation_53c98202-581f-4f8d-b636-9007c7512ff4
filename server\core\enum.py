
from enum import Enum


class SuperEnum(Enum):
    """
    增强版枚举基类
    
    扩展Python标准库Enum的功能，提供:
    1. 字典转换
    2. 键列表获取
    3. 值列表获取
    
    继承此类创建自定义枚举可获得这些增强功能
    
    示例:
        class Status(SuperEnum):
            ACTIVE = 1
            INACTIVE = 0
            
        Status.to_dict()  # {'ACTIVE': 1, 'INACTIVE': 0}
        Status.keys()     # ['ACTIVE', 'INACTIVE']
        Status.values()   # [1, 0]
    """

    @classmethod
    def to_dict(cls):
        """
        将枚举转换为字典
        
        返回:
            dict: 键为枚举项名称，值为枚举项值的字典
            
        示例:
            >>> Status.to_dict()
            {'ACTIVE': 1, 'INACTIVE': 0}
        """
        return {e.name: e.value for e in cls}

    @classmethod
    def keys(cls):
        """
        获取所有枚举项名称列表
        
        返回:
            list[str]: 枚举项名称字符串列表
            
        示例:
            >>> Status.keys()
            ['ACTIVE', 'INACTIVE']
        """
        return cls._member_names_

    @classmethod
    def values(cls):
        """
        获取所有枚举项值列表
        
        返回:
            list: 枚举项值列表(类型取决于具体枚举定义)
            
        示例:
            >>> Status.values()
            [1, 0]
        """
        return list(cls._value2member_map_.keys())
