<template>
  <div class="backtest-card" @click="onCardClick">
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="strategy-info">
        <h3 class="strategy-name">{{ backtest.strategy.name }}</h3>
        <div class="strategy-tags">
          <van-tag plain type="primary" size="small" class="tag">{{ getStrategyTypeText(backtest.strategy.type) }}</van-tag>
          <van-tag v-for="(market, index) in backtest.strategy.markets.slice(0, 2)"
                  :key="index"
                  plain
                  type="success"
                  size="small"
                  class="tag">
            {{ market }}
          </van-tag>
          <van-tag v-if="backtest.strategy.markets.length > 2"
                  plain
                  type="success"
                  size="small"
                  class="tag">
            等{{ backtest.strategy.markets.length }}个市场
          </van-tag>
        </div>
      </div>
      <div class="user-info">
        <span class="username">{{ backtest.userName }}</span>
        <span class="date">{{ formatDate(backtest.date) }}</span>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <p class="description">{{ backtest.description || '该用户没有添加描述' }}</p>

      <!-- 回测结果概览 -->
      <div class="result-overview">
        <div class="result-item">
          <span class="result-value" :class="getReturnClass(backtest.result.finalReturn)">
            {{ backtest.result.finalReturn > 0 ? '+' : '' }}{{ backtest.result.finalReturn }}%
          </span>
          <span class="result-label">总收益率</span>
        </div>
        <div class="result-item">
          <span class="result-value" :class="getReturnClass(backtest.result.annualReturn)">
            {{ backtest.result.annualReturn > 0 ? '+' : '' }}{{ backtest.result.annualReturn }}%
          </span>
          <span class="result-label">年化收益</span>
        </div>
        <div class="result-item">
          <span class="result-value negative">{{ backtest.result.maxDrawdown }}%</span>
          <span class="result-label">最大回撤</span>
        </div>
      </div>
    </div>

    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="action-buttons">
        <div class="action-button" @click.stop="onLike">
          <van-icon :name="isLiked ? 'like' : 'like-o'" :color="isLiked ? '#ee0a24' : ''" />
          <span class="action-count">{{ likeCount }}</span>
        </div>
        <div class="action-button" @click.stop="onCollect">
          <van-icon :name="isCollected ? 'star' : 'star-o'" :color="isCollected ? '#ff9900' : ''" />
          <span class="action-count">{{ collectCount }}</span>
        </div>
        <div class="action-button" @click.stop="onComment">
          <van-icon name="comment-o" />
          <span class="action-count">{{ commentCount }}</span>
        </div>
      </div>
      <div class="view-detail" @click.stop="onViewDetail">
        <span>查看详情</span>
        <van-icon name="arrow" />
      </div>
    </div>
  </div>
</template>

<script setup>

import { useStrategyStore } from '@/stores/strategy'

const props = defineProps({
  backtest: {
    type: Object,
    required: true
  },
  isLiked: {
    type: Boolean,
    default: false
  },
  isCollected: {
    type: Boolean,
    default: false
  },
  likeCount: {
    type: Number,
    default: 0
  },
  collectCount: {
    type: Number,
    default: 0
  },
  commentCount: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['click', 'like', 'collect', 'comment', 'view-detail'])

// 策略类型文本映射
const strategyTypeMap = {
  'trend': '趋势跟踪',
  'mean_reversion': '均值回归',
  'momentum': '动量策略',
  'volatility': '波动率策略'
}

// 获取策略类型文本
const getStrategyTypeText = (type) => {
  return strategyTypeMap[type] || '其他策略'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''

  const d = new Date(date)
  const now = new Date()
  const diff = now - d

  // 如果小于24小时，显示"x小时前"
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return hours > 0 ? `${hours}小时前` : '刚刚'
  }

  // 如果小于30天，显示"x天前"
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }

  // 否则显示具体日期
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
}

// 获取收益率的样式类
const getReturnClass = (value) => {
  if (value > 0) return 'positive'
  if (value < 0) return 'negative'
  return ''
}

// 事件处理函数
const onCardClick = () => {
  emit('click', props.backtest)
}

const onLike = () => {
  emit('like', {
    backtestId: props.backtest.id,
    liked: !props.isLiked
  })
}

const onCollect = () => {
  emit('collect', props.backtest.id)
}

const onComment = () => {
  emit('comment', props.backtest)
}

const onViewDetail = () => {
  emit('view-detail', props.backtest)
}
</script>

<style scoped>
.backtest-card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--card-margin);
  padding: var(--card-padding);
  transition: transform 0.2s, box-shadow 0.2s;
}

.backtest-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.strategy-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.strategy-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.strategy-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  margin-right: 4px;
}

.user-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.card-content {
  margin-bottom: 12px;
}

.description {
  font-size: 14px;
  color: #666;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.result-overview {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  padding: 8px 0;
  border-top: 1px solid #f5f5f5;
  border-bottom: 1px solid #f5f5f5;
}

.result-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.result-value {
  font-size: 16px;
  font-weight: 600;
}

.result-label {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.positive {
  color: #f56c6c;
}

.negative {
  color: #4eb61b;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.action-buttons {
  display: flex;
  gap: 16px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
}

.action-count {
  font-size: 12px;
}

.view-detail {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #1989fa;
  font-size: 14px;
}
</style>
