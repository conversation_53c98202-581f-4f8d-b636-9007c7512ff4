#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : 认证相关的数据模型

from typing import Any
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from apps.front.organ.models import AppUser


class Auth(BaseModel):
    """认证信息"""
    user: AppUser | None = None
    db: AsyncSession

    def __init__(self, **data: Any):
        super().__init__(**data)

    class Config:
        """配置"""
        arbitrary_types_allowed = True


class TokenData(BaseModel):
    """Token数据"""
    access_token: str
    token_type: str = "bearer"
    refresh_token: str | None = None


class TokenPayload(BaseModel):
    """Token载荷"""
    sub: int | None = None  # 用户ID
    app: int | None = None  # 应用ID
    exp: int | None = None  # 过期时间
    is_refresh: bool = False  # 是否是刷新token
