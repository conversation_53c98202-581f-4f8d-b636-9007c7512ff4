version: "3"
services:
  mxtt-api:
    container_name: mxtt-server
    restart: always
    ports:
      - "9000:9000"
    expose:
      - "9000"
    build:
      context: ..
      dockerfile: ./server/Dockerfile
    environment:
      TZ: "Asia/Shanghai"
    volumes:
      - ./mxtt-server:/app
    networks:
      mxtt_network:
        ipv4_address: *********

  mxtt-admin:
    container_name: mxtt-admin
    restart: always
    ports:
      - "80:80"
    expose:
      - "80"
    image: nginx:latest
    environment:
      TZ: "Asia/Shanghai"
    volumes:
      - ./admin/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./:/usr/share/nginx/html
    networks:
      mxtt_network:
        ipv4_address: *********

  mxtt-task:
    container_name: mxtt-task
    restart: always
    build:
      context: ..
      dockerfile: ./task/Dockerfile
    environment:
      TZ: "Asia/Shanghai"
    volumes:
      - ./mxtt-task:/app
    networks:
      mxtt_network:
        ipv4_address: *********

  mxtt-redis:
    container_name: mxtt-redis
    restart: always
    image: redis:latest
    ports:
      - "6379:6379"
    expose:
      - "6379"
    volumes:
      - ./redis/conf/redis.conf:/etc/redis/redis.conf
    environment:
      TZ: "Asia/Shanghai"
    networks:
      mxtt_network:
        ipv4_address: *********
    command: redis-server /etc/redis/redis.conf

  mxtt-mongo:
    container_name: mxtt-mongo
    restart: always
    image: mongo:latest
    ports:
      - "27017:27017"
    expose:
      - "27017"
    volumes:
      - ./mongo/data:/data/db
      - ./mongo/log:/data/log
      - ./mongo/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js # 挂载初始化脚本
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin # root 用户名
      MONGO_INITDB_ROOT_PASSWORD: 123456 # root 用户密码
      TZ: "Asia/Shanghai" # 设置时区
    networks:
      mxtt_network:
        ipv4_address: *********
    command: mongod --auth --bind_ip 0.0.0.0 # 启用身份验证和允许从任何IP连接

  mxtt-mysql:
    container_name: mxtt-mysql
    restart: always
    image: mysql:latest
    ports:
      - "3306:3306"
    expose:
      - "3306"
    volumes:
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/data:/var/lib/mysql
      - ./mysql/logs:/logs
    environment:
      TZ: "Asia/Shanghai"
      MYSQL_ROOT_PASSWORD: "1234"
      MYSQL_DATABASE: "mxtt"
    networks:
      mxtt_network:
        ipv4_address: *********

networks:
  mxtt_network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: "*********/16"
          gateway: "*********"