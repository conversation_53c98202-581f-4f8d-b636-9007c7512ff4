<template>
  <div class="backtest-history">
    <!-- 记录列表 -->
    <div class="empty-state" v-if="records.length === 0 && !loading">
      <van-empty description="暂无回测记录" image="search" />
      <div class="button-container">
        <van-button type="primary" size="normal" @click="goToBacktest" class="action-button">开始回测</van-button>
      </div>
    </div>

    <van-list
      v-else
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="fetchRecords"
      class="record-list"
    >
      <van-swipe-cell
        v-for="record in records"
        :key="record.id"
        class="record-item"
        stop-propagation
      >
        <van-cell
          :title="record.strategyName"
          :label="formatRecordInfo(record)"
          is-link
          @click="viewDetail(record)"
          class="record-cell"
        >
          <template #icon>
            <div class="strategy-icon" :class="getIconClass(record.returnRate)">
              {{ record.strategyName.charAt(0) }}
            </div>
          </template>
          <template #right-icon>
            <div class="return-rate">
              <van-tag :type="getReturnType(record.returnRate)" size="medium" class="rate-tag">
                {{ record.returnRate > 0 ? '+' : '' }}{{ record.returnRate }}%
              </van-tag>
            </div>
          </template>
        </van-cell>
        <template #right>
          <van-button
            square
            type="danger"
            text="删除"
            class="delete-button"
            @click="deleteRecord(record.id)"
          />
        </template>
      </van-swipe-cell>
    </van-list>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBacktestStore } from '@/stores/backtest'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()
const backtestStore = useBacktestStore()

// 列表状态
const loading = ref(false)
const finished = ref(false)
const records = ref([])

// 方法
const fetchRecords = async () => {
  loading.value = true
  try {
    // 从store获取数据
    const allRecords = backtestStore.history || []

    // 如果没有记录，直接返回空数组
    if (!allRecords || allRecords.length === 0) {
      records.value = []
      return
    }

    // 按时间排序
    const sortedRecords = [...allRecords].sort((a, b) => new Date(b.date) - new Date(a.date))

    // 转换数据格式
    records.value = sortedRecords.map(r => ({
      id: r.id,
      strategyName: r.strategy?.name || '未知策略',
      date: new Date(r.date),
      returnRate: r.result?.finalReturn || 0,
      asset: r.asset?.name || '未知资产',
      details: r
    }))
  } catch (error) {
    console.error('获取回测记录失败:', error)
  } finally {
    loading.value = false
    finished.value = true
  }
}

const formatDate = (date) => {
  const now = new Date()
  const recordDate = new Date(date)

  // 如果是今天
  if (recordDate.toDateString() === now.toDateString()) {
    const hours = recordDate.getHours().toString().padStart(2, '0')
    const minutes = recordDate.getMinutes().toString().padStart(2, '0')
    return `今天 ${hours}:${minutes}`
  }

  // 如果是昨天
  const yesterday = new Date(now)
  yesterday.setDate(now.getDate() - 1)
  if (recordDate.toDateString() === yesterday.toDateString()) {
    return '昨天'
  }

  // 其他日期
  const year = recordDate.getFullYear()
  const month = (recordDate.getMonth() + 1).toString().padStart(2, '0')
  const day = recordDate.getDate().toString().padStart(2, '0')

  // 如果是今年
  if (year === now.getFullYear()) {
    return `${month}-${day}`
  }

  return `${year}-${month}-${day}`
}

const formatRecordInfo = (record) => {
  return `${formatDate(record.date)} | ${record.asset || '未知资产'}`
}

const getReturnType = (returnRate) => {
  return returnRate > 0 ? 'success' : 'danger'
}

const getIconClass = (returnRate) => {
  return returnRate > 0 ? 'icon-profit' : 'icon-loss'
}

const viewDetail = (record) => {
  router.push({
    path: '/testnow/detail',
    query: { id: record.id }
  })
}

const deleteRecord = (id) => {
  showConfirmDialog({
    title: '删除确认',
    message: '确定要删除这条回测记录吗？',
  }).then(() => {
    backtestStore.deleteRecord(id)
    records.value = records.value.filter(r => r.id !== id)
    showToast('删除成功')
  }).catch(() => {
    // 取消删除
  })
}

const goToBacktest = () => {
  router.push('/testnow')
}

// 初始化
onMounted(() => {
  fetchRecords()
})
</script>

<style scoped>
.backtest-history {
  padding: 8px var(--content-padding) 24px;
}



.record-list {
  margin-top: 12px;
}

.record-item {
  margin-bottom: 16px;
}

.record-cell {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  background-color: #fff;
  transition: transform 0.2s, box-shadow 0.2s;
}

.record-cell:active {
  transform: scale(0.98);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}

.record-cell :deep(.van-cell__title) {
  font-weight: 500;
  font-size: 16px;
}

.record-cell :deep(.van-cell__label) {
  font-size: 14px;
  margin-top: 4px;
  color: var(--text-color-secondary);
}

.strategy-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  margin-right: 12px;
  font-size: 16px;
}

.icon-profit {
  background-color: var(--success-color);
  box-shadow: 0 2px 8px rgba(7, 193, 96, 0.2);
}

.icon-loss {
  background-color: var(--danger-color);
  box-shadow: 0 2px 8px rgba(238, 10, 36, 0.2);
}

.return-rate {
  display: flex;
  align-items: center;
}

.rate-tag {
  font-weight: bold;
  padding: 2px 10px;
  border-radius: 4px;
  font-size: 14px;
}

.delete-button {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  font-size: 15px;
  font-weight: 500;
}

.empty-state {
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-state .van-empty {
  padding-bottom: 32px;
}

.empty-state :deep(.van-empty__image) {
  width: 120px;
  height: 120px;
}

.empty-state :deep(.van-empty__description) {
  font-size: 15px;
  color: var(--text-color-secondary);
  margin-top: 16px;
}

.button-container {
  width: 100%;
  padding: 0 var(--content-padding);
  margin-top: 24px;
  margin-bottom: 24px;
}

.action-button {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  font-weight: 500;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.2);
}
</style>
