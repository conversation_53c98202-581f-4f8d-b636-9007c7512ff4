# 量化回测系统 (Quant-H5)

移动端量化交易回测系统，基于 Vue 3 + Vite + Vant UI 开发。

## 功能特点

- 支持多种量化交易策略回测
- 回测广场浏览和选择
- 个人中心管理回测记录
- 离线支持 (PWA)
- 移动端优化体验

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI 框架**: Vant UI
- **状态管理**: Pinia (带持久化)
- **路由**: Vue Router
- **图表**: Lightweight Charts
- **测试**: Vitest + Vue Test Utils
- **代码规范**: ESLint + Prettier
- **离线支持**: PWA (Workbox)

## 项目优化

本项目已进行以下优化：

### 性能优化

- **代码分割与懒加载**: 所有路由使用懒加载，并添加 chunk 命名以便于调试和缓存
- **Vite 构建优化**: 配置更好的生产性能，资源压缩，现代浏览器目标
- **组件优化**: 使用 `v-memo` 和 `keep-alive` 减少不必要的重渲染
- **状态管理改进**: Pinia 存储持久化，更好的数据缓存策略

### 代码质量改进

- **代码结构改进**: 更好的文件夹结构和命名约定
- **测试设置**: 添加 Vitest 单元测试和 Vue Test Utils 组件测试
- **代码质量工具**: 增强的 ESLint 配置，添加 Prettier 以保持代码格式一致

### 用户体验改进

- **移动端优化**: 改进触摸交互，添加更好的加载状态
- **离线支持**: 添加 PWA 功能，实现离线访问的服务工作者
- **性能监控**: 添加性能监控和错误跟踪

## 开发指南

### 安装依赖

```bash
npm install
```

### 开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

### 代码格式化

```bash
npm run format
```

### 代码检查

```bash
npm run lint
```

### 运行测试

```bash
npm run test
```

### 监视模式运行测试

```bash
npm run test:watch
```

## 项目结构

```
quant-h5/
├── public/             # 静态资源
├── src/
│   ├── components/     # 通用组件
│   ├── router/         # 路由配置
│   ├── stores/         # Pinia 状态存储
│   ├── views/          # 页面视图
│   ├── App.vue         # 根组件
│   └── main.js         # 入口文件
├── .eslintrc.js        # ESLint 配置
├── .prettierrc         # Prettier 配置
├── index.html          # HTML 模板
├── package.json        # 项目依赖
├── vite.config.js      # Vite 配置
└── vitest.config.js    # Vitest 配置
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

[MIT](LICENSE)
