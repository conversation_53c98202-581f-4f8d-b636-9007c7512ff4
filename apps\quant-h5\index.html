<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover">
    <title>量化回测系统</title>
    <!-- 微信环境下的特殊处理 -->
    <script>
      // 检测是否在微信环境中
      var isWechatBrowser = /MicroMessenger/i.test(navigator.userAgent);
      if (isWechatBrowser) {
        // 在微信环境中，添加特殊的类名
        document.documentElement.classList.add('wechat-html');
      }
    </script>
    <style>
      /* 微信环境下的特殊样式 */
      .wechat-html body {
        /* 确保微信环境下页面正常显示 */
        padding-top: 0 !important;
        margin-top: 0 !important;
      }

      /* 确保页面可以滚动 */
      html, body {
        height: 100%;
        overflow-y: auto !important;
      }

      /* 确保内容可以滚动 */
      #app {
        min-height: 100vh;
        overflow-y: auto !important;
      }
    </style>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>