import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as api from '@/api/user'
import router from '@/router'

export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref({
    id: null,
    nickname: '',
    avatar: '',
    riskLevel: 3,
    computePower: 0, // 算力值
    isGuest: true, // 是否为游客
    settings: {
      defaultCapital: 100000,
      defaultPeriod: 30
    }
  })

  // 认证相关 - 从localStorage初始化
  const token = ref(localStorage.getItem('token') || '')
  const refreshToken = ref(localStorage.getItem('refreshToken') || '')
  const isLoggedIn = computed(() => !!token.value)

  // 最近使用的资产
  const recentAssets = ref([])

  // 登录方法
  const login = async (loginData) => {
    try {
      const res = await api.login(loginData)
      if (res.data) {
        token.value = res.data.access_token
        refreshToken.value = res.data.refresh_token

        // 同时存储到localStorage
        localStorage.setItem('token', res.data.access_token)
        localStorage.setItem('refreshToken', res.data.refresh_token)

        await fetchUserInfo()
        return true
      }
      return false
    } catch (error) {
      console.error('登录失败:', error)
      return false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    // 检查token是否存在
    const currentToken = token.value || localStorage.getItem('token')
    if (!currentToken) return

    try {
      const res = await api.getUserInfo()
      if (res.code === 200 && res.data) {
        userInfo.value = {
          ...userInfo.value,
          id: res.data.id,
          nickname: res.data.nickname || '量化投资者',
          avatar: res.data.avatar || '',
          riskLevel: res.data.risk_level || 3,
          computePower: res.data.compute_power || 0,
          isGuest: false
        }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果是401错误，清除token
      if (error.response && error.response.status === 401) {
        token.value = ''
        refreshToken.value = ''
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        resetUserInfo()
      }
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      if (token.value) {
        await api.logout()
      }
    } catch (error) {
      console.error('退出登录失败:', error)
    } finally {
      // 无论API调用是否成功，都清除本地状态
      token.value = ''
      refreshToken.value = ''

      // 同时从localStorage中移除
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')

      resetUserInfo()
      router.push('/login')
    }
  }

  // 重置用户信息为游客状态
  const resetUserInfo = () => {
    userInfo.value = {
      id: null,
      nickname: '量化投资者',
      avatar: '',
      riskLevel: 3,
      computePower: 0,
      isGuest: true,
      settings: {
        defaultCapital: 100000,
        defaultPeriod: 30
      }
    }
  }

  // 更新头像
  const updateAvatar = (avatarUrl) => {
    userInfo.value.avatar = avatarUrl
  }

  // 更新昵称
  const updateNickname = (nickname) => {
    userInfo.value.nickname = nickname
  }

  // 更新风险等级
  const updateRiskLevel = (level) => {
    userInfo.value.riskLevel = level
  }

  // 更新设置
  const updateSettings = (settings) => {
    userInfo.value.settings = {
      ...userInfo.value.settings,
      ...settings
    }
  }

  // 更新算力值
  const updateComputePower = (power) => {
    userInfo.value.computePower = power
  }

  // 充值算力
  const rechargeComputePower = async (amount) => {
    if (userInfo.value.isGuest) return false

    try {
      const res = await api.rechargeComputePower(amount)
      if (res.code === 200 && res.data) {
        userInfo.value.computePower = res.data.compute_power
        return true
      }
      return false
    } catch (error) {
      console.error('充值算力失败:', error)
      return false
    }
  }

  // 消费算力
  const consumeComputePower = async (amount) => {
    if (userInfo.value.isGuest) return false

    try {
      const res = await api.consumeComputePower(amount)
      if (res.code === 200 && res.data) {
        userInfo.value.computePower = res.data.compute_power
        return true
      }
      return false
    } catch (error) {
      console.error('消费算力失败:', error)
      return false
    }
  }

  // 添加最近使用的资产
  const addRecentAsset = (asset) => {
    // 去重
    recentAssets.value = recentAssets.value.filter(
      item => item.code !== asset.code
    )
    // 添加到最前面
    recentAssets.value.unshift(asset)
    // 最多保留5个
    if (recentAssets.value.length > 5) {
      recentAssets.value = recentAssets.value.slice(0, 5)
    }
  }

  // 检查是否有足够的算力
  const hasEnoughComputePower = (required) => {
    if (userInfo.value.isGuest) return false
    return userInfo.value.computePower >= required
  }

  // 初始化方法 - 在应用启动时调用
  const init = async () => {
    // 如果有token，尝试获取用户信息
    if (token.value || localStorage.getItem('token')) {
      await fetchUserInfo()
    } else {
      // 如果没有token，设置为游客模式
      resetUserInfo()
    }
  }

  // 自动初始化
  init()

  return {
    userInfo,
    recentAssets,
    token,
    refreshToken,
    isLoggedIn,
    login,
    logout,
    fetchUserInfo,
    updateAvatar,
    updateNickname,
    updateRiskLevel,
    updateSettings,
    updateComputePower,
    rechargeComputePower,
    consumeComputePower,
    addRecentAsset,
    hasEnoughComputePower,
    init
  }
}, {
  persist: {
    paths: ['token', 'refreshToken', 'userInfo']
  }
})
