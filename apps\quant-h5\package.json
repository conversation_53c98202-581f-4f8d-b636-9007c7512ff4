{"name": "quant-h5", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src", "format": "prettier --write \"src/**/*.{js,vue,css,scss}\"", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"axios": "^1.9.0", "echarts": "^5.6.0", "lightweight-charts": "^4.0.0", "pinia": "^2.1.0", "pinia-plugin-persistedstate": "^3.2.0", "pinyin-pro": "^3.26.0", "vant": "^4.0.0", "vue": "^3.3.0", "vue-router": "^4.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.0.0", "@vue/test-utils": "^2.4.0", "eslint": "^8.40.0", "eslint-plugin-vue": "^9.15.0", "jsdom": "^22.1.0", "prettier": "^3.0.0", "terser": "^5.19.0", "vite": "^4.0.0", "vite-plugin-compression2": "^0.10.0", "vite-plugin-pwa": "^0.16.0", "vitest": "^0.34.0", "workbox-window": "^7.0.0"}}