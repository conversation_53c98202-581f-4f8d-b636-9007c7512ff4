<template>
  <div class="strategy-collection">
    <!-- 策略列表 -->
    <div v-if="filteredStrategies.length === 0" class="empty-state">
      <van-empty description="暂无收藏策略" image="star" />
      <div class="button-container">
        <van-button type="primary" size="normal" @click="goToSquare" class="action-button">去回测广场</van-button>
      </div>
    </div>

    <div v-else class="strategy-list">
      <van-swipe-cell
        v-for="strategy in filteredStrategies"
        :key="strategy.id"
        class="strategy-item"
        stop-propagation
      >
        <van-cell
          :title="strategy.name"
          @click="viewStrategy(strategy)"
          class="strategy-cell"
          is-link
        >
          <template #icon>
            <div class="strategy-icon" :style="{ backgroundColor: getStrategyColor(strategy.type) }">
              {{ strategy.name.charAt(0) }}
            </div>
          </template>
          <template #label>
            <div class="strategy-info">
              <span class="strategy-desc">{{ formatDescription(strategy.description) }}</span>
              <div class="strategy-tags">
                <van-tag
                  v-if="hasUpdate(strategy)"
                  type="warning"
                  size="small"
                  class="update-tag"
                >
                  有更新
                </van-tag>
                <van-tag
                  v-for="tag in strategy.tags?.slice(0, 2)"
                  :key="tag"
                  type="primary"
                  size="small"
                  plain
                  class="strategy-tag"
                >
                  {{ tag }}
                </van-tag>
              </div>
            </div>
          </template>
        </van-cell>
        <template #right>
          <van-button
            square
            type="danger"
            text="取消收藏"
            class="delete-button"
            @click="deleteStrategy(strategy.id)"
          />
        </template>
      </van-swipe-cell>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStrategyStore } from '@/stores/strategy'
import { showToast, showConfirmDialog } from 'vant'

const router = useRouter()
const strategyStore = useStrategyStore()

// 计算属性
const filteredStrategies = computed(() => {
  // 按名称排序
  return [...strategyStore.collectedStrategies].sort((a, b) => {
    const nameA = a.name.toLowerCase()
    const nameB = b.name.toLowerCase()
    return nameA.localeCompare(nameB)
  })
})

const formatDescription = (description) => {
  if (!description) return '暂无描述'
  return description.length > 60 ? description.substring(0, 60) + '...' : description
}

const viewStrategy = (strategy) => {
  router.push({
    path: '/square',
    query: { strategyId: strategy.id }
  })
}

const deleteStrategy = (strategyId) => {
  showConfirmDialog({
    title: '取消收藏',
    message: '确定要取消收藏这个策略吗？',
  }).then(() => {
    strategyStore.toggleCollect(strategyId)
    showToast('已取消收藏')
  }).catch(() => {
    // 取消操作
  })
}

const goToSquare = () => {
  router.push('/square')
}

const getStrategyColor = (type) => {
  const colorMap = {
    trend: '#1989fa',
    mean_reversion: '#ff976a',
    arbitrage: '#07c160',
    hedge: '#ee0a24'
  }
  return colorMap[type] || '#c8c9cc'
}

const hasUpdate = (strategy) => {
  // 模拟更新检测
  return strategyStore.hasUpdate(strategy)
}
</script>

<style scoped>
.strategy-collection {
  padding: 8px var(--content-padding) 24px;
}



.strategy-list {
  margin-top: 12px;
}

.strategy-item {
  margin-bottom: 16px;
}

.strategy-cell {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  background-color: #fff;
  transition: transform 0.2s, box-shadow 0.2s;
}

.strategy-cell:active {
  transform: scale(0.98);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}

.strategy-cell :deep(.van-cell__title) {
  flex: 3;
  font-weight: 500;
  font-size: 16px;
}

.strategy-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  margin-right: 12px;
  font-size: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.strategy-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 2px;
}

.strategy-desc {
  color: var(--text-color-secondary);
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.strategy-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 6px;
}

.strategy-tag {
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 4px;
}

.update-tag {
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 20px;
  border-radius: 4px;
  font-weight: 500;
}

.delete-button {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  font-size: 15px;
  font-weight: 500;
}

.empty-state {
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-state .van-empty {
  padding-bottom: 32px;
}

.empty-state :deep(.van-empty__image) {
  width: 120px;
  height: 120px;
}

.empty-state :deep(.van-empty__description) {
  font-size: 15px;
  color: var(--text-color-secondary);
  margin-top: 16px;
}

.button-container {
  width: 100%;
  padding: 0 var(--content-padding);
  margin-top: 24px;
  margin-bottom: 24px;
}

.action-button {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  font-weight: 500;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.2);
}
</style>
