#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : 增删改查

from typing import Any
from redis.asyncio import Redis
from sqlalchemy.orm import joinedload
from sqlalchemy.orm.strategy_options import _AbstractLoad
from core.exception import CustomException
from fastapi.encoders import jsonable_encoder
from sqlalchemy import select, false
from core.crud import DalBase
from sqlalchemy.ext.asyncio import AsyncSession
from core.validator import vali_telephone
from utils.tools import test_password
from . import models, schemas
from utils import status
from datetime import datetime


class AppDal(DalBase):
    """应用管理"""

    def __init__(self, db: AsyncSession):
        super(AppDal, self).__init__()
        self.db = db
        self.model = models.AppBase
        self.schema = schemas.AppBaseOut

    async def create_data(
            self,
            data: schemas.AppBaseIn,
            v_options: list[_AbstractLoad] = None,
            v_return_obj: bool = False,
            v_schema: Any = None
    ) -> Any:
        """创建应用"""
        obj = self.model(**data.model_dump())
        await self.flush(obj)
        # 创建应用设置
        settings = models.AppSettings(app_id=obj.id)
        await self.flush(settings)
        return await self.out_dict(obj, v_options, v_return_obj, v_schema)

    async def get_app_full_info(self, app_id: int) -> schemas.AppFullOut:
        """获取应用完整信息"""
        sql = select(self.model).where(
            self.model.id == app_id,
            self.model.is_delete == false()
        ).options(joinedload(self.model.settings))
        result = await self.db.scalars(sql)
        obj = result.unique().first()
        if not obj:
            raise CustomException("应用不存在", code=404)
        return schemas.AppFullOut.model_validate(obj)


class UserDal(DalBase):
    """用户管理"""

    def __init__(self, db: AsyncSession):
        super(UserDal, self).__init__()
        self.db = db
        self.model = models.AppUser
        self.schema = schemas.UserSimpleOut

    async def create_data(
            self,
            data: schemas.UserIn,
            v_options: list[_AbstractLoad] = None,
            v_return_obj: bool = False,
            v_schema: Any = None
    ) -> Any:
        """创建用户"""
        # 检查手机号是否已存在
        unique = await self.get_data(telephone=data.telephone, v_return_none=True)
        if unique:
            raise CustomException("手机号已存在！", code=status.HTTP_ERROR)

        # 创建用户
        obj = self.model(**data.model_dump())
        if data.password:
            obj.password = self.model.get_password_hash(data.password)
        await self.flush(obj)

        # 创建用户与应用的关联
        user_base = models.AppUserBase(user_id=obj.id, app_id=data.app_id)
        await self.flush(user_base)

        return await self.out_dict(obj, v_options, v_return_obj, v_schema)

    async def update_login_info(self, user: models.AppUser, last_ip: str) -> None:
        """更新登录信息"""
        user.last_ip = last_ip
        user.last_login = datetime.now()
        await self.db.flush()

    async def reset_current_password(self, user: models.AppUser, data: schemas.ResetPwd) -> None:
        """重置密码"""
        if data.password != data.password_two:
            raise CustomException(msg="两次密码不一致", code=400)
        result = test_password(data.password)
        if isinstance(result, str):
            raise CustomException(msg=result, code=400)
        user.password = self.model.get_password_hash(data.password)
        await self.flush(user)

    async def update_current_info(self, user: models.AppUser, data: schemas.UserUpdateBaseInfo) -> Any:
        """更新当前用户基本信息"""
        user_dict = jsonable_encoder(data)
        for key, value in user_dict.items():
            if value is not None:
                setattr(user, key, value)
        await self.flush(user)
        return await self.out_dict(user)

    async def update_wx_info(self, user: models.AppUser, openid: str, unionid: str = None) -> None:
        """更新微信信息"""
        user.wx_openid = openid
        if unionid:
            user.wx_unionid = unionid
        await self.flush(user)
