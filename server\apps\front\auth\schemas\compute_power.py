#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : 算力相关的数据模型

from pydantic import BaseModel, Field


class ComputePowerRecharge(BaseModel):
    """算力充值请求"""
    amount: int = Field(..., description="充值数量", gt=0)


class ComputePowerConsume(BaseModel):
    """算力消费请求"""
    amount: int = Field(..., description="消费数量", gt=0)


class ComputePowerResponse(BaseModel):
    """算力响应"""
    compute_power: int = Field(..., description="当前算力值")
    message: str = Field(..., description="操作结果消息")
