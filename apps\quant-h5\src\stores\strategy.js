import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useUserStore } from './user'

export const useStrategyStore = defineStore('strategy', () => {
  const strategies = ref([])
  const selectedStrategy = ref(null)
  const popularStrategies = ref([])
  const collectedStrategies = ref([])
  const collectionGroups = ref([
    {
      id: 1,
      name: '默认分组',
      strategyIds: []
    }
  ])

  // 点赞、留言和回复相关数据
  const likes = ref({}) // 策略ID -> 点赞数
  const userLikes = ref({}) // 策略ID -> 用户是否点赞
  const comments = ref({}) // 策略ID -> 评论数组
  const replies = ref({}) // 评论ID -> 回复数组

  const fetchStrategies = async () => {
    // 模拟API请求
    strategies.value = [
      {
        id: 1,
        name: '双均线策略',
        type: 'trend',
        description: '通过短期和长期均线交叉产生交易信号',
        riskLevel: 3,
        tags: ['趋势跟踪', '技术指标'],
        markets: ['A股', '港股', '美股'],
        hotness: 1245,
        parameters: [
          { name: 'fastPeriod', label: '快线周期', type: 'number', default: 5, min: 1, max: 50 },
          { name: 'slowPeriod', label: '慢线周期', type: 'number', default: 20, min: 5, max: 100 }
        ],
        performance: {
          annualizedReturn: 15.6,
          maxDrawdown: 22.3,
          winRate: 58.7
        },
        updatedAt: new Date('2023-06-10')
      },
      {
        id: 2,
        name: 'RSI超买超卖',
        type: 'mean_reversion',
        description: '基于RSI指标在超买超卖区域进行反向交易',
        riskLevel: 2,
        tags: ['均值回归', '技术指标'],
        markets: ['A股', '港股'],
        hotness: 876,
        parameters: [
          { name: 'rsiPeriod', label: 'RSI周期', type: 'number', default: 14, min: 5, max: 30 },
          { name: 'overbought', label: '超买阈值', type: 'number', default: 70, min: 50, max: 90 },
          { name: 'oversold', label: '超卖阈值', type: 'number', default: 30, min: 10, max: 50 }
        ],
        performance: {
          annualizedReturn: 12.1,
          maxDrawdown: 18.5,
          winRate: 62.3
        },
        updatedAt: new Date('2023-06-15')
      }
    ]
    popularStrategies.value = strategies.value.slice(0, 3)

    // 初始化点赞数据
    strategies.value.forEach(strategy => {
      if (!likes.value[strategy.id]) {
        likes.value[strategy.id] = Math.floor(Math.random() * 100) + 10
      }

      if (!comments.value[strategy.id]) {
        comments.value[strategy.id] = []
      }
    })
  }

  const selectStrategy = (strategy) => {
    selectedStrategy.value = strategy
  }

  const toggleCollect = (strategyId) => {
    const index = collectedStrategies.value.findIndex(s => s.id === strategyId)
    if (index >= 0) {
      collectedStrategies.value.splice(index, 1)
      // 从所有分组中移除
      collectionGroups.value.forEach(group => {
        group.strategyIds = group.strategyIds.filter(id => id !== strategyId)
      })
    } else {
      const strategy = strategies.value.find(s => s.id === strategyId)
      if (strategy) {
        collectedStrategies.value.push(strategy)
        // 添加到默认分组
        collectionGroups.value[0].strategyIds.push(strategyId)
      }
    }
  }

  const addCollectionGroup = (groupName) => {
    collectionGroups.value.push({
      id: Date.now(),
      name: groupName,
      strategyIds: []
    })
  }

  const moveToGroup = (strategyIds, targetGroupId) => {
    // 从原分组移除
    collectionGroups.value.forEach(group => {
      group.strategyIds = group.strategyIds.filter(id => !strategyIds.includes(id))
    })

    // 添加到目标分组
    const targetGroup = collectionGroups.value.find(g => g.id === targetGroupId)
    if (targetGroup) {
      targetGroup.strategyIds.push(...strategyIds)
    }
  }

  const hasUpdate = (strategyId) => {
    const strategy = strategies.value.find(s => s.id === strategyId)
    if (!strategy) return false

    const collected = collectedStrategies.value.find(s => s.id === strategyId)
    if (!collected) return false

    return new Date(strategy.updatedAt) > new Date(collected.updatedAt)
  }

  // 点赞相关方法
  const toggleLike = (strategyId) => {
    const userStore = useUserStore()
    const userId = userStore.userInfo.id

    // 切换用户点赞状态
    if (!userLikes.value[strategyId]) {
      userLikes.value[strategyId] = []
    }

    const userLiked = userLikes.value[strategyId].includes(userId)

    if (userLiked) {
      // 取消点赞
      userLikes.value[strategyId] = userLikes.value[strategyId].filter(id => id !== userId)
      likes.value[strategyId]--
    } else {
      // 添加点赞
      userLikes.value[strategyId].push(userId)
      likes.value[strategyId]++
    }

    return !userLiked
  }

  // 检查用户是否点赞
  const isLiked = (strategyId) => {
    const userStore = useUserStore()
    const userId = userStore.userInfo.id

    return userLikes.value[strategyId] && userLikes.value[strategyId].includes(userId)
  }

  // 获取策略点赞数
  const getLikeCount = (strategyId) => {
    return likes.value[strategyId] || 0
  }

  // 添加评论
  const addComment = (strategyId, content) => {
    const userStore = useUserStore()

    const comment = {
      id: Date.now(),
      userId: userStore.userInfo.id,
      userName: userStore.userInfo.nickname,
      userAvatar: userStore.userInfo.avatar,
      content,
      createTime: new Date(),
      likes: 0
    }

    if (!comments.value[strategyId]) {
      comments.value[strategyId] = []
    }

    comments.value[strategyId].unshift(comment)
    return comment
  }

  // 添加回复
  const addReply = (commentId, content, replyToId = null) => {
    const userStore = useUserStore()

    const reply = {
      id: Date.now(),
      commentId,
      userId: userStore.userInfo.id,
      userName: userStore.userInfo.nickname,
      userAvatar: userStore.userInfo.avatar,
      content,
      createTime: new Date(),
      replyToId,
      replyToName: replyToId ? getUserNameById(replyToId) : null
    }

    if (!replies.value[commentId]) {
      replies.value[commentId] = []
    }

    replies.value[commentId].push(reply)
    return reply
  }

  // 获取评论列表
  const getComments = (strategyId) => {
    return comments.value[strategyId] || []
  }

  // 获取回复列表
  const getReplies = (commentId) => {
    return replies.value[commentId] || []
  }

  // 辅助方法：根据用户ID获取用户名
  const getUserNameById = (userId) => {
    const userStore = useUserStore()
    return userId === userStore.userInfo.id ? userStore.userInfo.nickname : '用户'
  }

  // 设置初始点赞数（用于模拟数据）
  const setInitialLikes = (strategyId, count) => {
    if (!likes.value[strategyId]) {
      likes.value[strategyId] = count
    }
    if (!userLikes.value[strategyId]) {
      userLikes.value[strategyId] = []
    }
    return count
  }

  return {
    strategies,
    selectedStrategy,
    popularStrategies,
    collectedStrategies,
    collectionGroups,
    fetchStrategies,
    selectStrategy,
    toggleCollect,
    addCollectionGroup,
    moveToGroup,
    hasUpdate,

    // 点赞、评论相关
    toggleLike,
    isLiked,
    getLikeCount,
    setInitialLikes,
    addComment,
    addReply,
    getComments,
    getReplies
  }
})
