# For more information on configuration, see:
#   * Official English Documentation: http://nginx.org/en/docs/
#   * Official Russian Documentation: http://nginx.org/ru/docs/

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 4096;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    server
    {
        listen       80;
        server_name localhost;
        client_max_body_size 100M;

        location / {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            set_real_ip_from 0.0.0.0/0;
            real_ip_header X-Forwarded-For;
            root /usr/share/nginx/html/mxtt-admin/dist-pro;
            index  index.html;
            try_files $uri $uri/ /index.html; # 防止刷新报404
        }

        location /api/ {
            proxy_http_version 1.1;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Nginx-Proxy true;
            set_real_ip_from 0.0.0.0/0;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_connect_timeout 600s;
            proxy_read_timeout 600s;
            proxy_send_timeout 600s;
            real_ip_header X-Forwarded-For;
            rewrite ^/api/(.*)$ /$1 break;  #重写
            proxy_pass http://*********:9000/; # 设置代理服务器的协议和地址
        }


        location /media/ {
            alias /usr/share/nginx/html/mxtt-api/static/;
        }

        location /temp/ {
            alias /usr/share/nginx/html/mxtt-api/temp/;
        }

        location ~ /openapi\.json$ {
            proxy_pass http://*********:9000;
        }

        location @router {
            rewrite ^.*$ /index.html break;
        }
     }

}

