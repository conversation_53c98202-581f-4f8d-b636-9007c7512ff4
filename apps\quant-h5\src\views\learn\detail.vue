<template>
  <div class="course-detail">
    <van-nav-bar
      :title="course?.title || '课程详情'"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
      class="nav-bar"
    />

    <!-- 课程内容 -->
    <div class="course-container">
      <!-- 课程头部信息 -->
      <div class="course-header">
        <!-- 视频课程 -->
        <div v-if="course?.type === 'video'" class="video-container">
          <video 
            :src="course?.videoUrl" 
            controls 
            class="course-video"
            poster="course?.image"
            @play="onVideoPlay"
          ></video>
        </div>
        
        <!-- 图文课程 -->
        <div v-else class="image-container">
          <img :src="course?.image" alt="课程封面" class="course-image" />
        </div>
        
        <div class="course-info">
          <h1 class="course-title">{{ course?.title }}</h1>
          <div class="course-meta">
            <span class="course-author">{{ course?.author }}</span>
            <span class="course-level" :class="levelClass">{{ course?.level }}</span>
            <span class="course-type" :class="typeClass">{{ getTypeText(course?.type) }}</span>
          </div>
          <div class="course-stats">
            <div class="stat-item">
              <van-icon name="eye-o" />
              <span>{{ formatNumber(course?.views) }}</span>
            </div>
            <div class="stat-item">
              <van-icon name="like-o" />
              <span>{{ formatNumber(course?.likes) }}</span>
            </div>
            <div class="stat-item">
              <van-icon name="clock-o" />
              <span>{{ course?.duration }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 课程内容 -->
      <div class="course-content">
        <van-tabs 
          v-model="activeTab" 
          sticky 
          animated
          color="var(--primary-color)"
          title-active-color="var(--primary-color)"
          title-inactive-color="var(--text-color-regular)"
        >
          <van-tab title="课程介绍" name="intro">
            <div class="tab-content">
              <div class="section">
                <h3 class="section-title">课程简介</h3>
                <p class="section-text">{{ course?.description }}</p>
              </div>
              
              <div class="section">
                <h3 class="section-title">适合人群</h3>
                <ul class="target-list">
                  <li v-for="(target, index) in course?.targetAudience" :key="index">
                    <van-icon name="success" color="var(--success-color)" />
                    <span>{{ target }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </van-tab>
          
          <van-tab title="课程内容" name="content">
            <div class="tab-content">
              <!-- 图文课程内容 -->
              <div v-if="course?.type === 'article'" class="article-content">
                <div v-html="course?.content" class="rich-text"></div>
              </div>
              
              <!-- 视频课程章节列表 -->
              <div v-else class="chapter-list">
                <div 
                  v-for="(chapter, index) in course?.chapters" 
                  :key="index"
                  class="chapter-item"
                  @click="playChapter(chapter, index)"
                >
                  <div class="chapter-info">
                    <div class="chapter-index">{{ index + 1 }}</div>
                    <div class="chapter-detail">
                      <div class="chapter-title">{{ chapter.title }}</div>
                      <div class="chapter-duration">{{ chapter.duration }}</div>
                    </div>
                  </div>
                  <van-icon 
                    :name="currentChapter === index ? 'play-circle' : 'play-circle-o'" 
                    :color="currentChapter === index ? 'var(--primary-color)' : ''"
                    size="20"
                  />
                </div>
              </div>
            </div>
          </van-tab>
          
          <van-tab title="评论" name="comments">
            <div class="tab-content">
              <div v-if="course?.comments && course.comments.length > 0" class="comment-list">
                <div v-for="(comment, index) in course.comments" :key="index" class="comment-item">
                  <div class="comment-user">
                    <img :src="comment.avatar || 'https://img.yzcdn.cn/vant/cat.jpeg'" alt="用户头像" class="user-avatar" />
                    <div class="user-info">
                      <div class="user-name">{{ comment.username }}</div>
                      <div class="comment-time">{{ comment.time }}</div>
                    </div>
                  </div>
                  <div class="comment-content">{{ comment.content }}</div>
                </div>
              </div>
              <van-empty v-else description="暂无评论" />
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-bar">
      <div class="action-buttons">
        <van-button 
          type="primary" 
          block 
          @click="handleLike"
          :icon="isLiked ? 'like' : 'like-o'"
        >
          {{ isLiked ? '已点赞' : '点赞' }}
        </van-button>
      </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="safe-area-inset-bottom"></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'

const route = useRoute()
const router = useRouter()
const courseId = computed(() => route.params.id)
const activeTab = ref('intro')
const course = ref(null)
const isLiked = ref(false)
const currentChapter = ref(0)

// 根据课程级别设置不同的样式类
const levelClass = computed(() => {
  const levelMap = {
    '入门': 'level-basic',
    '进阶': 'level-advanced',
    '实战': 'level-case'
  }
  return levelMap[course.value?.level] || 'level-basic'
})

// 根据课程类型设置不同的样式类
const typeClass = computed(() => {
  const typeMap = {
    'video': 'type-video',
    'article': 'type-article'
  }
  return typeMap[course.value?.type] || 'type-article'
})

// 获取课程类型文本
const getTypeText = (type) => {
  const typeMap = {
    'video': '视频课程',
    'article': '图文教程'
  }
  return typeMap[type] || '图文教程'
}

// 格式化数字
const formatNumber = (num) => {
  if (!num) return 0
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 点赞课程
const handleLike = () => {
  isLiked.value = !isLiked.value
  if (isLiked.value) {
    course.value.likes++
    showToast('点赞成功')
  } else {
    course.value.likes--
    showToast('已取消点赞')
  }
}

// 播放视频章节
const playChapter = (chapter, index) => {
  currentChapter.value = index
  if (course.value.type === 'video') {
    // 在实际应用中，这里应该更新视频源
    showToast(`正在播放: ${chapter.title}`)
    
    // 模拟更新视频源
    const videoElement = document.querySelector('.course-video')
    if (videoElement) {
      // 实际项目中应该设置真实的视频URL
      // videoElement.src = chapter.videoUrl
      videoElement.currentTime = 0
      videoElement.play()
    }
  }
}

// 视频播放事件
const onVideoPlay = () => {
  // 可以在这里添加视频播放的统计逻辑
  console.log('视频开始播放')
}

// 获取课程详情
const fetchCourseDetail = async () => {
  try {
    // 实际项目中应该从API获取数据
    // const response = await api.getCourseDetail(courseId.value)
    // course.value = response.data
    
    // 模拟数据
    setTimeout(() => {
      // 根据ID判断是视频课程还是图文课程
      if (Number(courseId.value) % 2 === 0) {
        // 视频课程
        course.value = {
          id: courseId.value,
          title: '量化交易策略实战教程',
          author: '量化学院',
          level: '进阶',
          type: 'video',
          views: 2345,
          likes: 567,
          duration: '2小时30分钟',
          image: 'https://img.yzcdn.cn/vant/cat.jpeg',
          videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4', // 示例视频URL
          description: '本课程将带你深入了解量化交易策略的实战应用，从基础概念到高级策略，全面掌握量化交易技能。',
          targetAudience: [
            '对量化交易感兴趣的初学者',
            '希望提升交易策略的投资者',
            '想要系统学习量化分析的金融从业者'
          ],
          chapters: [
            { title: '量化交易基础概念', duration: '15分钟', videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4' },
            { title: '策略回测方法论', duration: '20分钟', videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4' },
            { title: '均值回归策略详解', duration: '25分钟', videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4' },
            { title: '趋势跟踪策略实战', duration: '30分钟', videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4' },
            { title: '多因子模型构建', duration: '35分钟', videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4' },
            { title: '策略优化与调参', duration: '25分钟', videoUrl: 'https://www.w3schools.com/html/mov_bbb.mp4' }
          ],
          comments: [
            { username: '量化小白', avatar: '', time: '2023-06-15', content: '课程内容非常实用，讲解清晰易懂！' },
            { username: '投资达人', avatar: '', time: '2023-06-10', content: '学习了这个课程后，我的策略回测效果提升了不少。' }
          ]
        }
      } else {
        // 图文课程
        course.value = {
          id: courseId.value,
          title: 'MACD指标详解与应用',
          author: '量化学院',
          level: '入门',
          type: 'article',
          views: 1876,
          likes: 432,
          duration: '30分钟阅读',
          image: 'https://img.yzcdn.cn/vant/cat.jpeg',
          description: '本教程详细介绍MACD指标的原理、计算方法及在量化交易中的实际应用，帮助你掌握这一经典技术指标。',
          targetAudience: [
            '量化交易初学者',
            '对技术指标感兴趣的投资者',
            '希望深入了解MACD指标的交易者'
          ],
          content: `
            <h2>MACD指标介绍</h2>
            <p>MACD（Moving Average Convergence Divergence，移动平均线收敛散度）是由Gerald Appel于1979年提出的一种趋势跟踪指标，它通过计算两条不同周期的指数移动平均线（EMA）之间的差值，来判断市场趋势的强弱和可能的转折点。</p>
            
            <h2>MACD的计算方法</h2>
            <p>MACD由以下三个部分组成：</p>
            <ol>
              <li><strong>MACD线</strong>：快速EMA与慢速EMA的差值</li>
              <li><strong>信号线</strong>：MACD线的EMA</li>
              <li><strong>柱状图</strong>：MACD线与信号线的差值</li>
            </ol>
            <p>计算公式如下：</p>
            <ul>
              <li>MACD线 = 12日EMA - 26日EMA</li>
              <li>信号线 = 9日MACD线的EMA</li>
              <li>柱状图 = MACD线 - 信号线</li>
            </ul>
            
            <h2>MACD的交易信号</h2>
            <p>MACD主要产生以下几种交易信号：</p>
            <ol>
              <li><strong>金叉</strong>：MACD线从下向上穿越信号线，形成买入信号</li>
              <li><strong>死叉</strong>：MACD线从上向下穿越信号线，形成卖出信号</li>
              <li><strong>背离</strong>：当价格创新高而MACD未能创新高，或价格创新低而MACD未能创新低时，表明趋势可能即将反转</li>
              <li><strong>零轴交叉</strong>：MACD线穿越零轴，表明中期趋势的变化</li>
            </ol>
            
            <h2>MACD策略实例</h2>
            <p>以下是一个基于MACD的简单交易策略：</p>
            <pre>
            def macd_strategy(data):
                # 计算MACD
                data['ema12'] = data['close'].ewm(span=12, adjust=False).mean()
                data['ema26'] = data['close'].ewm(span=26, adjust=False).mean()
                data['macd'] = data['ema12'] - data['ema26']
                data['signal'] = data['macd'].ewm(span=9, adjust=False).mean()
                data['histogram'] = data['macd'] - data['signal']
                
                # 生成交易信号
                data['position'] = 0
                data.loc[data['macd'] > data['signal'], 'position'] = 1
                data.loc[data['macd'] < data['signal'], 'position'] = -1
                
                return data
            </pre>
            
            <h2>MACD的优缺点</h2>
            <p><strong>优点：</strong></p>
            <ul>
              <li>能够同时显示趋势方向、强度和动量</li>
              <li>信号明确，易于理解和使用</li>
              <li>可以应用于不同的时间周期和市场</li>
            </ul>
            <p><strong>缺点：</strong></p>
            <ul>
              <li>作为滞后指标，信号可能出现较晚</li>
              <li>在震荡市场中可能产生错误信号</li>
              <li>需要与其他指标或分析方法结合使用以提高准确性</li>
            </ul>
            
            <h2>结论</h2>
            <p>MACD是一种经典且实用的技术指标，适合用于识别市场趋势和潜在的转折点。通过理解其计算原理和应用方法，投资者可以将其有效地整合到量化交易策略中。</p>
          `,
          comments: [
            { username: '技术分析爱好者', avatar: '', time: '2023-05-20', content: '这篇教程把MACD讲解得非常透彻，对我帮助很大！' },
            { username: '量化新手', avatar: '', time: '2023-05-15', content: '终于理解了MACD的计算原理，感谢分享！' }
          ]
        }
      }
    }, 500)
  } catch (error) {
    console.error('获取课程详情失败:', error)
    showToast('获取课程详情失败')
  }
}

// 监听路由参数变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchCourseDetail()
  }
}, { immediate: true })

onMounted(() => {
  fetchCourseDetail()
})
</script>

<style scoped>
.course-detail {
  min-height: 100vh;
  background-color: var(--background-color);
  padding-bottom: calc(var(--tabbar-height) + var(--safe-area-inset-bottom));
}

.nav-bar {
  background-color: var(--background-color-light);
  box-shadow: var(--shadow-sm);
}

.course-container {
  padding-bottom: 60px; /* 为底部操作栏留出空间 */
}

.course-header {
  background-color: var(--background-color-light);
  margin-bottom: 12px;
}

.video-container {
  width: 100%;
  background-color: #000;
}

.course-video {
  width: 100%;
  max-height: 220px;
  display: block;
}

.image-container {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.course-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-info {
  padding: 16px;
}

.course-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.course-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.course-author {
  font-size: 14px;
  color: var(--text-color-regular);
}

.course-level, .course-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.level-basic {
  background-color: #e8f4ff;
  color: #1989fa;
}

.level-advanced {
  background-color: #f0f9eb;
  color: #67c23a;
}

.level-case {
  background-color: #fef0f0;
  color: #f56c6c;
}

.type-video {
  background-color: #f0f2ff;
  color: #5c6bc0;
}

.type-article {
  background-color: #fff7e6;
  color: #ff9800;
}

.course-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  color: var(--text-color-secondary);
  font-size: 13px;
}

.stat-item .van-icon {
  margin-right: 4px;
  font-size: 16px;
}

.course-content {
  background-color: var(--background-color-light);
}

.tab-content {
  padding: 16px;
}

.section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 12px 0;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4px;
  bottom: 4px;
  width: 4px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.section-text {
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-color-regular);
  margin: 0;
}

.target-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.target-list li {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-color-regular);
}

.target-list li .van-icon {
  margin-right: 8px;
}

.rich-text {
  font-size: 15px;
  line-height: 1.6;
  color: var(--text-color-regular);
}

.rich-text h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin: 24px 0 16px 0;
}

.rich-text h2:first-child {
  margin-top: 0;
}

.rich-text p {
  margin: 0 0 16px 0;
}

.rich-text ul, .rich-text ol {
  padding-left: 20px;
  margin: 0 0 16px 0;
}

.rich-text li {
  margin-bottom: 8px;
}

.rich-text pre {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.5;
  margin: 0 0 16px 0;
}

.chapter-list {
  margin-top: 8px;
}

.chapter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color-light);
  cursor: pointer;
}

.chapter-item:last-child {
  border-bottom: none;
}

.chapter-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.chapter-index {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f2f3f5;
  color: var(--text-color-regular);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 12px;
}

.chapter-detail {
  flex: 1;
}

.chapter-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin-bottom: 4px;
}

.chapter-duration {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.comment-list {
  margin-top: 8px;
}

.comment-item {
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color-light);
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 12px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin-bottom: 2px;
}

.comment-time {
  font-size: 12px;
  color: var(--text-color-secondary);
}

.comment-content {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color-regular);
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--background-color-light);
  padding: 8px 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.safe-area-inset-bottom {
  height: var(--safe-area-inset-bottom);
}
</style>
