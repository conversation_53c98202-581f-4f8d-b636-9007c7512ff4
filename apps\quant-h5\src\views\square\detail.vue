<template>
  <div class="backtest-detail">
    <!-- 导航栏 -->
    <van-nav-bar
      title="回测详情"
      left-text="返回"
      left-arrow
      @click-left="goBack"
      fixed
      placeholder
      class="nav-bar"
    />

    <ErrorBoundary>
      <LoadingContainer :loading="loading" text="加载回测详情...">
        <!-- 错误状态 -->
        <div v-if="!backtest" class="error-container">
          <van-empty description="未找到回测记录" image="search" />
          <van-button
            class="action-button"
            type="primary"
            round
            size="large"
            icon="arrow-left"
            @click="goBack"
          >
            返回广场
          </van-button>
        </div>

        <!-- 详情内容 -->
        <div v-else class="detail-content">
          <!-- 回测报告标题 -->
          <div class="report-title card">
            <div class="title-row">
              <h2>{{ backtest.strategy.name }}</h2>
              <div class="result-badge" :class="resultColor(backtest.result.finalReturn)">
                {{ backtest.result.finalReturn > 0 ? '+' : '' }}{{ backtest.result.finalReturn }}%
              </div>
            </div>
            <div class="report-meta">
              <div class="strategy-tags">
                <van-tag plain type="primary" size="small" class="tag">{{ getStrategyTypeText(backtest.strategy.type) }}</van-tag>
                <van-tag v-for="(market, index) in backtest.strategy.markets"
                        :key="index"
                        plain
                        type="success"
                        size="small"
                        class="tag">
                  {{ market }}
                </van-tag>
              </div>
              <div class="user-info">
                <span class="username">{{ backtest.userName }}</span>
                <span class="date">{{ formatDate(backtest.date) }}</span>
              </div>
            </div>
          </div>

          <!-- 收益曲线图卡片 -->
          <div class="chart-section card">
            <h3 class="section-title">
              收益曲线
            </h3>
            <div ref="chartContainer" class="performance-chart" />
          </div>

          <!-- 回测结果概览卡片 -->
          <div class="result-overview card">
            <h3 class="section-title">
              回测结果概览
            </h3>
            <div class="metrics-grid">
              <div class="metric-item">
                <div class="metric-value" :class="resultColor(backtest.result.finalReturn)">
                  {{ backtest.result.finalReturn > 0 ? '+' : '' }}{{ formatMetricValue(backtest.result.finalReturn) }}%
                </div>
                <div class="metric-label">
                  总收益率
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-value" :class="resultColor(backtest.result.annualReturn)">
                  {{ backtest.result.annualReturn > 0 ? '+' : '' }}{{ backtest.result.annualReturn }}%
                </div>
                <div class="metric-label">
                  年化收益
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-value negative">
                  {{ backtest.result.maxDrawdown }}%
                </div>
                <div class="metric-label">
                  最大回撤
                </div>
              </div>
              <div class="metric-item">
                <div class="metric-value">
                  {{ backtest.result.winRate }}%
                </div>
                <div class="metric-label">
                  胜率
                </div>
              </div>
            </div>
          </div>

          <!-- 回测参数卡片 -->
          <div class="parameters-section card">
            <h3 class="section-title">
              回测参数
            </h3>
            <div class="parameters-list">
              <div class="parameter-item" v-for="(param, index) in backtest.parameters" :key="index">
                <span class="parameter-label">{{ param.label }}</span>
                <span class="parameter-value">{{ param.value }}</span>
              </div>
            </div>
          </div>

          <!-- 作者描述卡片 -->
          <div v-if="backtest.description" class="description-section card">
            <h3 class="section-title">
              作者描述
            </h3>
            <p class="description-text">
              {{ backtest.description }}
            </p>
          </div>

          <!-- 交易记录卡片 -->
          <div class="trades-section card">
            <h3 class="section-title">
              交易记录
            </h3>
            <div class="trades-list">
              <van-empty v-if="trades.length === 0" description="暂无交易记录" />
              <div v-else class="trade-table">
                <div class="trade-header">
                  <span>日期</span>
                  <span>类型</span>
                  <span>价格</span>
                  <span>数量</span>
                </div>
                <div class="trade-row" v-for="(trade, index) in trades" :key="index">
                  <span>{{ formatTradeDate(trade.date) }}</span>
                  <span :class="trade.type === 'buy' ? 'buy' : 'sell'">
                    {{ trade.type === 'buy' ? '买入' : '卖出' }}
                  </span>
                  <span>{{ trade.price }}</span>
                  <span>{{ trade.quantity }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 评论区卡片 -->
          <div class="comments-section card">
            <div class="section-header">
              <h3 class="section-title">
                评论区
              </h3>
              <span class="comment-count">{{ comments.length }}条评论</span>
            </div>

            <!-- 评论输入框 -->
            <div class="comment-input">
              <van-field
                v-model="commentText"
                rows="2"
                autosize
                type="textarea"
                placeholder="写下你的评论..."
                maxlength="200"
                show-word-limit
              />
              <van-button
                type="primary"
                size="small"
                :disabled="!commentText.trim()"
                @click="submitComment"
              >
                发表
              </van-button>
            </div>

            <!-- 评论列表 -->
            <div class="comments-list">
              <van-empty v-if="comments.length === 0" description="暂无评论" />
              <div v-else>
                <div class="comment-item" v-for="comment in comments" :key="comment.id">
                  <div class="comment-user">
                    <span class="comment-username">{{ comment.userName }}</span>
                    <span class="comment-time">{{ formatDate(comment.createTime) }}</span>
                  </div>
                  <div class="comment-content">
                    {{ comment.content }}
                  </div>
                  <div class="comment-actions">
                    <div class="comment-like">
                      <van-icon name="like-o" size="14" />
                      <span>{{ comment.likes || 0 }}</span>
                    </div>
                    <div class="comment-reply" @click="showReplyInput(comment.id)">
                      <van-icon name="comment-o" size="14" />
                      <span>回复</span>
                    </div>
                  </div>

                  <!-- 回复列表 -->
                  <div class="replies-list" v-if="getReplies(comment.id).length > 0">
                    <div class="reply-item" v-for="reply in getReplies(comment.id)" :key="reply.id">
                      <div class="reply-user">
                        <span class="reply-username">{{ reply.userName }}</span>
                        <span v-if="reply.replyToName" class="reply-to">
                          回复 <span class="reply-to-name">{{ reply.replyToName }}</span>
                        </span>
                        <span class="reply-time">{{ formatDate(reply.createTime) }}</span>
                      </div>
                      <div class="reply-content">
                        {{ reply.content }}
                      </div>
                    </div>
                  </div>

                  <!-- 回复输入框 -->
                  <div v-if="replyToCommentId === comment.id" class="reply-input">
                    <van-field
                      v-model="replyText"
                      placeholder="回复评论..."
                      maxlength="100"
                      show-word-limit
                    />
                    <div class="reply-buttons">
                      <van-button
                        plain
                        type="default"
                        size="small"
                        @click="cancelReply"
                      >
                        取消
                      </van-button>
                      <van-button
                        type="primary"
                        size="small"
                        :disabled="!replyText.trim()"
                        @click="submitReply(comment.id)"
                      >
                        回复
                      </van-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <van-button
              class="action-button"
              type="primary"
              round
              block
              icon="replay"
              @click="replicateBacktest"
            >
              复现回测
            </van-button>
          </div>
        </div>
      </LoadingContainer>
    </ErrorBoundary>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount, inject, defineComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useBacktestStore } from '@/stores/backtest'
import { useStrategyStore } from '@/stores/strategy'
import { showToast, Empty, Button, NavBar, Icon, Tag, Field } from 'vant'
import * as echarts from 'echarts'
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import LoadingContainer from '@/components/LoadingContainer.vue'

// 使用完整的 echarts 包，无需注册组件

// 路由和存储
const route = useRoute()
const router = useRouter()
const backtestStore = useBacktestStore()
const strategyStore = useStrategyStore()
const globalLoading = inject('globalLoading', { show: () => {}, hide: () => {} })

// 组件状态
const loading = ref(true)
const backtest = ref(null)
const chartContainer = ref(null)
const trades = ref([])
const comments = ref([])
const commentText = ref('')
const replyText = ref('')
const replyToCommentId = ref(null)
let chart = null

// 策略类型文本映射
const strategyTypeMap = {
  'trend': '趋势跟踪',
  'mean_reversion': '均值回归',
  'momentum': '动量策略',
  'volatility': '波动率策略'
}

// 获取策略类型文本
const getStrategyTypeText = (type) => {
  return strategyTypeMap[type] || '其他策略'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''

  const d = new Date(date)
  const now = new Date()
  const diff = now - d

  // 如果小于24小时，显示"x小时前"
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return hours > 0 ? `${hours}小时前` : '刚刚'
  }

  // 如果小于30天，显示"x天前"
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }

  // 否则显示具体日期
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
}

// 格式化交易日期
const formatTradeDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
}

// 格式化指标值
const formatMetricValue = (value) => {
  return parseFloat(value).toFixed(2)
}

// 获取收益率的颜色类
const resultColor = (value) => {
  if (value > 0) return 'positive'
  if (value < 0) return 'negative'
  return ''
}

// 获取回测记录
const fetchBacktest = async () => {
  loading.value = true
  globalLoading.show()

  try {
    const id = parseInt(route.params.id)
    if (id) {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 800))

      // 尝试从回测历史中获取记录
      let record = backtestStore.getRecordById(id)

      // 如果在历史记录中找不到，则创建一个模拟记录
      if (!record) {
        console.log('未在历史记录中找到回测，创建模拟数据')

        // 确保策略数据已加载
        if (strategyStore.strategies.length === 0) {
          await strategyStore.fetchStrategies()
        }

        // 随机选择一个策略
        const strategies = strategyStore.strategies
        if (!strategies || strategies.length === 0) {
          throw new Error('没有可用的策略数据')
        }

        const strategy = strategies[Math.floor(Math.random() * strategies.length)]
        const finalReturn = (Math.random() * 50 - 10).toFixed(2)
        const annualReturn = (Math.random() * 40 - 5).toFixed(2)
        const maxDrawdown = (Math.random() * 30).toFixed(2)
        const winRate = (Math.random() * 30 + 50).toFixed(2)

        // 创建模拟回测记录
        record = {
          id: id,
          strategy: { ...strategy },
          asset: strategy.type === 'trend' ? { code: '000001', name: '上证指数' } :
                 [{ code: '000001', name: '上证指数' }, { code: '399001', name: '深证成指' }],
          parameters: strategy.parameters.map(p => ({
            label: p.label,
            value: p.default
          })),
          result: {
            finalReturn,
            annualReturn,
            maxDrawdown,
            winRate
          },
          date: new Date(),
          userName: '模拟用户',
          description: '这是一个模拟的回测记录，用于展示详情页面。'
        }

        // 将模拟记录添加到回测历史中
        backtestStore.addRecord(record)
      }

      backtest.value = record

      // 生成模拟交易记录数据
      generateTrades()

      // 获取评论数据
      fetchComments()
    }
  } catch (error) {
    console.error('获取回测记录失败:', error)
    showToast({
      type: 'fail',
      message: '加载数据失败',
      position: 'middle'
    })
    throw error
  } finally {
    loading.value = false
    globalLoading.hide()
  }
}

// 生成模拟交易记录
const generateTrades = () => {
  if (!backtest.value) return

  const tradeCount = Math.floor(Math.random() * 10) + 5 // 5-15笔交易
  const startDate = new Date('2023-01-01')
  const endDate = new Date('2023-12-31')
  const dateRange = endDate - startDate

  trades.value = []

  for (let i = 0; i < tradeCount; i++) {
    const randomDate = new Date(startDate.getTime() + Math.random() * dateRange)
    const trade = {
      id: i + 1,
      date: randomDate,
      type: Math.random() > 0.5 ? 'buy' : 'sell',
      price: (Math.random() * 100 + 50).toFixed(2),
      quantity: Math.floor(Math.random() * 1000) + 100
    }
    trades.value.push(trade)
  }

  // 按日期排序
  trades.value.sort((a, b) => a.date - b.date)
}

// 获取评论
const fetchComments = () => {
  if (!backtest.value) return

  comments.value = strategyStore.getComments(backtest.value.id) || []
}

// 获取回复
const getReplies = (commentId) => {
  return strategyStore.getReplies(commentId) || []
}

// 提交评论
const submitComment = () => {
  if (!commentText.value.trim() || !backtest.value) return

  const newComment = strategyStore.addComment(backtest.value.id, commentText.value.trim())
  comments.value = [newComment, ...comments.value]
  commentText.value = ''

  showToast({
    type: 'success',
    message: '评论成功',
    position: 'bottom'
  })
}

// 显示回复输入框
const showReplyInput = (commentId) => {
  replyToCommentId.value = commentId
  replyText.value = ''
}

// 取消回复
const cancelReply = () => {
  replyToCommentId.value = null
  replyText.value = ''
}

// 提交回复
const submitReply = (commentId) => {
  if (!replyText.value.trim()) return

  strategyStore.addReply(commentId, replyText.value.trim())
  replyText.value = ''
  replyToCommentId.value = null

  showToast({
    type: 'success',
    message: '回复成功',
    position: 'bottom'
  })
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value || !backtest.value) return

  if (chart) {
    chart.dispose()
    chart = null
  }

  // 创建一个包装元素，用于隔离事件传播
  chartWrapper = document.createElement('div')
  chartWrapper.style.width = '100%'
  chartWrapper.style.height = '100%'
  chartWrapper.style.position = 'relative'
  chartContainer.value.appendChild(chartWrapper)

  // 使用带有 passive 事件监听器的配置初始化 ECharts
  chart = echarts.init(chartWrapper, null, {
    renderer: 'canvas',
    useDirtyRect: true,
    useCoarsePointer: true, // 减少事件监听器数量
    devicePixelRatio: window.devicePixelRatio
  })

  // 手动添加被动事件监听器
  const passiveSupported = (() => {
    let passive = false
    try {
      const options = Object.defineProperty({}, 'passive', {
        get: function() {
          passive = true
          return true
        }
      })
      window.addEventListener('test', null, options)
      window.removeEventListener('test', null, options)
    } catch (err) {}
    return passive
  })()

  const passiveOptions = passiveSupported ? { passive: true } : false

  // 为图表容器添加被动事件监听器
  const touchEvents = ['touchstart', 'touchmove', 'touchend']
  const mouseEvents = ['mousewheel', 'wheel', 'mousedown', 'mousemove', 'mouseup']

  // 阻止事件冒泡到父元素
  const stopPropagation = (e) => {
    e.stopPropagation()
  }

  // 添加被动事件监听器并存储引用
  touchEvents.forEach(eventName => {
    chartWrapper.addEventListener(eventName, stopPropagation, passiveOptions)
    eventListeners.push({ element: chartWrapper, event: eventName, handler: stopPropagation, options: passiveOptions })
  })

  mouseEvents.forEach(eventName => {
    chartWrapper.addEventListener(eventName, stopPropagation, passiveOptions)
    eventListeners.push({ element: chartWrapper, event: eventName, handler: stopPropagation, options: passiveOptions })
  })

  // 生成模拟数据
  const days = 120
  const baseValue = 1000
  const values = [baseValue]
  let value = baseValue

  for (let i = 1; i < days; i++) {
    const change = (Math.random() - 0.5) * 20
    value = Math.max(0, value + change)
    values.push(value)
  }

  // 计算收益率数据
  const returns = values.map(v => ((v - baseValue) / baseValue) * 100)

  // 生成日期数据
  const dates = []
  const startDate = new Date('2023-01-01')
  for (let i = 0; i < days; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)
    dates.push(date.toISOString().split('T')[0])
  }

  // 设置图表选项
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const data = params[0]
        return `${data.axisValue}<br/>${data.marker}收益率: ${data.value.toFixed(2)}%`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        formatter: (value) => {
          return value.substring(5) // 只显示月-日
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '收益率',
        type: 'line',
        data: returns,
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: returns[returns.length - 1] >= 0 ? '#f56c6c' : '#4eb61b'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: returns[returns.length - 1] >= 0 ? 'rgba(245, 108, 108, 0.3)' : 'rgba(78, 182, 27, 0.3)'
            },
            {
              offset: 1,
              color: 'rgba(255, 255, 255, 0.1)'
            }
          ])
        }
      }
    ]
  }

  chart.setOption(option)
}

// 窗口大小变化时重新调整图表大小
const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 复现回测
const replicateBacktest = () => {
  if (!backtest.value) return

  router.push({
    path: '/testnow',
    query: { strategyId: backtest.value.strategy.id }
  })
}

onMounted(async () => {
  // 设置 ECharts 全局配置，使用被动事件监听器
  try {
    // 尝试设置全局配置，如果 echarts 支持的话
    if (echarts.setPassiveListeners) {
      echarts.setPassiveListeners(true)
    }
  } catch (e) {
    console.warn('ECharts 不支持设置被动事件监听器', e)
  }

  await fetchBacktest()

  if (backtest.value) {
    nextTick(() => {
      initChart()
    })
  }

  // 使用被动事件监听器添加 resize 事件
  window.addEventListener('resize', handleResize, { passive: true })
})

// 存储事件监听器引用，以便在组件卸载时清理
let chartWrapper = null
let eventListeners = []

onBeforeUnmount(() => {
  // 移除事件监听器时也需要指定相同的选项
  window.removeEventListener('resize', handleResize, { passive: true })

  // 清理图表事件监听器
  if (eventListeners.length > 0) {
    // 移除所有存储的事件监听器
    eventListeners.forEach(({ element, event, handler, options }) => {
      if (element) {
        element.removeEventListener(event, handler, options)
      }
    })
  }

  // 清理图表实例
  if (chart) {
    chart.dispose()
    chart = null
  }

  // 清理包装元素
  if (chartWrapper && chartWrapper.parentNode) {
    chartWrapper.parentNode.removeChild(chartWrapper)
    chartWrapper = null
  }

  // 清空事件监听器引用
  eventListeners = []
})
</script>

<style scoped>
/* 主要样式 */
.backtest-detail {
  min-height: 100vh;
  background-color: var(--background-color);
  padding-bottom: calc(var(--tabbar-height) + var(--safe-area-inset-bottom));
}

.nav-bar {
  background-color: var(--background-color-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
  gap: 16px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 12px;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  padding: var(--card-padding);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--card-margin);
}

.report-title {
  margin-bottom: 10px;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.title-row h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.result-badge {
  font-size: 16px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.report-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.strategy-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  margin-right: 4px;
}

.user-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #333;
}

.performance-chart {
  height: 200px;
  width: 100%;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
}

.metric-label {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.positive {
  color: #f56c6c;
}

.negative {
  color: #4eb61b;
}

.parameters-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.parameter-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.parameter-item:last-child {
  border-bottom: none;
}

.parameter-label {
  color: #666;
}

.parameter-value {
  font-weight: 500;
}

.description-text {
  margin: 0;
  line-height: 1.6;
  color: #666;
}

.trades-list {
  margin-top: 8px;
}

.trade-table {
  width: 100%;
  border-collapse: collapse;
}

.trade-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  font-weight: 500;
  color: #333;
}

.trade-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.trade-row:last-child {
  border-bottom: none;
}

.buy {
  color: #f56c6c;
}

.sell {
  color: #4eb61b;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.comment-count {
  font-size: 12px;
  color: #999;
}

.comment-input {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.comments-list {
  margin-top: 16px;
}

.comment-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-user {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.comment-username {
  font-weight: 500;
  color: #333;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-content {
  margin: 8px 0;
  line-height: 1.5;
}

.comment-actions {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.comment-like,
.comment-reply {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.replies-list {
  margin: 8px 0 0 16px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.reply-item {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-user {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.reply-username {
  font-weight: 500;
  color: #333;
}

.reply-to {
  font-size: 12px;
  color: #999;
}

.reply-to-name {
  color: #1989fa;
}

.reply-time {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

.reply-content {
  margin: 4px 0;
  line-height: 1.5;
}

.reply-input {
  margin-top: 8px;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.reply-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.action-buttons {
  margin: 16px 0 32px 0;
  padding: 0 16px;
}

.action-button {
  margin-bottom: 8px;
}
</style>
