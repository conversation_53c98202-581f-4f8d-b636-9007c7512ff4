from pydantic import BaseModel, ConfigDict, Field
from core.data_types import DatetimeStr, ObjectIdStr


class DictType(BaseModel):
    dict_name: str
    dict_type: str
    disabled: bool | None = False
    remark: str | None = None


class DictTypeSimpleOut(DictType):
    model_config = ConfigDict(from_attributes=True)

    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


class DictTypeOptionsOut(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    label: str = Field(alias='dict_name')
    value: int = Field(alias='id')
    disabled: bool


class DictDetails(BaseModel):
    label: str
    value: str
    disabled: bool | None = False
    is_default: bool | None = False
    remark: str | None = None
    order: int | None = None
    dict_type_id: int


class DictDetailsSimpleOut(DictDetails):
    model_config = ConfigDict(from_attributes=True)

    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


class Settings(BaseModel):
    config_label: str | None = None
    config_key: str
    config_value: str | None = None
    remark: str | None = None
    disabled: bool | None = None
    tab_id: int


class SettingsSimpleOut(Settings):
    model_config = ConfigDict(from_attributes=True)

    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


class SettingsTab(BaseModel):
    title: str
    classify: str
    tab_label: str
    tab_name: str
    hidden: bool


class SettingsTabSimpleOut(SettingsTab):
    model_config = ConfigDict(from_attributes=True)

    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


class Task(BaseModel):
    name: str
    group: str | None = None
    job_class: str
    exec_strategy: str
    expression: str
    is_active: bool | None = True  # 临时字段，不在表中创建
    remark: str | None = None
    start_date: DatetimeStr | None = None
    end_date: DatetimeStr | None = None


class TaskSimpleOut(Task):
    model_config = ConfigDict(from_attributes=True)

    id: ObjectIdStr = Field(..., alias='_id')
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr
    last_run_datetime: DatetimeStr | None = None  # 临时字段，不在表中创建
