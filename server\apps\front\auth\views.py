#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : 认证相关的视图

from datetime import timedelta
from redis.asyncio import Redis
from fastapi import APIRouter, Depends, Request, Body
from sqlalchemy.ext.asyncio import AsyncSession
from core.database import db_getter, redis_getter
from core.exception import CustomException
from utils import status
from utils.response import SuccessResponse, ErrorResponse
from config import settings
from .core.validation import LoginValidation
from .schemas import LoginForm, TokenData, SendCodeForm, RefreshToken
from .schemas.compute_power import ComputePowerRecharge, ComputePowerConsume, ComputePowerResponse
from apps.front.organ.crud import UserDal
from apps.front.organ.models import AppUser
from .core.current import AllUserAuth
from .schemas.auth import Auth
import jwt

app = APIRouter()


@app.post("/login", summary="用户登录", response_model=None)
async def login(
    request: Request,
    data: LoginForm,
    db: AsyncSession = Depends(db_getter),
    redis: Redis = Depends(redis_getter)
) -> ErrorResponse | SuccessResponse:
    """
    用户登录接口，支持多种登录方式：
    - password: 密码登录
    - code: 验证码登录
    - wx: 微信登录
    """
    try:
        # 验证登录数据
        @LoginValidation
        async def validate_login(self, data: LoginForm, user: AppUser, app, request: Request):
            result = LoginForm()

            if data.method == "password":
                # 密码登录验证
                if not data.password:
                    result.msg = "请输入密码"
                    return result
                if not user.password:
                    result.msg = "请先设置密码"
                    return result
                if not AppUser.verify_password(data.password, user.password):
                    result.msg = "手机号或密码错误"
                    return result

            elif data.method == "code":
                # 验证码登录验证
                if not data.code:
                    result.msg = "请输入验证码"
                    return result
                code_key = f"{data.telephone}_login_code"
                saved_code = await redis.get(code_key)
                if not saved_code or saved_code.decode() != data.code:
                    result.msg = "验证码错误或已过期"
                    return result
                await redis.delete(code_key)

            elif data.method == "wx":
                # 微信登录验证
                if not data.code:
                    result.msg = "请提供微信授权码"
                    return result
                # TODO: 实现微信登录逻辑
                result.msg = "微信登录暂未实现"
                return result

            result.status = True
            return result

        # 执行登录验证
        result = await validate_login(data=data, db=db, request=request)
        if not result.status:
            return ErrorResponse(msg=result.msg)

        # 生成token
        payload = {
            "sub": result.user.id,
            "app": data.app_id,
            "is_refresh": False
        }
        access_token = jwt.encode(
            payload,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )

        # 生成刷新token
        refresh_payload = {
            "sub": result.user.id,
            "app": data.app_id,
            "is_refresh": True
        }
        expires = timedelta(minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES)
        refresh_token = jwt.encode(
            refresh_payload,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )

        # 返回token
        token_data = TokenData(
            access_token=access_token,
            refresh_token=refresh_token
        )
        return SuccessResponse(data=token_data)

    except Exception as e:
        return ErrorResponse(msg=str(e))


@app.post("/send/code", summary="发送验证码", response_model=None)
async def send_code(
    data: SendCodeForm,
    redis: Redis = Depends(redis_getter),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """发送验证码"""
    # 检查应用是否存在
    from apps.front.organ.crud import AppDal
    app = await AppDal(db).get_app_full_info(data.app_id)

    # 根据验证码类型进行不同处理
    user_dal = UserDal(db)
    user = await user_dal.get_data(telephone=data.telephone, v_return_none=True)

    if data.type == "login":
        # 登录验证码：用户必须存在
        if not user:
            raise CustomException("该手机号未注册")
        if not app.settings.allow_code_login:
            raise CustomException("该应用未开启验证码登录")

    elif data.type == "register":
        # 注册验证码：用户不能存在
        if user:
            raise CustomException("该手机号已注册")

    elif data.type == "reset":
        # 重置密码验证码：用户必须存在
        if not user:
            raise CustomException("该手机号未注册")

    # 生成验证码
    from random import randint
    code = str(randint(100000, 999999))

    # 保存验证码到Redis
    code_key = f"{data.telephone}_{data.type}_code"
    await redis.set(code_key, code, ex=300)  # 5分钟有效期

    # TODO: 调用短信服务发送验证码
    # 开发环境直接返回验证码
    if settings.DEBUG:
        return SuccessResponse(data={"code": code})
    return SuccessResponse(msg="验证码已发送")


@app.post("/token/refresh", summary="刷新Token", response_model=None)
async def refresh_token(
    data: RefreshToken,
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """刷新Token"""
    error_code = status.HTTP_401_UNAUTHORIZED
    try:
        # 解析刷新token
        payload = jwt.decode(
            data.refresh_token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        user_id = payload.get("sub")
        app_id = payload.get("app")
        is_refresh = payload.get("is_refresh")

        if not user_id or not app_id or not is_refresh:
            raise CustomException("无效的刷新Token", code=error_code)

        if app_id != data.app_id:
            raise CustomException("应用ID不匹配", code=error_code)

        # 验证用户是否存在
        user = await UserDal(db).get_data(id=user_id, v_return_none=True)
        if not user:
            raise CustomException("用户不存在", code=error_code)

        # 生成新token
        new_payload = {
            "sub": user_id,
            "app": app_id,
            "is_refresh": False
        }
        access_token = jwt.encode(
            new_payload,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )

        # 生成新的刷新token
        refresh_payload = {
            "sub": user_id,
            "app": app_id,
            "is_refresh": True
        }
        refresh_token = jwt.encode(
            refresh_payload,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )

        # 返回新token
        token_data = TokenData(
            access_token=access_token,
            refresh_token=refresh_token
        )
        return SuccessResponse(data=token_data)

    except jwt.InvalidTokenError:
        raise CustomException("无效的Token", code=error_code)


@app.get("/user/info", summary="获取当前用户信息", response_model=None)
async def get_current_user_info(
    auth: Auth = Depends(AllUserAuth())
) -> SuccessResponse:
    """获取当前用户信息"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)
    return SuccessResponse(data=auth.user)


@app.post("/compute-power/recharge", summary="充值算力", response_model=None)
async def recharge_compute_power(
    data: ComputePowerRecharge,
    auth: Auth = Depends(AllUserAuth()),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """充值算力"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)

    # 更新用户算力
    user_dal = UserDal(db)
    user = await user_dal.get_data(auth.user.id)

    # 增加算力
    user.compute_power += data.amount
    await db.flush()

    return SuccessResponse(
        data=ComputePowerResponse(
            compute_power=user.compute_power,
            message=f"成功充值{data.amount}点算力"
        )
    )


@app.post("/compute-power/consume", summary="消费算力", response_model=None)
async def consume_compute_power(
    data: ComputePowerConsume,
    auth: Auth = Depends(AllUserAuth()),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """消费算力"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)

    # 更新用户算力
    user_dal = UserDal(db)
    user = await user_dal.get_data(auth.user.id)

    # 检查算力是否足够
    if user.compute_power < data.amount:
        raise CustomException("算力不足", code=status.HTTP_400_BAD_REQUEST)

    # 扣除算力
    user.compute_power -= data.amount
    await db.flush()

    return SuccessResponse(
        data=ComputePowerResponse(
            compute_power=user.compute_power,
            message=f"成功消费{data.amount}点算力"
        )
    )


@app.get("/compute-power", summary="获取当前算力", response_model=None)
async def get_compute_power(
    auth: Auth = Depends(AllUserAuth()),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """获取当前算力"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)

    # 获取用户算力
    user_dal = UserDal(db)
    user = await user_dal.get_data(auth.user.id)

    return SuccessResponse(
        data=ComputePowerResponse(
            compute_power=user.compute_power,
            message="获取算力成功"
        )
    )
