#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : 算力相关的视图

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from core.database import db_getter
from core.exception import CustomException
from utils import status
from utils.response import SuccessResponse
from apps.front.organ.crud import UserDal
from apps.front.auth.core.current import AllUserAuth
from apps.front.auth.schemas.auth import Auth
from apps.front.auth.schemas.compute_power import ComputePowerRecharge, ComputePowerConsume, ComputePowerResponse

app = APIRouter()


@app.post("/compute-power/recharge", summary="充值算力")
async def recharge_compute_power(
    data: ComputePowerRecharge,
    auth: Auth = Depends(AllUserAuth()),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """充值算力"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)
    
    # 更新用户算力
    user_dal = UserDal(db)
    user = await user_dal.get_data(auth.user.id)
    
    # 增加算力
    user.compute_power += data.amount
    await db.flush()
    
    return SuccessResponse(
        data=ComputePowerResponse(
            compute_power=user.compute_power,
            message=f"成功充值{data.amount}点算力"
        )
    )


@app.post("/compute-power/consume", summary="消费算力")
async def consume_compute_power(
    data: ComputePowerConsume,
    auth: Auth = Depends(AllUserAuth()),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """消费算力"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)
    
    # 更新用户算力
    user_dal = UserDal(db)
    user = await user_dal.get_data(auth.user.id)
    
    # 检查算力是否足够
    if user.compute_power < data.amount:
        raise CustomException("算力不足", code=status.HTTP_400_BAD_REQUEST)
    
    # 扣除算力
    user.compute_power -= data.amount
    await db.flush()
    
    return SuccessResponse(
        data=ComputePowerResponse(
            compute_power=user.compute_power,
            message=f"成功消费{data.amount}点算力"
        )
    )


@app.get("/compute-power", summary="获取当前算力")
async def get_compute_power(
    auth: Auth = Depends(AllUserAuth()),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """获取当前算力"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)
    
    # 获取用户算力
    user_dal = UserDal(db)
    user = await user_dal.get_data(auth.user.id)
    
    return SuccessResponse(
        data=ComputePowerResponse(
            compute_power=user.compute_power,
            message="获取算力成功"
        )
    )
