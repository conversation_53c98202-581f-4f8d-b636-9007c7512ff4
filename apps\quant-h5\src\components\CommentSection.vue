<template>
  <div class="comment-section">
    <!-- 评论列表 -->
    <div class="comment-list" v-if="comments.length > 0">
      <div
        v-for="comment in comments"
        :key="comment.id"
        class="comment-item"
      >
        <!-- 评论头部 -->
        <div class="comment-header">
          <div class="user-info">
            <img :src="comment.user.avatar" class="user-avatar" />
            <div class="user-meta">
              <div class="user-name">{{ comment.user.name }}</div>
              <div class="comment-time">{{ formatTime(comment.time) }}</div>
            </div>
          </div>
          <div class="comment-actions">
            <van-button
              size="mini"
              icon="like-o"
              :class="{ 'liked': comment.liked }"
              @click="toggleLike(comment)"
              :loading="likeLoading === comment.id"
              class="action-btn like-btn"
            >
              {{ comment.likes }}
            </van-button>
            <van-button
              size="mini"
              icon="chat-o"
              @click="showReplyInput(comment.id)"
              class="action-btn reply-btn"
            >
              回复
            </van-button>
          </div>
        </div>

        <!-- 评论内容 -->
        <div class="comment-content">
          {{ comment.content }}
        </div>

        <!-- 回复列表 -->
        <div class="reply-list" v-if="comment.replies && comment.replies.length > 0">
          <div
            v-for="reply in comment.replies"
            :key="reply.id"
            class="reply-item"
          >
            <div class="reply-header">
              <div class="user-info">
                <img :src="reply.user.avatar" class="user-avatar small" />
                <div class="user-meta">
                  <div class="user-name">{{ reply.user.name }}</div>
                  <div class="comment-time">{{ formatTime(reply.time) }}</div>
                </div>
              </div>
              <div class="reply-actions">
                <van-button
                  size="mini"
                  icon="like-o"
                  :class="{ 'liked': reply.liked }"
                  @click="toggleLike(reply, comment.id)"
                  :loading="likeLoading === reply.id"
                  class="action-btn like-btn"
                >
                  {{ reply.likes }}
                </van-button>
              </div>
            </div>
            <div class="reply-content">
              <template v-if="reply.replyTo">
                <span class="reply-to">@{{ reply.replyTo }}</span>
              </template>
              {{ reply.content }}
            </div>
          </div>
        </div>

        <!-- 回复输入框 -->
        <div class="reply-input-container" v-if="activeReplyId === comment.id">
          <van-field
            v-model="replyContent"
            placeholder="写下你的回复..."
            :border="false"
            :autosize="{ minHeight: 36, maxHeight: 100 }"
            class="reply-input"
          >
            <template #button>
              <van-button
                size="small"
                type="primary"
                @click="submitReply(comment.id)"
                :disabled="!replyContent.trim()"
                :loading="replyLoading"
                class="submit-btn"
                icon="passed"
              >
                发送
              </van-button>
            </template>
          </van-field>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-else>
      <van-empty description="暂无评论" />
    </div>

    <!-- 评论输入框 -->
    <div class="comment-input-container">
      <van-field
        v-model="commentContent"
        placeholder="写下你的评论..."
        :border="false"
        :autosize="{ minHeight: 36, maxHeight: 100 }"
        class="comment-input"
      >
        <template #button>
          <van-button
            size="small"
            type="primary"
            @click="submitComment"
            :disabled="!commentContent.trim()"
            :loading="commentLoading"
            class="submit-btn"
            icon="passed"
          >
            发送
          </van-button>
        </template>
      </van-field>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { showToast } from 'vant'

const props = defineProps({
  itemId: {
    type: [String, Number],
    required: true
  },
  itemType: {
    type: String,
    default: 'strategy'
  }
})

const emit = defineEmits(['update:comments'])

// 评论数据
const comments = ref([])
const commentContent = ref('')
const replyContent = ref('')
const activeReplyId = ref(null)

// 加载状态
const commentLoading = ref(false)
const replyLoading = ref(false)
const likeLoading = ref(null)

// 格式化时间
const formatTime = (timestamp) => {
  const now = new Date()
  const date = new Date(timestamp)
  const diff = now - date

  // 一分钟内
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 一小时内
  if (diff < 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 1000))}分钟前`
  }

  // 一天内
  if (diff < 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (60 * 60 * 1000))}小时前`
  }

  // 一周内
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`
  }

  // 其他情况显示具体日期
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 显示回复输入框
const showReplyInput = (commentId) => {
  activeReplyId.value = activeReplyId.value === commentId ? null : commentId
  replyContent.value = ''
}

// 提交评论
const submitComment = async () => {
  if (!commentContent.value.trim()) return

  commentLoading.value = true

  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 500))

    // 创建新评论
    const newComment = {
      id: Date.now(),
      content: commentContent.value,
      time: new Date().toISOString(),
      likes: 0,
      liked: false,
      user: {
        id: 1,
        name: '当前用户',
        avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
      },
      replies: []
    }

    // 添加到评论列表
    comments.value.unshift(newComment)
    emit('update:comments', comments.value)

    // 清空输入框
    commentContent.value = ''

    showToast({
      type: 'success',
      message: '评论成功',
      position: 'bottom'
    })
  } catch (error) {
    console.error('提交评论失败:', error)
    showToast({
      type: 'fail',
      message: '评论失败，请重试',
      position: 'bottom'
    })
  } finally {
    commentLoading.value = false
  }
}

// 提交回复
const submitReply = async (commentId) => {
  if (!replyContent.value.trim()) return

  replyLoading.value = true

  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 500))

    // 创建新回复
    const newReply = {
      id: Date.now(),
      content: replyContent.value,
      time: new Date().toISOString(),
      likes: 0,
      liked: false,
      user: {
        id: 1,
        name: '当前用户',
        avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
      }
    }

    // 添加到回复列表
    const commentIndex = comments.value.findIndex(c => c.id === commentId)
    if (commentIndex !== -1) {
      if (!comments.value[commentIndex].replies) {
        comments.value[commentIndex].replies = []
      }
      comments.value[commentIndex].replies.push(newReply)
      emit('update:comments', comments.value)
    }

    // 清空输入框并隐藏
    replyContent.value = ''
    activeReplyId.value = null

    showToast({
      type: 'success',
      message: '回复成功',
      position: 'bottom'
    })
  } catch (error) {
    console.error('提交回复失败:', error)
    showToast({
      type: 'fail',
      message: '回复失败，请重试',
      position: 'bottom'
    })
  } finally {
    replyLoading.value = false
  }
}

// 点赞/取消点赞
const toggleLike = async (item, parentId = null) => {
  likeLoading.value = item.id

  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 300))

    if (parentId) {
      // 回复点赞
      const commentIndex = comments.value.findIndex(c => c.id === parentId)
      if (commentIndex !== -1) {
        const replyIndex = comments.value[commentIndex].replies.findIndex(r => r.id === item.id)
        if (replyIndex !== -1) {
          const reply = comments.value[commentIndex].replies[replyIndex]
          reply.liked = !reply.liked
          reply.likes += reply.liked ? 1 : -1
        }
      }
    } else {
      // 评论点赞
      const commentIndex = comments.value.findIndex(c => c.id === item.id)
      if (commentIndex !== -1) {
        const comment = comments.value[commentIndex]
        comment.liked = !comment.liked
        comment.likes += comment.liked ? 1 : -1
      }
    }

    emit('update:comments', comments.value)
  } catch (error) {
    console.error('点赞操作失败:', error)
    showToast({
      type: 'fail',
      message: '操作失败，请重试',
      position: 'bottom'
    })
  } finally {
    likeLoading.value = null
  }
}

// 加载评论
const loadComments = async () => {
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟评论数据
    comments.value = [
      {
        id: 1,
        content: '这个策略在震荡市场表现很好，我用了三个月收益率达到15%',
        time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        likes: 12,
        liked: false,
        user: {
          id: 2,
          name: '投资达人',
          avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
        },
        replies: [
          {
            id: 3,
            content: '请问你用的什么参数设置？我最近也在尝试这个策略',
            time: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            likes: 3,
            liked: false,
            user: {
              id: 4,
              name: '量化小白',
              avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
            }
          },
          {
            id: 5,
            content: '我用的是默认参数，但把止损比例调低了一点，大概8%左右',
            time: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            likes: 5,
            liked: false,
            replyTo: '量化小白',
            user: {
              id: 2,
              name: '投资达人',
              avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
            }
          }
        ]
      },
      {
        id: 2,
        content: '这个策略在牛市表现很好，但熊市要小心使用',
        time: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        likes: 8,
        liked: false,
        user: {
          id: 6,
          name: '策略研究员',
          avatar: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'
        },
        replies: []
      }
    ]

    emit('update:comments', comments.value)
  } catch (error) {
    console.error('加载评论失败:', error)
    showToast({
      type: 'fail',
      message: '加载评论失败',
      position: 'middle'
    })
  }
}

// 初始化
onMounted(() => {
  loadComments()
})
</script>

<style scoped>
.comment-section {
  padding: var(--spacing-md) 0;
}

.comment-list {
  margin-bottom: var(--spacing-lg);
}

.comment-item {
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-color-light);
}

.comment-header,
.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: var(--spacing-sm);
}

.user-avatar.small {
  width: 24px;
  height: 24px;
}

.user-meta {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
}

.comment-time {
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  margin-top: 2px;
}

.comment-actions,
.reply-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.comment-content,
.reply-content {
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
  line-height: var(--line-height-normal);
  margin: var(--spacing-xs) 0 var(--spacing-sm);
  word-break: break-word;
}

.reply-list {
  margin-left: var(--spacing-lg);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--border-color-light);
  border-radius: var(--border-radius-md);
}

.reply-item {
  padding: var(--spacing-xs) 0;
}

.reply-item:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-xs);
}

.reply-to {
  color: var(--primary-color);
  margin-right: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.reply-input-container,
.comment-input-container {
  margin-top: var(--spacing-md);
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.reply-input-container {
  margin-top: var(--spacing-sm);
}

.empty-state {
  padding: var(--spacing-xl) 0;
  display: flex;
  justify-content: center;
}

:deep(.van-button--mini) {
  min-width: auto;
  padding: 0 var(--spacing-xs);
}

:deep(.liked) {
  color: var(--primary-color);
}

:deep(.van-field__control) {
  min-height: 36px;
}

.action-btn {
  background-color: var(--background-color-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: all 0.2s;
}

.action-btn:active {
  opacity: 0.8;
}

.like-btn.liked {
  background-color: rgba(238, 10, 36, 0.05);
  border-color: rgba(238, 10, 36, 0.2);
}

.reply-btn {
  background-color: rgba(25, 137, 250, 0.05);
  border-color: rgba(25, 137, 250, 0.2);
}

.submit-btn {
  border-radius: var(--border-radius-md);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.submit-btn .van-icon {
  font-size: 14px;
  margin-right: 2px;
}
</style>
