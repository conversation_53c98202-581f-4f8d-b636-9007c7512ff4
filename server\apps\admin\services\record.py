import random
from motor.motor_asyncio import AsyncIOMotorDatabase
from sqlalchemy.ext.asyncio import AsyncSession

from apps.admin.schemas.record import LoginRecordSimpleOut, SMSSendRecordSimpleOut, OperationRecordSimpleOut
from core.crud import DalBase
from core.mongo_manage import MongoManage
from models.admin.record.login import AdminLoginRecord
from models.admin.record.sms import AdminSMSSendRecord


class LoginRecordDal(DalBase):

    def __init__(self, db: AsyncSession):
        super(LoginRecordDal, self).__init__()
        self.db = db
        self.model = AdminLoginRecord
        self.schema = LoginRecordSimpleOut

    async def get_user_distribute(self) -> list[dict]:
        """
        获取用户登录分布情况
        高德经纬度查询：https://lbs.amap.com/tools/picker

        {
            name: '北京',
            center: [116.407394, 39.904211],
            total: 20
        }

        :return: List[dict]
        """
        result = [{
                    "name": '北京',
                    "center": [116.407394, 39.904211],
                },
                {
                    "name": '重庆',
                    "center": [106.551643, 29.562849],
                },
                {
                    "name": '郑州',
                    "center": [113.778584, 34.759197],
                },
                {
                    "name": '南京',
                    "center": [118.796624, 32.059344],
                },
                {
                    "name": '武汉',
                    "center": [114.304569, 30.593354],
                },
                {
                    "name": '乌鲁木齐',
                    "center": [87.616824, 43.825377],
                },
                {
                    "name": '新乡',
                    "center": [113.92679, 35.303589],
                }]
        for data in result:
            assert isinstance(data, dict)
            data["total"] = random.randint(2, 80)
        return result


class SMSSendRecordDal(DalBase):

    def __init__(self, db: AsyncSession):
        super(SMSSendRecordDal, self).__init__()
        self.db = db
        self.model = AdminSMSSendRecord
        self.schema = SMSSendRecordSimpleOut


class OperationRecordDal(MongoManage):

    def __init__(self, db: AsyncIOMotorDatabase):
        super(OperationRecordDal, self).__init__()
        self.db = db
        self.collection = db["operation_record"]
        self.schema = OperationRecordSimpleOut
        self.is_object_id = True
