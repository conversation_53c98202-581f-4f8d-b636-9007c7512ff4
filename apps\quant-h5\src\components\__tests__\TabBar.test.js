import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import App from '../../App.vue'

// Create a mock router
const createTestRouter = () => {
  return createRouter({
    history: createWebHistory(),
    routes: [
      {
        path: '/testnow',
        name: 'Backtest',
        component: { template: '<div>Backtest</div>' },
        meta: { title: '立即回测' }
      },
      {
        path: '/square',
        name: 'Strategy',
        component: { template: '<div>Strategy</div>' },
        meta: { title: '策略广场' }
      },
      {
        path: '/mine',
        name: 'User',
        component: { template: '<div>User</div>' },
        meta: { title: '个人中心' }
      }
    ]
  })
}

describe('App.vue TabBar', () => {
  let router
  let wrapper

  beforeEach(async () => {
    router = createTestRouter()
    
    // Mock Vant components
    const mockVantTabbar = {
      name: 'VanTabbar',
      template: '<div class="mock-tabbar"><slot /></div>'
    }
    
    const mockVantTabbarItem = {
      name: 'VanTabbarItem',
      template: '<div class="mock-tabbar-item"><slot /></div>',
      props: ['to', 'icon', 'replace']
    }
    
    wrapper = mount(App, {
      global: {
        plugins: [router],
        stubs: {
          'van-tabbar': mockVantTabbar,
          'van-tabbar-item': mockVantTabbarItem,
          'router-view': true,
          'keep-alive': true,
          'component': true
        }
      }
    })
    
    await router.push('/testnow')
    await router.isReady()
  })

  it('renders the tabbar with 3 items', () => {
    expect(wrapper.findAll('.mock-tabbar-item').length).toBe(3)
  })

  it('displays the correct tab labels', () => {
    const tabItems = wrapper.findAll('.mock-tabbar-item')
    expect(tabItems[0].text()).toContain('立即回测')
    expect(tabItems[1].text()).toContain('策略广场')
    expect(tabItems[2].text()).toContain('个人中心')
  })
})
