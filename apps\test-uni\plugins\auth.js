// 导入Vuex Store
import store from '@/store'

// 验证用户是否具备某个权限
function authPermission(permission) {
  const all_permission = '*:*:*' // 超级权限，表示拥有所有权限
  const permissions = store.getters && store.getters.permissions // 从Store中获取用户权限列表
  if (permission && permission.length > 0) {
    // 检查用户权限列表中是否包含目标权限或超级权限
    return permissions.some((v) => {
      return all_permission === v || v === permission
    })
  } else {
    return false // 如果未传入权限，直接返回false
  }
}

// 验证用户是否具备某个角色
function authRole(role) {
  const super_admin = 'admin' // 超级管理员角色
  const roles = store.getters && store.getters.roles // 从Store中获取用户角色列表
  if (role && role.length > 0) {
    // 检查用户角色列表中是否包含目标角色或超级管理员角色
    return roles.some((v) => {
      return super_admin === v || v === role
    })
  } else {
    return false // 如果未传入角色，直接返回false
  }
}

// 导出权限验证工具
export default {
  // 验证用户是否具备某个权限
  hasPermi(permission) {
    return authPermission(permission)
  },
  // 验证用户是否具备指定权限中的任意一个
  hasPermiOr(permissions) {
    return permissions.some((item) => {
      return authPermission(item)
    })
  },
  // 验证用户是否具备所有指定权限
  hasPermiAnd(permissions) {
    return permissions.every((item) => {
      return authPermission(item)
    })
  },
  // 验证用户是否具备某个角色
  hasRole(role) {
    return authRole(role)
  },
  // 验证用户是否具备指定角色中的任意一个
  hasRoleOr(roles) {
    return roles.some((item) => {
      return authRole(item)
    })
  },
  // 验证用户是否具备所有指定角色
  hasRoleAnd(roles) {
    return roles.every((item) => {
      return authRole(item)
    })
  }
}