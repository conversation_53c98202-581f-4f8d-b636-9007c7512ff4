export default {
  name: 'Ant Design Icons',
  prefix: 'ant-design',
  icons: [
    'ant-design:account-book-filled',
    'ant-design:account-book-outlined',
    'ant-design:account-book-twotone',
    'ant-design:aim-outlined',
    'ant-design:alert-filled',
    'ant-design:alert-outlined',
    'ant-design:alert-twotone',
    'ant-design:alibaba-outlined',
    'ant-design:align-center-outlined',
    'ant-design:align-left-outlined',
    'ant-design:align-right-outlined',
    'ant-design:alipay-circle-filled',
    'ant-design:alipay-circle-outlined',
    'ant-design:alipay-outlined',
    'ant-design:alipay-square-filled',
    'ant-design:aliwangwang-filled',
    'ant-design:aliwangwang-outlined',
    'ant-design:aliyun-outlined',
    'ant-design:amazon-circle-filled',
    'ant-design:amazon-outlined',
    'ant-design:amazon-square-filled',
    'ant-design:android-filled',
    'ant-design:android-outlined',
    'ant-design:ant-cloud-outlined',
    'ant-design:ant-design-outlined',
    'ant-design:apartment-outlined',
    'ant-design:api-filled',
    'ant-design:api-outlined',
    'ant-design:api-twotone',
    'ant-design:apple-filled',
    'ant-design:apple-outlined',
    'ant-design:appstore-add-outlined',
    'ant-design:appstore-filled',
    'ant-design:appstore-outlined',
    'ant-design:appstore-twotone',
    'ant-design:area-chart-outlined',
    'ant-design:arrow-down-outlined',
    'ant-design:arrow-left-outlined',
    'ant-design:arrow-right-outlined',
    'ant-design:arrow-up-outlined',
    'ant-design:arrows-alt-outlined',
    'ant-design:audio-filled',
    'ant-design:audio-muted-outlined',
    'ant-design:audio-outlined',
    'ant-design:audio-twotone',
    'ant-design:audit-outlined',
    'ant-design:backward-filled',
    'ant-design:backward-outlined',
    'ant-design:bank-filled',
    'ant-design:bank-outlined',
    'ant-design:bank-twotone',
    'ant-design:bar-chart-outlined',
    'ant-design:barcode-outlined',
    'ant-design:bars-outlined',
    'ant-design:behance-circle-filled',
    'ant-design:behance-outlined',
    'ant-design:behance-square-filled',
    'ant-design:behance-square-outlined',
    'ant-design:bell-filled',
    'ant-design:bell-outlined',
    'ant-design:bell-twotone',
    'ant-design:bg-colors-outlined',
    'ant-design:block-outlined',
    'ant-design:bold-outlined',
    'ant-design:book-filled',
    'ant-design:book-outlined',
    'ant-design:book-twotone',
    'ant-design:border-bottom-outlined',
    'ant-design:border-horizontal-outlined',
    'ant-design:border-inner-outlined',
    'ant-design:border-left-outlined',
    'ant-design:border-outer-outlined',
    'ant-design:border-outlined',
    'ant-design:border-right-outlined',
    'ant-design:border-top-outlined',
    'ant-design:border-verticle-outlined',
    'ant-design:borderless-table-outlined',
    'ant-design:box-plot-filled',
    'ant-design:box-plot-outlined',
    'ant-design:box-plot-twotone',
    'ant-design:branches-outlined',
    'ant-design:bug-filled',
    'ant-design:bug-outlined',
    'ant-design:bug-twotone',
    'ant-design:build-filled',
    'ant-design:build-outlined',
    'ant-design:build-twotone',
    'ant-design:bulb-filled',
    'ant-design:bulb-outlined',
    'ant-design:bulb-twotone',
    'ant-design:calculator-filled',
    'ant-design:calculator-outlined',
    'ant-design:calculator-twotone',
    'ant-design:calendar-filled',
    'ant-design:calendar-outlined',
    'ant-design:calendar-twotone',
    'ant-design:camera-filled',
    'ant-design:camera-outlined',
    'ant-design:camera-twotone',
    'ant-design:car-filled',
    'ant-design:car-outlined',
    'ant-design:car-twotone',
    'ant-design:caret-down-filled',
    'ant-design:caret-down-outlined',
    'ant-design:caret-left-filled',
    'ant-design:caret-left-outlined',
    'ant-design:caret-right-filled',
    'ant-design:caret-right-outlined',
    'ant-design:caret-up-filled',
    'ant-design:caret-up-outlined',
    'ant-design:carry-out-filled',
    'ant-design:carry-out-outlined',
    'ant-design:carry-out-twotone',
    'ant-design:check-circle-filled',
    'ant-design:check-circle-outlined',
    'ant-design:check-circle-twotone',
    'ant-design:check-outlined',
    'ant-design:check-square-filled',
    'ant-design:check-square-outlined',
    'ant-design:check-square-twotone',
    'ant-design:chrome-filled',
    'ant-design:chrome-outlined',
    'ant-design:ci-circle-filled',
    'ant-design:ci-circle-outlined',
    'ant-design:ci-circle-twotone',
    'ant-design:ci-outlined',
    'ant-design:ci-twotone',
    'ant-design:clear-outlined',
    'ant-design:clock-circle-filled',
    'ant-design:clock-circle-outlined',
    'ant-design:clock-circle-twotone',
    'ant-design:close-circle-filled',
    'ant-design:close-circle-outlined',
    'ant-design:close-circle-twotone',
    'ant-design:close-outlined',
    'ant-design:close-square-filled',
    'ant-design:close-square-outlined',
    'ant-design:close-square-twotone',
    'ant-design:cloud-download-outlined',
    'ant-design:cloud-filled',
    'ant-design:cloud-outlined',
    'ant-design:cloud-server-outlined',
    'ant-design:cloud-sync-outlined',
    'ant-design:cloud-twotone',
    'ant-design:cloud-upload-outlined',
    'ant-design:cluster-outlined',
    'ant-design:code-filled',
    'ant-design:code-outlined',
    'ant-design:code-sandbox-circle-filled',
    'ant-design:code-sandbox-outlined',
    'ant-design:code-sandbox-square-filled',
    'ant-design:code-twotone',
    'ant-design:codepen-circle-filled',
    'ant-design:codepen-circle-outlined',
    'ant-design:codepen-outlined',
    'ant-design:codepen-square-filled',
    'ant-design:coffee-outlined',
    'ant-design:column-height-outlined',
    'ant-design:column-width-outlined',
    'ant-design:comment-outlined',
    'ant-design:compass-filled',
    'ant-design:compass-outlined',
    'ant-design:compass-twotone',
    'ant-design:compress-outlined',
    'ant-design:console-sql-outlined',
    'ant-design:contacts-filled',
    'ant-design:contacts-outlined',
    'ant-design:contacts-twotone',
    'ant-design:container-filled',
    'ant-design:container-outlined',
    'ant-design:container-twotone',
    'ant-design:control-filled',
    'ant-design:control-outlined',
    'ant-design:control-twotone',
    'ant-design:copy-filled',
    'ant-design:copy-outlined',
    'ant-design:copy-twotone',
    'ant-design:copyright-circle-filled',
    'ant-design:copyright-circle-outlined',
    'ant-design:copyright-circle-twotone',
    'ant-design:copyright-outlined',
    'ant-design:copyright-twotone',
    'ant-design:credit-card-filled',
    'ant-design:credit-card-outlined',
    'ant-design:credit-card-twotone',
    'ant-design:crown-filled',
    'ant-design:crown-outlined',
    'ant-design:crown-twotone',
    'ant-design:customer-service-filled',
    'ant-design:customer-service-outlined',
    'ant-design:customer-service-twotone',
    'ant-design:dash-outlined',
    'ant-design:dashboard-filled',
    'ant-design:dashboard-outlined',
    'ant-design:dashboard-twotone',
    'ant-design:database-filled',
    'ant-design:database-outlined',
    'ant-design:database-twotone',
    'ant-design:delete-column-outlined',
    'ant-design:delete-filled',
    'ant-design:delete-outlined',
    'ant-design:delete-row-outlined',
    'ant-design:delete-twotone',
    'ant-design:delivered-procedure-outlined',
    'ant-design:deployment-unit-outlined',
    'ant-design:desktop-outlined',
    'ant-design:diff-filled',
    'ant-design:diff-outlined',
    'ant-design:diff-twotone',
    'ant-design:dingding-outlined',
    'ant-design:dingtalk-circle-filled',
    'ant-design:dingtalk-outlined',
    'ant-design:dingtalk-square-filled',
    'ant-design:disconnect-outlined',
    'ant-design:dislike-filled',
    'ant-design:dislike-outlined',
    'ant-design:dislike-twotone',
    'ant-design:dollar-circle-filled',
    'ant-design:dollar-circle-outlined',
    'ant-design:dollar-circle-twotone',
    'ant-design:dollar-outlined',
    'ant-design:dollar-twotone',
    'ant-design:dot-chart-outlined',
    'ant-design:double-left-outlined',
    'ant-design:double-right-outlined',
    'ant-design:down-circle-filled',
    'ant-design:down-circle-outlined',
    'ant-design:down-circle-twotone',
    'ant-design:down-outlined',
    'ant-design:down-square-filled',
    'ant-design:down-square-outlined',
    'ant-design:down-square-twotone',
    'ant-design:download-outlined',
    'ant-design:drag-outlined',
    'ant-design:dribbble-circle-filled',
    'ant-design:dribbble-outlined',
    'ant-design:dribbble-square-filled',
    'ant-design:dribbble-square-outlined',
    'ant-design:dropbox-circle-filled',
    'ant-design:dropbox-outlined',
    'ant-design:dropbox-square-filled',
    'ant-design:edit-filled',
    'ant-design:edit-outlined',
    'ant-design:edit-twotone',
    'ant-design:ellipsis-outlined',
    'ant-design:enter-outlined',
    'ant-design:environment-filled',
    'ant-design:environment-outlined',
    'ant-design:environment-twotone',
    'ant-design:euro-circle-filled',
    'ant-design:euro-circle-outlined',
    'ant-design:euro-circle-twotone',
    'ant-design:euro-outlined',
    'ant-design:euro-twotone',
    'ant-design:exception-outlined',
    'ant-design:exclamation-circle-filled',
    'ant-design:exclamation-circle-outlined',
    'ant-design:exclamation-circle-twotone',
    'ant-design:exclamation-outlined',
    'ant-design:expand-alt-outlined',
    'ant-design:expand-outlined',
    'ant-design:experiment-filled',
    'ant-design:experiment-outlined',
    'ant-design:experiment-twotone',
    'ant-design:export-outlined',
    'ant-design:eye-filled',
    'ant-design:eye-invisible-filled',
    'ant-design:eye-invisible-outlined',
    'ant-design:eye-invisible-twotone',
    'ant-design:eye-outlined',
    'ant-design:eye-twotone',
    'ant-design:facebook-filled',
    'ant-design:facebook-outlined',
    'ant-design:fall-outlined',
    'ant-design:fast-backward-filled',
    'ant-design:fast-backward-outlined',
    'ant-design:fast-forward-filled',
    'ant-design:fast-forward-outlined',
    'ant-design:field-binary-outlined',
    'ant-design:field-number-outlined',
    'ant-design:field-string-outlined',
    'ant-design:field-time-outlined',
    'ant-design:file-add-filled',
    'ant-design:file-add-outlined',
    'ant-design:file-add-twotone',
    'ant-design:file-done-outlined',
    'ant-design:file-excel-filled',
    'ant-design:file-excel-outlined',
    'ant-design:file-excel-twotone',
    'ant-design:file-exclamation-filled',
    'ant-design:file-exclamation-outlined',
    'ant-design:file-exclamation-twotone',
    'ant-design:file-filled',
    'ant-design:file-gif-outlined',
    'ant-design:file-image-filled',
    'ant-design:file-image-outlined',
    'ant-design:file-image-twotone',
    'ant-design:file-jpg-outlined',
    'ant-design:file-markdown-filled',
    'ant-design:file-markdown-outlined',
    'ant-design:file-markdown-twotone',
    'ant-design:file-outlined',
    'ant-design:file-pdf-filled',
    'ant-design:file-pdf-outlined',
    'ant-design:file-pdf-twotone',
    'ant-design:file-ppt-filled',
    'ant-design:file-ppt-outlined',
    'ant-design:file-ppt-twotone',
    'ant-design:file-protect-outlined',
    'ant-design:file-search-outlined',
    'ant-design:file-sync-outlined',
    'ant-design:file-text-filled',
    'ant-design:file-text-outlined',
    'ant-design:file-text-twotone',
    'ant-design:file-twotone',
    'ant-design:file-unknown-filled',
    'ant-design:file-unknown-outlined',
    'ant-design:file-unknown-twotone',
    'ant-design:file-word-filled',
    'ant-design:file-word-outlined',
    'ant-design:file-word-twotone',
    'ant-design:file-zip-filled',
    'ant-design:file-zip-outlined',
    'ant-design:file-zip-twotone',
    'ant-design:filter-filled',
    'ant-design:filter-outlined',
    'ant-design:filter-twotone',
    'ant-design:fire-filled',
    'ant-design:fire-outlined',
    'ant-design:fire-twotone',
    'ant-design:flag-filled',
    'ant-design:flag-outlined',
    'ant-design:flag-twotone',
    'ant-design:folder-add-filled',
    'ant-design:folder-add-outlined',
    'ant-design:folder-add-twotone',
    'ant-design:folder-filled',
    'ant-design:folder-open-filled',
    'ant-design:folder-open-outlined',
    'ant-design:folder-open-twotone',
    'ant-design:folder-outlined',
    'ant-design:folder-twotone',
    'ant-design:folder-view-outlined',
    'ant-design:font-colors-outlined',
    'ant-design:font-size-outlined',
    'ant-design:fork-outlined',
    'ant-design:form-outlined',
    'ant-design:format-painter-filled',
    'ant-design:format-painter-outlined',
    'ant-design:forward-filled',
    'ant-design:forward-outlined',
    'ant-design:frown-filled',
    'ant-design:frown-outlined',
    'ant-design:frown-twotone',
    'ant-design:fullscreen-exit-outlined',
    'ant-design:fullscreen-outlined',
    'ant-design:function-outlined',
    'ant-design:fund-filled',
    'ant-design:fund-outlined',
    'ant-design:fund-projection-screen-outlined',
    'ant-design:fund-twotone',
    'ant-design:fund-view-outlined',
    'ant-design:funnel-plot-filled',
    'ant-design:funnel-plot-outlined',
    'ant-design:funnel-plot-twotone',
    'ant-design:gateway-outlined',
    'ant-design:gif-outlined',
    'ant-design:gift-filled',
    'ant-design:gift-outlined',
    'ant-design:gift-twotone',
    'ant-design:github-filled',
    'ant-design:github-outlined',
    'ant-design:gitlab-filled',
    'ant-design:gitlab-outlined',
    'ant-design:global-outlined',
    'ant-design:gold-filled',
    'ant-design:gold-outlined',
    'ant-design:gold-twotone',
    'ant-design:golden-filled',
    'ant-design:google-circle-filled',
    'ant-design:google-outlined',
    'ant-design:google-plus-circle-filled',
    'ant-design:google-plus-outlined',
    'ant-design:google-plus-square-filled',
    'ant-design:google-square-filled',
    'ant-design:group-outlined',
    'ant-design:hdd-filled',
    'ant-design:hdd-outlined',
    'ant-design:hdd-twotone',
    'ant-design:heart-filled',
    'ant-design:heart-outlined',
    'ant-design:heart-twotone',
    'ant-design:heat-map-outlined',
    'ant-design:highlight-filled',
    'ant-design:highlight-outlined',
    'ant-design:highlight-twotone',
    'ant-design:history-outlined',
    'ant-design:holder-outlined',
    'ant-design:home-filled',
    'ant-design:home-outlined',
    'ant-design:home-twotone',
    'ant-design:hourglass-filled',
    'ant-design:hourglass-outlined',
    'ant-design:hourglass-twotone',
    'ant-design:html5-filled',
    'ant-design:html5-outlined',
    'ant-design:html5-twotone',
    'ant-design:idcard-filled',
    'ant-design:idcard-outlined',
    'ant-design:idcard-twotone',
    'ant-design:ie-circle-filled',
    'ant-design:ie-outlined',
    'ant-design:ie-square-filled',
    'ant-design:import-outlined',
    'ant-design:inbox-outlined',
    'ant-design:info-circle-filled',
    'ant-design:info-circle-outlined',
    'ant-design:info-circle-twotone',
    'ant-design:info-outlined',
    'ant-design:insert-row-above-outlined',
    'ant-design:insert-row-below-outlined',
    'ant-design:insert-row-left-outlined',
    'ant-design:insert-row-right-outlined',
    'ant-design:instagram-filled',
    'ant-design:instagram-outlined',
    'ant-design:insurance-filled',
    'ant-design:insurance-outlined',
    'ant-design:insurance-twotone',
    'ant-design:interaction-filled',
    'ant-design:interaction-outlined',
    'ant-design:interaction-twotone',
    'ant-design:issues-close-outlined',
    'ant-design:italic-outlined',
    'ant-design:key-outlined',
    'ant-design:laptop-outlined',
    'ant-design:layout-filled',
    'ant-design:layout-outlined',
    'ant-design:layout-twotone',
    'ant-design:left-circle-filled',
    'ant-design:left-circle-outlined',
    'ant-design:left-circle-twotone',
    'ant-design:left-outlined',
    'ant-design:left-square-filled',
    'ant-design:left-square-outlined',
    'ant-design:left-square-twotone',
    'ant-design:like-filled',
    'ant-design:like-outlined',
    'ant-design:like-twotone',
    'ant-design:line-chart-outlined',
    'ant-design:line-height-outlined',
    'ant-design:line-outlined',
    'ant-design:link-outlined',
    'ant-design:linkedin-filled',
    'ant-design:linkedin-outlined',
    'ant-design:loading-3-quarters-outlined',
    'ant-design:loading-outlined',
    'ant-design:lock-filled',
    'ant-design:lock-outlined',
    'ant-design:lock-twotone',
    'ant-design:login-outlined',
    'ant-design:logout-outlined',
    'ant-design:mac-command-filled',
    'ant-design:mac-command-outlined',
    'ant-design:mail-filled',
    'ant-design:mail-outlined',
    'ant-design:mail-twotone',
    'ant-design:man-outlined',
    'ant-design:medicine-box-filled',
    'ant-design:medicine-box-outlined',
    'ant-design:medicine-box-twotone',
    'ant-design:medium-circle-filled',
    'ant-design:medium-outlined',
    'ant-design:medium-square-filled',
    'ant-design:medium-workmark-outlined',
    'ant-design:meh-filled',
    'ant-design:meh-outlined',
    'ant-design:meh-twotone',
    'ant-design:menu-fold-outlined',
    'ant-design:menu-outlined',
    'ant-design:menu-unfold-outlined',
    'ant-design:merge-cells-outlined',
    'ant-design:message-filled',
    'ant-design:message-outlined',
    'ant-design:message-twotone',
    'ant-design:minus-circle-filled',
    'ant-design:minus-circle-outlined',
    'ant-design:minus-circle-twotone',
    'ant-design:minus-outlined',
    'ant-design:minus-square-filled',
    'ant-design:minus-square-outlined',
    'ant-design:minus-square-twotone',
    'ant-design:mobile-filled',
    'ant-design:mobile-outlined',
    'ant-design:mobile-twotone',
    'ant-design:money-collect-filled',
    'ant-design:money-collect-outlined',
    'ant-design:money-collect-twotone',
    'ant-design:monitor-outlined',
    'ant-design:more-outlined',
    'ant-design:node-collapse-outlined',
    'ant-design:node-expand-outlined',
    'ant-design:node-index-outlined',
    'ant-design:notification-filled',
    'ant-design:notification-outlined',
    'ant-design:notification-twotone',
    'ant-design:number-outlined',
    'ant-design:one-to-one-outlined',
    'ant-design:ordered-list-outlined',
    'ant-design:paper-clip-outlined',
    'ant-design:partition-outlined',
    'ant-design:pause-circle-filled',
    'ant-design:pause-circle-outlined',
    'ant-design:pause-circle-twotone',
    'ant-design:pause-outlined',
    'ant-design:pay-circle-filled',
    'ant-design:pay-circle-outlined',
    'ant-design:percentage-outlined',
    'ant-design:phone-filled',
    'ant-design:phone-outlined',
    'ant-design:phone-twotone',
    'ant-design:pic-center-outlined',
    'ant-design:pic-left-outlined',
    'ant-design:pic-right-outlined',
    'ant-design:picture-filled',
    'ant-design:picture-outlined',
    'ant-design:picture-twotone',
    'ant-design:pie-chart-filled',
    'ant-design:pie-chart-outlined',
    'ant-design:pie-chart-twotone',
    'ant-design:play-circle-filled',
    'ant-design:play-circle-outlined',
    'ant-design:play-circle-twotone',
    'ant-design:play-square-filled',
    'ant-design:play-square-outlined',
    'ant-design:play-square-twotone',
    'ant-design:plus-circle-filled',
    'ant-design:plus-circle-outlined',
    'ant-design:plus-circle-twotone',
    'ant-design:plus-outlined',
    'ant-design:plus-square-filled',
    'ant-design:plus-square-outlined',
    'ant-design:plus-square-twotone',
    'ant-design:pound-circle-filled',
    'ant-design:pound-circle-outlined',
    'ant-design:pound-circle-twotone',
    'ant-design:pound-outlined',
    'ant-design:poweroff-outlined',
    'ant-design:printer-filled',
    'ant-design:printer-outlined',
    'ant-design:printer-twotone',
    'ant-design:profile-filled',
    'ant-design:profile-outlined',
    'ant-design:profile-twotone',
    'ant-design:project-filled',
    'ant-design:project-outlined',
    'ant-design:project-twotone',
    'ant-design:property-safety-filled',
    'ant-design:property-safety-outlined',
    'ant-design:property-safety-twotone',
    'ant-design:pull-request-outlined',
    'ant-design:pushpin-filled',
    'ant-design:pushpin-outlined',
    'ant-design:pushpin-twotone',
    'ant-design:qq-circle-filled',
    'ant-design:qq-outlined',
    'ant-design:qq-square-filled',
    'ant-design:qrcode-outlined',
    'ant-design:question-circle-filled',
    'ant-design:question-circle-outlined',
    'ant-design:question-circle-twotone',
    'ant-design:question-outlined',
    'ant-design:radar-chart-outlined',
    'ant-design:radius-bottomleft-outlined',
    'ant-design:radius-bottomright-outlined',
    'ant-design:radius-setting-outlined',
    'ant-design:radius-upleft-outlined',
    'ant-design:radius-upright-outlined',
    'ant-design:read-filled',
    'ant-design:read-outlined',
    'ant-design:reconciliation-filled',
    'ant-design:reconciliation-outlined',
    'ant-design:reconciliation-twotone',
    'ant-design:red-envelope-filled',
    'ant-design:red-envelope-outlined',
    'ant-design:red-envelope-twotone',
    'ant-design:reddit-circle-filled',
    'ant-design:reddit-outlined',
    'ant-design:reddit-square-filled',
    'ant-design:redo-outlined',
    'ant-design:reload-outlined',
    'ant-design:rest-filled',
    'ant-design:rest-outlined',
    'ant-design:rest-twotone',
    'ant-design:retweet-outlined',
    'ant-design:right-circle-filled',
    'ant-design:right-circle-outlined',
    'ant-design:right-circle-twotone',
    'ant-design:right-outlined',
    'ant-design:right-square-filled',
    'ant-design:right-square-outlined',
    'ant-design:right-square-twotone',
    'ant-design:rise-outlined',
    'ant-design:robot-filled',
    'ant-design:robot-outlined',
    'ant-design:rocket-filled',
    'ant-design:rocket-outlined',
    'ant-design:rocket-twotone',
    'ant-design:rollback-outlined',
    'ant-design:rotate-left-outlined',
    'ant-design:rotate-right-outlined',
    'ant-design:safety-certificate-filled',
    'ant-design:safety-certificate-outlined',
    'ant-design:safety-certificate-twotone',
    'ant-design:safety-outlined',
    'ant-design:save-filled',
    'ant-design:save-outlined',
    'ant-design:save-twotone',
    'ant-design:scan-outlined',
    'ant-design:schedule-filled',
    'ant-design:schedule-outlined',
    'ant-design:schedule-twotone',
    'ant-design:scissor-outlined',
    'ant-design:search-outlined',
    'ant-design:security-scan-filled',
    'ant-design:security-scan-outlined',
    'ant-design:security-scan-twotone',
    'ant-design:select-outlined',
    'ant-design:send-outlined',
    'ant-design:setting-filled',
    'ant-design:setting-outlined',
    'ant-design:setting-twotone',
    'ant-design:shake-outlined',
    'ant-design:share-alt-outlined',
    'ant-design:shop-filled',
    'ant-design:shop-outlined',
    'ant-design:shop-twotone',
    'ant-design:shopping-cart-outlined',
    'ant-design:shopping-filled',
    'ant-design:shopping-outlined',
    'ant-design:shopping-twotone',
    'ant-design:shrink-outlined',
    'ant-design:signal-filled',
    'ant-design:sisternode-outlined',
    'ant-design:sketch-circle-filled',
    'ant-design:sketch-outlined',
    'ant-design:sketch-square-filled',
    'ant-design:skin-filled',
    'ant-design:skin-outlined',
    'ant-design:skin-twotone',
    'ant-design:skype-filled',
    'ant-design:skype-outlined',
    'ant-design:slack-circle-filled',
    'ant-design:slack-outlined',
    'ant-design:slack-square-filled',
    'ant-design:slack-square-outlined',
    'ant-design:sliders-filled',
    'ant-design:sliders-outlined',
    'ant-design:sliders-twotone',
    'ant-design:small-dash-outlined',
    'ant-design:smile-filled',
    'ant-design:smile-outlined',
    'ant-design:smile-twotone',
    'ant-design:snippets-filled',
    'ant-design:snippets-outlined',
    'ant-design:snippets-twotone',
    'ant-design:solution-outlined',
    'ant-design:sort-ascending-outlined',
    'ant-design:sort-descending-outlined',
    'ant-design:sound-filled',
    'ant-design:sound-outlined',
    'ant-design:sound-twotone',
    'ant-design:split-cells-outlined',
    'ant-design:star-filled',
    'ant-design:star-outlined',
    'ant-design:star-twotone',
    'ant-design:step-backward-filled',
    'ant-design:step-backward-outlined',
    'ant-design:step-forward-filled',
    'ant-design:step-forward-outlined',
    'ant-design:stock-outlined',
    'ant-design:stop-filled',
    'ant-design:stop-outlined',
    'ant-design:stop-twotone',
    'ant-design:strikethrough-outlined',
    'ant-design:subnode-outlined',
    'ant-design:swap-left-outlined',
    'ant-design:swap-outlined',
    'ant-design:swap-right-outlined',
    'ant-design:switcher-filled',
    'ant-design:switcher-outlined',
    'ant-design:switcher-twotone',
    'ant-design:sync-outlined',
    'ant-design:table-outlined',
    'ant-design:tablet-filled',
    'ant-design:tablet-outlined',
    'ant-design:tablet-twotone',
    'ant-design:tag-filled',
    'ant-design:tag-outlined',
    'ant-design:tag-twotone',
    'ant-design:tags-filled',
    'ant-design:tags-outlined',
    'ant-design:tags-twotone',
    'ant-design:taobao-circle-filled',
    'ant-design:taobao-circle-outlined',
    'ant-design:taobao-outlined',
    'ant-design:taobao-square-filled',
    'ant-design:team-outlined',
    'ant-design:thunderbolt-filled',
    'ant-design:thunderbolt-outlined',
    'ant-design:thunderbolt-twotone',
    'ant-design:to-top-outlined',
    'ant-design:tool-filled',
    'ant-design:tool-outlined',
    'ant-design:tool-twotone',
    'ant-design:trademark-circle-filled',
    'ant-design:trademark-circle-outlined',
    'ant-design:trademark-circle-twotone',
    'ant-design:trademark-outlined',
    'ant-design:transaction-outlined',
    'ant-design:translation-outlined',
    'ant-design:trophy-filled',
    'ant-design:trophy-outlined',
    'ant-design:trophy-twotone',
    'ant-design:twitter-circle-filled',
    'ant-design:twitter-outlined',
    'ant-design:twitter-square-filled',
    'ant-design:underline-outlined',
    'ant-design:undo-outlined',
    'ant-design:ungroup-outlined',
    'ant-design:unlock-filled',
    'ant-design:unlock-outlined',
    'ant-design:unlock-twotone',
    'ant-design:unordered-list-outlined',
    'ant-design:up-circle-filled',
    'ant-design:up-circle-outlined',
    'ant-design:up-circle-twotone',
    'ant-design:up-outlined',
    'ant-design:up-square-filled',
    'ant-design:up-square-outlined',
    'ant-design:up-square-twotone',
    'ant-design:upload-outlined',
    'ant-design:usb-filled',
    'ant-design:usb-outlined',
    'ant-design:usb-twotone',
    'ant-design:user-add-outlined',
    'ant-design:user-delete-outlined',
    'ant-design:user-outlined',
    'ant-design:user-switch-outlined',
    'ant-design:usergroup-add-outlined',
    'ant-design:usergroup-delete-outlined',
    'ant-design:verified-outlined',
    'ant-design:vertical-align-bottom-outlined',
    'ant-design:vertical-align-middle-outlined',
    'ant-design:vertical-align-top-outlined',
    'ant-design:vertical-left-outlined',
    'ant-design:vertical-right-outlined',
    'ant-design:video-camera-add-outlined',
    'ant-design:video-camera-filled',
    'ant-design:video-camera-outlined',
    'ant-design:video-camera-twotone',
    'ant-design:wallet-filled',
    'ant-design:wallet-outlined',
    'ant-design:wallet-twotone',
    'ant-design:warning-filled',
    'ant-design:warning-outlined',
    'ant-design:warning-twotone',
    'ant-design:wechat-filled',
    'ant-design:wechat-outlined',
    'ant-design:weibo-circle-filled',
    'ant-design:weibo-circle-outlined',
    'ant-design:weibo-outlined',
    'ant-design:weibo-square-filled',
    'ant-design:weibo-square-outlined',
    'ant-design:whats-app-outlined',
    'ant-design:wifi-outlined',
    'ant-design:windows-filled',
    'ant-design:windows-outlined',
    'ant-design:woman-outlined',
    'ant-design:yahoo-filled',
    'ant-design:yahoo-outlined',
    'ant-design:youtube-filled',
    'ant-design:youtube-outlined',
    'ant-design:yuque-filled',
    'ant-design:yuque-outlined',
    'ant-design:zhihu-circle-filled',
    'ant-design:zhihu-outlined',
    'ant-design:zhihu-square-filled',
    'ant-design:zoom-in-outlined',
    'ant-design:zoom-out-outlined'
  ]
}
