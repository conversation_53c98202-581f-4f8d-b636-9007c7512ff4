import datetime
import os.path
from config.settings import BASE_DIR


class CreateApp:
    """
    应用创建工具类
    
    功能：
    1. 自动创建应用目录结构
    2. 生成基础代码文件(views.py, crud.py等)
    3. 自动生成初始化文件
    
    目录结构示例：
    apps/
      [app_name]/
        __init__.py
        models/       # 数据模型目录
        params/       # 请求参数目录
        schemas/      # 响应模型目录
        views.py      # 视图路由文件
        crud.py       # 数据库操作文件
        
    使用示例：
    creator = CreateApp("admin/auth")  # 创建apps/admin/auth应用
    creator.run()
    """

    # 应用根目录路径
    APPS_ROOT = os.path.join(BASE_DIR, "apps")
    # 脚本目录路径
    SCRIPT_DIR = os.path.join(BASE_DIR, 'scripts', 'create_app')

    def __init__(self, path: str):
        """
        初始化应用创建器
        
        参数:
            path (str): 应用相对路径，基于APPS_ROOT目录
                       例如: "admin/auth" 会创建 apps/admin/auth 目录
            
        注意:
            - 路径使用"/"分隔，不要使用反斜杠
            - 路径不应以"/"开头或结尾
        """
        self.app_path = os.path.join(self.APPS_ROOT, path)
        self.path = path

    def run(self) -> bool:
        """
        执行应用创建流程
        
        步骤:
        1. 检查目标路径是否已存在
        2. 创建应用目录结构
        3. 生成基础代码文件
        
        返回:
            bool: 是否成功创建应用目录
            
        异常:
            OSError: 当目录创建失败时抛出
        """
        if self.exist(self.app_path):
            print(f"{self.app_path} 已经存在，无法自动创建，请删除后，重新执行。")
            return False
        print("开始生成 App 目录：", self.path)
        path = []
        for item in self.path.split("/"):
            path.append(item)
            self.create_pag(os.path.join(self.APPS_ROOT, *path))
        self.create_pag(os.path.join(self.app_path, "models"))
        self.create_pag(os.path.join(self.app_path, "params"))
        self.create_pag(os.path.join(self.app_path, "schemas"))
        self.generate_file("views.py")
        self.generate_file("crud.py")
        print("App 目录生成结束", self.app_path)

    def create_pag(self, path: str) -> None:
        """
        创建Python包目录结构
        
        参数:
            path (str): 要创建的目录绝对路径
            
        功能:
            1. 创建目录
            2. 生成__init__.py文件
            
        注意:
            - 如果目录已存在则跳过
            - 会自动创建多级目录
        """
        if self.exist(path):
            return
        os.makedirs(path)
        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        params = {
            "create_datetime": now,
            "filename": "__init__.py",
            "desc": "初始化文件"
        }
        self.create_file(os.path.join(path, "__init__.py"), "init.py", **params)

    def generate_file(self, name: str) -> None:
        """
        生成基础代码文件
        
        参数:
            name (str): 要生成的文件名(views.py/crud.py)
            
        功能:
            根据模板文件生成对应的代码文件
            
        模板位置:
            scripts/create_app/template/[name]
        """
        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        params = {
            "create_datetime": now,
        }
        self.create_file(os.path.join(self.app_path, name), name, **params)

    def create_file(self, filepath: str, name: str, **kwargs) -> None:
        """
        根据模板创建文件
        
        参数:
            filepath (str): 目标文件路径
            name (str): 模板文件名
            **kwargs: 模板变量参数
            
        功能:
            1. 读取模板文件内容
            2. 渲染模板变量
            3. 写入目标文件
            
        注意:
            会覆盖已存在的文件
        """
        with open(filepath, "w", encoding="utf-8") as f:
            content = self.__get_template(name)
            f.write(content.format(**kwargs))

    @classmethod
    def exist(cls, path: str) -> bool:
        """
        检查路径是否存在
        
        参数:
            path (str): 要检查的路径
            
        返回:
            bool: 路径是否存在
            
        注意:
            可以检查文件或目录
        """
        return os.path.exists(path)

    def __get_template(self, name: str) -> str:
        """
        读取模板文件内容
        
        参数:
            name (str): 模板文件名
            
        返回:
            str: 模板文件内容
            
        异常:
            FileNotFoundError: 当模板文件不存在时抛出
        """
        template = open(os.path.join(self.SCRIPT_DIR, "template", name), 'r')
        content = template.read()
        template.close()
        return content

