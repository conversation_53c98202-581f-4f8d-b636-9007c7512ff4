<template>
  <div class="learn-center">
    <van-nav-bar
      title="学习课堂"
      fixed
      placeholder
      class="nav-bar"
    />

    <!-- 课程类型筛选 -->
    <div class="filter-section">
      <div class="filter-title">课程类型</div>
      <div class="filter-options">
        <div
          class="filter-option"
          :class="{ active: courseType === 'all' }"
          @click="courseType = 'all'"
        >
          全部
        </div>
        <div
          class="filter-option"
          :class="{ active: courseType === 'video' }"
          @click="courseType = 'video'"
        >
          <van-icon name="play-circle-o" />
          视频课程
        </div>
        <div
          class="filter-option"
          :class="{ active: courseType === 'article' }"
          @click="courseType = 'article'"
        >
          <van-icon name="description" />
          图文教程
        </div>
      </div>
    </div>

    <!-- 课程分类 -->
    <van-tabs
      v-model="activeTab"
      sticky
      animated
      swipeable
      color="var(--primary-color)"
      title-active-color="var(--primary-color)"
      title-inactive-color="var(--text-color-regular)"
      class="content-tabs"
    >
      <van-tab title="量化入门" name="basic">
        <CourseList :courses="filteredBasicCourses" />
      </van-tab>
      <van-tab title="策略进阶" name="advanced">
        <CourseList :courses="filteredAdvancedCourses" />
      </van-tab>
      <van-tab title="实战案例" name="cases">
        <CourseList :courses="filteredCaseCourses" />
      </van-tab>
    </van-tabs>

    <!-- 底部安全区域 -->
    <div class="safe-area-inset-bottom"></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, inject } from 'vue'
import CourseList from './components/CourseList.vue'
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import LoadingContainer from '@/components/LoadingContainer.vue'

// 活动标签页
const activeTab = ref('basic')
const loading = ref(true)
const courseType = ref('all') // 课程类型筛选：all, video, article
const globalLoading = inject('globalLoading', { show: () => {}, hide: () => {} })

// 根据课程类型筛选课程
const filteredBasicCourses = computed(() => {
  if (courseType.value === 'all') {
    return basicCourses.value
  }
  return basicCourses.value.filter(course => course.type === courseType.value)
})

const filteredAdvancedCourses = computed(() => {
  if (courseType.value === 'all') {
    return advancedCourses.value
  }
  return advancedCourses.value.filter(course => course.type === courseType.value)
})

const filteredCaseCourses = computed(() => {
  if (courseType.value === 'all') {
    return caseCourses.value
  }
  return caseCourses.value.filter(course => course.type === courseType.value)
})

// 模拟课程数据
const basicCourses = ref([
  {
    id: 1,
    title: '量化交易基础概念',
    author: '量化学院',
    duration: '30分钟阅读',
    level: '入门',
    type: 'article',
    views: 1256,
    likes: 328,
    image: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: 2,
    title: '技术指标入门',
    author: '量化学院',
    duration: '45分钟',
    level: '入门',
    type: 'video',
    views: 986,
    likes: 256,
    image: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: 3,
    title: '回测系统使用指南',
    author: '量化学院',
    duration: '20分钟阅读',
    level: '入门',
    type: 'article',
    views: 1542,
    likes: 412,
    image: 'https://img.yzcdn.cn/vant/cat.jpeg'
  }
])

const advancedCourses = ref([
  {
    id: 4,
    title: '趋势跟踪策略详解',
    author: '量化学院',
    duration: '60分钟',
    level: '进阶',
    type: 'video',
    views: 876,
    likes: 231,
    image: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: 5,
    title: '均值回归策略构建',
    author: '量化学院',
    duration: '50分钟阅读',
    level: '进阶',
    type: 'article',
    views: 765,
    likes: 198,
    image: 'https://img.yzcdn.cn/vant/cat.jpeg'
  }
])

const caseCourses = ref([
  {
    id: 6,
    title: 'MACD金叉策略实战',
    author: '量化学院',
    duration: '40分钟',
    level: '实战',
    type: 'video',
    views: 1123,
    likes: 345,
    image: 'https://img.yzcdn.cn/vant/cat.jpeg'
  },
  {
    id: 7,
    title: '双均线系统实战',
    author: '量化学院',
    duration: '35分钟阅读',
    level: '实战',
    type: 'article',
    views: 987,
    likes: 276,
    image: 'https://img.yzcdn.cn/vant/cat.jpeg'
  }
])

// 模拟加载数据
onMounted(() => {
  globalLoading.show()

  // 模拟网络请求延迟
  setTimeout(() => {
    loading.value = false
    globalLoading.hide()
  }, 800)
})
</script>

<style scoped>
.learn-center {
  min-height: 100vh;
  background-color: var(--background-color);
  padding-bottom: calc(var(--tabbar-height) + var(--safe-area-inset-bottom));
}

.filter-section {
  padding: 12px 16px;
  background-color: var(--background-color-light);
  margin-bottom: 12px;
}

.filter-title {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin-bottom: 12px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-option {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 13px;
  background-color: var(--background-color);
  color: var(--text-color-regular);
  cursor: pointer;
  transition: all 0.3s;
}

.filter-option .van-icon {
  margin-right: 4px;
  font-size: 16px;
}

.filter-option.active {
  background-color: var(--primary-color);
  color: white;
}

.content-tabs {
  margin-top: 12px;
}

.safe-area-inset-bottom {
  height: var(--safe-area-inset-bottom);
}
</style>
