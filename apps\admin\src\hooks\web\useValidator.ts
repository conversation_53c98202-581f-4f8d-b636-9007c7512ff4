import { useI18n } from '@/hooks/web/useI18n'
import { isEmpty, isNullOrUnDef } from '@/utils/is'
import { FormItemRule } from 'element-plus'

const { t } = useI18n()

// 回调函数类型定义
type Callback = (error?: string | Error | undefined) => void

// 长度范围接口定义
interface LengthRange {
  min: number // 最小长度
  max: number // 最大长度
  message?: string // 可选的自定义错误消息
}

// 表单验证钩子函数
export const useValidator = () => {
  /**
   * 必填验证
   * @param message 可选的自定义错误消息
   * @returns FormItemRule 验证规则对象
   */
  const required = (message?: string): FormItemRule => {
    return {
      required: true,
      message: message || t('common.required')
    }
  }

  /**
   * 长度范围验证
   * @param options 包含min、max和message的对象
   * @returns FormItemRule 验证规则对象
   */
  const lengthRange = (options: LengthRange): FormItemRule => {
    const { min, max, message } = options

    return {
      min,
      max,
      message: message || t('common.lengthRange', { min, max })
    }
  }

  /**
   * 禁止空格验证
   * @param message 可选的自定义错误消息
   * @returns FormItemRule 验证规则对象
   */
  const notSpace = (message?: string): FormItemRule => {
    return {
      validator: (_, val, callback) => {
        if (val?.indexOf(' ') !== -1) {
          callback(new Error(message || t('common.notSpace')))
        } else {
          callback()
        }
      }
    }
  }

  /**
   * 禁止特殊字符验证
   * @param message 可选的自定义错误消息
   * @returns FormItemRule 验证规则对象
   */
  const notSpecialCharacters = (message?: string): FormItemRule => {
    return {
      validator: (_, val, callback) => {
        if (/[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/gi.test(val)) {
          callback(new Error(message || t('common.notSpecialCharacters')))
        } else {
          callback()
        }
      }
    }
  }

  /**
   * 邮箱格式验证
   * @param rule 验证规则(未使用)
   * @param val 要验证的值
   * @param callback 验证回调函数
   */
  const isEmail = (rule: any, val: any, callback: Callback) => {
    if (isEmpty(val) || isNullOrUnDef(val)) {
      callback()
    }
    // 判断是否为邮箱地址
    if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val)) {
      callback()
    } else {
      callback(new Error('请填写正确的邮箱地址'))
    }
  }

  /**
   * 手机号格式验证
   * @param rule 验证规则(未使用)
   * @param val 要验证的值
   * @param callback 验证回调函数
   */
  const isTelephone = (rule: any, val: any, callback: Callback) => {
    if (isEmpty(val) || isNullOrUnDef(val)) {
      callback()
    }
    // 判断是否为正确手机号
    if (/^1[3-9]\d{9}$/.test(val)) {
      callback()
    } else {
      callback(new Error('请填写正确的手机号'))
    }
  }

  /**
   * 金额格式验证
   * @param rule 验证规则(未使用)
   * @param val 要验证的值
   * @param callback 验证回调函数
   */
  const isAmount = (rule: any, val: any, callback: Callback) => {
    if (isEmpty(val) || isNullOrUnDef(val)) {
      callback()
    }
    // 判断是否为正确金额
    if (/^\d+(\.\d{1,2})?$/.test(val)) {
      callback()
    } else {
      callback(new Error('请填写正确的金额格式'))
    }
  }

  return {
    required,
    lengthRange,
    notSpace,
    notSpecialCharacters,
    isEmail,
    isTelephone,
    isAmount
  }
}
