#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : 认证核心验证

from fastapi import Request
import jwt
from config.settings import DEFAULT_AUTH_ERROR_MAX_NUMBER, REDIS_DB_ENABLE
from apps.front.organ import crud, models
from config import settings
from sqlalchemy.ext.asyncio import AsyncSession
from core.database import redis_getter
from core.exception import CustomException
from utils import status
from datetime import timedelta, datetime
from utils.count import Count
from ..schemas import Auth, LoginForm, LoginResult


class AuthValidation:
    """
    用于用户每次调用接口时，验证用户提交的token是否正确，并从token中获取用户信息
    """

    error_code = status.HTTP_401_UNAUTHORIZED
    warning_code = status.HTTP_ERROR

    @classmethod
    def validate_token(cls, request: Request, token: str | None) -> tuple[int, int]:
        """
        验证用户 token
        返回：(user_id, app_id)
        """
        if not token:
            raise CustomException(
                msg="请您先登录！",
                code=status.HTTP_403_FORBIDDEN,
                status_code=status.HTTP_403_FORBIDDEN
            )
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            user_id: int = payload.get("sub")
            app_id: int = payload.get("app")
            exp: int = payload.get("exp")
            is_refresh: bool = payload.get("is_refresh")

            if not user_id or not app_id or is_refresh:
                raise CustomException(
                    msg="未认证，请您重新登录",
                    code=status.HTTP_403_FORBIDDEN,
                    status_code=status.HTTP_403_FORBIDDEN
                )

            # 计算当前时间 + 缓冲时间是否大于等于 JWT 过期时间
            buffer_time = (datetime.now() + timedelta(minutes=settings.ACCESS_TOKEN_CACHE_MINUTES)).timestamp()
            if buffer_time >= exp:
                request.scope["if-refresh"] = 1
            else:
                request.scope["if-refresh"] = 0

        except (jwt.exceptions.InvalidSignatureError, jwt.exceptions.DecodeError):
            raise CustomException(
                msg="无效认证，请您重新登录",
                code=status.HTTP_403_FORBIDDEN,
                status_code=status.HTTP_403_FORBIDDEN
            )
        except jwt.exceptions.ExpiredSignatureError:
            raise CustomException(msg="认证已失效，请您重新登录", code=cls.error_code, status_code=cls.error_code)

        return user_id, app_id

    @classmethod
    async def validate_user(
        cls,
        request: Request,
        user: models.AppUser | None,
        app_id: int,
        db: AsyncSession
    ) -> Auth:
        """验证用户信息"""
        if user is None:
            raise CustomException(msg="未认证，请您重新登录", code=cls.error_code, status_code=cls.error_code)
        elif not user.is_active:
            raise CustomException(msg="用户已被冻结！", code=cls.error_code, status_code=cls.error_code)

        # 验证用户是否属于该应用
        user_base = await crud.UserDal(db).get_data(user_id=user.id, app_id=app_id, v_return_none=True)
        if not user_base:
            raise CustomException(msg="用户不属于该应用", code=cls.error_code, status_code=cls.error_code)

        request.scope["user_id"] = user.id
        request.scope["app_id"] = app_id
        request.scope["telephone"] = user.telephone
        try:
            request.scope["body"] = await request.body()
        except RuntimeError:
            request.scope["body"] = "获取失败"

        return Auth(user=user, db=db)


class LoginValidation:
    """验证用户登录时提交的数据是否有效"""

    def __init__(self, func):
        self.func = func

    async def __call__(
        self,
        data: LoginForm,
        db: AsyncSession,
        request: Request
    ) -> LoginResult:
        self.result = LoginResult()

        # 验证登录方式
        if data.method not in ["password", "code", "wx"]:
            self.result.msg = "无效的登录方式"
            return self.result

        # 获取应用配置
        app = await crud.AppDal(db).get_app_full_info(data.app_id)
        if not app.settings:
            self.result.msg = "应用配置不存在"
            return self.result

        # 验证登录方式是否启用
        if data.method == "password" and not app.settings.allow_password_login:
            self.result.msg = "该应用未启用密码登录"
            return self.result
        elif data.method == "code" and not app.settings.allow_code_login:
            self.result.msg = "该应用未启用验证码登录"
            return self.result
        elif data.method == "wx" and not app.settings.allow_wx_login:
            self.result.msg = "该应用未启用微信登录"
            return self.result

        # 对于密码和验证码登录，验证手机号
        if data.method in ["password", "code"]:
            if not data.telephone:
                self.result.msg = "请输入手机号"
                return self.result
            user = await crud.UserDal(db).get_data(telephone=data.telephone, v_return_none=True)
            if not user:
                self.result.msg = "该手机号不存在"
                return self.result
        else:
            user = None

        # 调用具体的登录验证方法
        result = await self.func(self, data=data, user=user, app=app, request=request)

        if REDIS_DB_ENABLE and data.method in ["password", "code"]:
            count_key = f"{data.telephone}_{data.method}_auth"
            count = Count(redis_getter(request), count_key)
        else:
            count = None

        if not result.status:
            self.result.msg = result.msg
            if count:
                number = await count.add(ex=86400)
                if number >= DEFAULT_AUTH_ERROR_MAX_NUMBER:
                    await count.reset()
                    # 如果等于最大次数，那么就将用户冻结
                    if user:
                        user.is_active = False
                        await db.flush()
        else:
            if count:
                await count.delete()
            self.result.msg = "OK"
            self.result.status = True
            self.result.user = user
            if user:
                await crud.UserDal(db).update_login_info(user, request.client.host)

        return self.result
