#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2022/12/9 15:27
# @File           : main.py
# @IDE            : PyCharm
# @desc           : CRUD代码生成器 - 根据数据模型自动生成CRUD相关代码

import os.path
import sys
from typing import Type
from config.settings import BASE_DIR
import inspect
from pathlib import Path
from core.database import Base
from scripts.crud_generate.utils.generate_base import GenerateBase
from scripts.crud_generate.utils.schema_generate import SchemaGenerate
from scripts.crud_generate.utils.params_generate import ParamsGenerate
from scripts.crud_generate.utils.dal_generate import DalGenerate
from scripts.crud_generate.utils.view_generate import ViewGenerate


class CrudGenerate(GenerateBase):
    """
    CRUD代码生成器
    
    功能：
    1. 根据数据模型自动生成以下代码：
       - Schema模型 (schemas目录)
       - 请求参数模型 (params目录)
       - 数据库访问层 (crud.py)
       - 视图路由 (views.py)
    
    生成规则：
    1. 文件名使用下划线命名法 (snake_case)
    2. 类名使用大驼峰命名法 (CamelCase)
    3. URL路径使用斜杠分隔 (path/segment)
    
    使用示例：
    generator = CrudGenerate(
        model=User,
        zh_name="用户管理",
        en_name="user"
    )
    generator.main()  # 生成并写入代码
    generator.generate_codes()  # 仅打印生成代码
    """

    # 应用根目录路径
    APPS_ROOT = os.path.join(BASE_DIR, "apps")
    # 脚本目录路径
    SCRIPT_DIR = os.path.join(BASE_DIR, 'scripts', 'crud_generate')

    def __init__(self, model: Type[Base], zh_name: str, en_name: str = None):
        """
        初始化CRUD生成器
        
        参数:
            model (Type[Base]): SQLAlchemy数据模型类
            zh_name (str): 功能中文名称(用于注释和描述)
            en_name (str, optional): 功能英文名称(用于文件名和类名)
            
        注意:
            - 如果en_name未提供，则使用模型类名
            - en_name应使用下划线命名法(如:user_profile)
            - 会自动转换为大驼峰命名法用于类名(UserProfile)
        """
        self.model = model
        self.zh_name = zh_name
        # model 文件的地址
        self.model_file_path = Path(inspect.getfile(sys.modules[model.__module__]))
        # model 文件 app 路径
        self.app_dir_path = self.model_file_path.parent.parent
        # schemas 目录地址
        self.schemas_dir_path = self.app_dir_path / "schemas"
        # params 目录地址
        self.params_dir_path = self.app_dir_path / "params"
        # crud 文件地址
        self.crud_file_path = self.app_dir_path / "crud.py"
        # view 文件地址
        self.view_file_path = self.app_dir_path / "views.py"

        if en_name:
            self.en_name = en_name
        else:
            self.en_name = self.model.__name__

        self.schema_file_path = self.schemas_dir_path / f"{self.en_name}.py"
        self.param_file_path = self.params_dir_path / f"{self.en_name}.py"

        self.base_class_name = self.snake_to_camel(self.en_name)
        self.schema_simple_out_class_name = f"{self.base_class_name}SimpleOut"
        self.dal_class_name = f"{self.base_class_name}Dal"
        self.param_class_name = f"{self.base_class_name}Params"

    def generate_codes(self) -> None:
        """
        生成CRUD相关代码并打印到控制台(不写入文件)
        
        生成内容包括:
        1. Schema模型代码
        2. 数据库访问层代码
        3. 请求参数模型代码
        4. 视图路由代码
        
        用途:
        用于预览生成的代码内容，确认无误后再调用main()写入文件
        """
        print(f"==========================={self.schema_file_path} 代码内容=================================")
        schema = SchemaGenerate(
            self.model,
            self.zh_name,
            self.en_name,
            self.schema_file_path,
            self.schemas_dir_path,
            self.base_class_name,
            self.schema_simple_out_class_name
        )
        print(schema.generate_code())

        print(f"==========================={self.dal_class_name} 代码内容=================================")
        dal = DalGenerate(
            self.model,
            self.zh_name,
            self.en_name,
            self.dal_class_name,
            self.schema_simple_out_class_name
        )
        print(dal.generate_code())

        print(f"==========================={self.param_file_path} 代码内容=================================")
        params = ParamsGenerate(
            self.model,
            self.zh_name,
            self.en_name,
            self.params_dir_path,
            self.param_file_path,
            self.param_class_name
        )
        print(params.generate_code())

        print(f"==========================={self.view_file_path} 代码内容=================================")
        view = ViewGenerate(
            self.model,
            self.zh_name,
            self.en_name,
            self.base_class_name,
            self.schema_simple_out_class_name,
            self.dal_class_name,
            self.param_class_name
        )
        print(view.generate_code())

    def main(self) -> None:
        """
        生成CRUD相关代码并写入项目文件
        
        执行步骤:
        1. 生成Schema模型代码并写入schemas目录
        2. 生成数据库访问层代码并写入crud.py
        3. 生成请求参数模型代码并写入params目录
        4. 生成视图路由代码并写入views.py
        
        注意:
            - 会覆盖已存在的文件
            - 建议先调用generate_codes()预览生成内容
        """
        schema = SchemaGenerate(
            self.model,
            self.zh_name,
            self.en_name,
            self.schema_file_path,
            self.schemas_dir_path,
            self.base_class_name,
            self.schema_simple_out_class_name
        )
        schema.write_generate_code()

        dal = DalGenerate(
            self.model,
            self.zh_name,
            self.en_name,
            self.dal_class_name,
            self.schema_simple_out_class_name
        )
        dal.write_generate_code()

        params = ParamsGenerate(
            self.model,
            self.zh_name,
            self.en_name,
            self.params_dir_path,
            self.param_file_path,
            self.param_class_name
        )
        params.write_generate_code()

        view = ViewGenerate(
            self.model,
            self.zh_name,
            self.en_name,
            self.base_class_name,
            self.schema_simple_out_class_name,
            self.dal_class_name,
            self.param_class_name
        )
        view.write_generate_code()
