from fastapi import APIRouter, Depends, UploadFile
from sqlalchemy.orm import joinedload

from apps.admin.params.resource import ImagesParams
from apps.admin.schemas.resource import ImagesOut, Images, ImagesSimpleOut
from apps.admin.services.resource import ImagesDal
from core.dependencies import IdList
from models.admin.resource.images import AdminImages
from utils.file.aliyun_oss import AliyunOSS, BucketConf
from utils.response import SuccessResponse
from apps.admin.services.auth import FullAdminAuth
from apps.admin.schemas.auth import Auth
from config.settings import ALIYUN_OSS

app = APIRouter()


###########################################################
#    图片资源管理
###########################################################
@app.get("/images", summary="获取图片列表")
async def get_images_list(p: ImagesParams = Depends(), auth: Auth = Depends(FullAdminAuth())):
    model = AdminImages
    v_options = [joinedload(model.create_user)]
    v_schema = ImagesOut
    datas, count = await ImagesDal(auth.db).get_datas(
        **p.dict(),
        v_options=v_options,
        v_schema=v_schema,
        v_return_count=True
    )
    return SuccessResponse(datas, count=count)


@app.post("/images", summary="创建图片")
async def create_images(file: UploadFile, auth: Auth = Depends(FullAdminAuth())):
    filepath = f"/resource/images/"
    result = await AliyunOSS(BucketConf(**ALIYUN_OSS)).upload_image(filepath, file)
    data = Images(
        filename=file.filename,
        image_url=result,
        create_user_id=auth.user.id
    )

    return SuccessResponse(await ImagesDal(auth.db).create_data(data=data))


@app.delete("/images", summary="删除图片", description="硬删除")
async def delete_images(ids: IdList = Depends(), auth: Auth = Depends(FullAdminAuth())):
    await ImagesDal(auth.db).delete_datas(ids.ids, v_soft=False)
    return SuccessResponse("删除成功")


@app.get("/images/{data_id}", summary="获取图片信息")
async def get_images(data_id: int, auth: Auth = Depends(FullAdminAuth())):
    return SuccessResponse(await ImagesDal(auth.db).get_data(data_id, v_schema=ImagesSimpleOut))
