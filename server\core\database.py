from typing import AsyncGenerator
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker, AsyncAttrs
from sqlalchemy.orm import DeclarativeBase, declared_attr
from config.settings import SQLALCHEMY_DATABASE_URL, REDIS_DB_ENABLE, MONGO_DB_ENABLE
from fastapi import Request
from core.exception import CustomException
from motor.motor_asyncio import AsyncIOMotorDatabase

# 创建异步数据库引擎
# 配置参数说明：
# - echo=False: 是否输出SQL语句到日志，生产环境建议关闭
# - echo_pool=False: 是否输出连接池事件到日志
# - pool_pre_ping=True: 每次连接检出时检查连接是否有效，可防止使用失效连接
# - pool_recycle=3600: 连接在连接池中的最大存活时间(秒)，超时后自动回收重建
# - pool_size=5: 连接池保持的连接数量
# - max_overflow=5: 允许的最大连接溢出数量，即在pool_size基础上额外允许创建的连接数
async_engine = create_async_engine(
    SQLALCHEMY_DATABASE_URL,
    echo=False,
    echo_pool=False,
    pool_pre_ping=True,
    pool_recycle=3600,
    pool_size=5,
    max_overflow=5,
    connect_args={}
)

# 创建异步会话工厂
# 配置参数说明：
# - autocommit=False: 禁用自动提交，需要显式调用commit()
# - autoflush=False: 禁用自动刷新，需要显式调用flush()
# - expire_on_commit=True: commit后使对象过期，确保后续访问会重新加载最新数据
session_factory = async_sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=async_engine,
    expire_on_commit=True,
    class_=AsyncSession
)


class Base(AsyncAttrs, DeclarativeBase):
    """
    SQLAlchemy ORM 基础模型类

    此类为所有数据模型提供基础功能：
    1. 异步属性访问支持 (通过AsyncAttrs)
    2. 声明式映射基础功能 (通过DeclarativeBase)
    3. 自动表名生成 (通过__tablename__属性)

    继承此类的模型示例：
    ```python
    class User(Base):
        __tablename__ = "users"  # 可选，不指定则自动生成

        id: Mapped[int] = mapped_column(primary_key=True)
        name: Mapped[str]
        email: Mapped[str] = mapped_column(unique=True)

        # 关系定义
        posts: Mapped[List["Post"]] = relationship(back_populates="author")
    ```

    自动表名生成规则：
    - 如果模型类已定义__tablename__，则使用该值
    - 否则将类名从驼峰式转换为下划线形式
      例如：UserProfile -> user_profile
    """

    @declared_attr.directive
    def __tablename__(cls) -> str:
        """
        自动生成数据库表名

        生成规则：
        1. 如果类中已定义__tablename__，则直接使用该值
        2. 否则将类名从驼峰式转换为下划线小写形式
           例如：UserProfile -> user_profile

        返回：
            str: 生成的表名
        """
        table_name = cls.__tablename__
        if not table_name:
            model_name = cls.__name__
            ls = []
            for index, char in enumerate(model_name):
                if char.isupper() and index != 0:
                    ls.append("_")
                ls.append(char)
            table_name = "".join(ls).lower()
        return table_name


async def db_getter() -> AsyncGenerator[AsyncSession, None]:
    """
    异步数据库会话获取器

    此函数用于FastAPI的依赖注入系统，为每个请求提供独立的数据库会话。
    特点：
    1. 自动事务管理（使用async with session.begin()）
    2. 会话生命周期与请求绑定
    3. 异常时自动回滚

    使用示例：
    ```python
    @app.get("/users/")
    async def get_users(db: AsyncSession = Depends(db_getter)):
        result = await db.execute(select(User))
        return result.scalars().all()
    ```

    返回：
        AsyncGenerator[AsyncSession, None]: 异步数据库会话生成器

    注意：
        - 会话会在请求结束时自动关闭
        - 事务会在with块结束时自动提交，如有异常则回滚
    """
    async with session_factory() as session:
        # 创建事务上下文，自动处理提交和回滚
        async with session.begin():
            yield session


def redis_getter(request: Request) -> Redis:
    """
    Redis客户端获取器

    从FastAPI应用状态中获取预配置的Redis客户端实例。

    参数：
        request: FastAPI请求对象，包含应用状态

    返回：
        Redis: 异步Redis客户端实例

    异常：
        CustomException: 当REDIS_DB_ENABLE=False时抛出

    使用示例：
    ```python
    @app.get("/cache/{key}")
    async def get_cached_data(key: str, request: Request):
        redis = redis_getter(request)
        value = await redis.get(key)
        return {"key": key, "value": value}
    ```

    注意：
        使用前需要确保：
        1. settings.py中REDIS_DB_ENABLE=True
        2. 应用启动时已正确初始化Redis连接
    """
    if not REDIS_DB_ENABLE:
        raise CustomException("请先配置Redis数据库链接并启用！", desc="请启用 application/settings.py: REDIS_DB_ENABLE")
    return request.app.state.redis


def mongo_getter(request: Request) -> AsyncIOMotorDatabase:
    """
    MongoDB数据库获取器

    从FastAPI应用状态中获取预配置的MongoDB数据库实例。

    参数：
        request: FastAPI请求对象，包含应用状态

    返回：
        AsyncIOMotorDatabase: 异步MongoDB数据库实例

    异常：
        CustomException: 当MONGO_DB_ENABLE=False时抛出

    使用示例：
    ```python
    @app.get("/documents/")
    async def get_documents(request: Request):
        mongo = mongo_getter(request)
        cursor = mongo.collection.find({})
        documents = await cursor.to_list(length=10)
        return documents
    ```

    注意：
        使用前需要确保：
        1. settings.py中MONGO_DB_ENABLE=True
        2. 应用启动时已正确初始化MongoDB连接
    """
    if not MONGO_DB_ENABLE:
        raise CustomException(
            msg="请先开启 MongoDB 数据库连接！",
            desc="请启用 application/settings.py: MONGO_DB_ENABLE"
        )
    return request.app.state.mongo
