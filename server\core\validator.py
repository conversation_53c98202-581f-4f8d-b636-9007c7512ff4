"""
官方文档：https://pydantic-docs.helpmanual.io/usage/validators/#reuse-validators
"""

import re


def vali_telephone(value: str) -> str:
    """
    手机号验证器
    
    验证中国手机号格式，支持所有主流运营商号段:
    1. 中国移动: 134-139, 147, 150-152, 157-159, 172, 178, 182-184, 187-188, 198
    2. 中国联通: 130-132, 145-146, 155-156, 166, 171, 175-176, 185-186
    3. 中国电信: 133, 149, 153, 173, 174, 177, 180-181, 189, 191, 199
    4. 虚拟运营商: 170, 162
    
    参数:
        value: str - 待验证的手机号字符串
        
    返回:
        str: 验证通过的手机号
        
    异常:
        ValueError: 当手机号格式无效时抛出
        
    示例:
        >>> vali_telephone("13800138000")
        "13800138000"
        
        >>> vali_telephone("123456")
        ValueError: 请输入正确手机号
        
    正则表达式说明:
        ^1                          # 以1开头
        (3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8[0-9]|9[0-9])  # 第二位数字规则
        \d{8}$                      # 后面8位数字
    """
    if not value or len(value) != 11 or not value.isdigit():
        raise ValueError("请输入正确手机号")

    regex = r'^1(3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8[0-9]|9[0-9])\d{8}$'

    if not re.match(regex, value):
        raise ValueError("请输入正确手机号")

    return value


def vali_email(value: str) -> str:
    """
    邮箱地址验证器
    
    验证常见邮箱格式，支持:
    1. 标准格式: <EMAIL>
    2. 带特殊字符: <EMAIL>
    3. 国际化域名: 用户@例子.中国
    
    参数:
        value: str - 待验证的邮箱字符串
        
    返回:
        str: 验证通过的邮箱地址
        
    异常:
        ValueError: 当邮箱格式无效时抛出
        
    示例:
        >>> vali_email("<EMAIL>")
        "<EMAIL>"
        
        >>> vali_email("invalid.email")
        ValueError: 请输入正确邮箱地址
        
    正则表达式说明:
        ^[a-zA-Z0-9._%+-]+          # 用户名部分(允许字母数字和常见符号)
        @                           # @符号
        [a-zA-Z0-9.-]+              # 域名部分
        \.[a-zA-Z]{2,}$             # 顶级域名(2个以上字母)
    """
    if not value:
        raise ValueError("请输入邮箱地址")

    regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

    if not re.match(regex, value):
        raise ValueError("请输入正确邮箱地址")

    return value




