<template>
  <ErrorBoundary>
    <LoadingContainer :loading="loading" text="加载回测详情...">
      <!-- 错误状态 -->
      <div v-if="!record" class="error-container">
        <van-empty description="未找到回测记录" image="search" />
        <van-button
          class="action-button"
          type="primary"
          round
          size="large"
          icon="plus"
          @click="resetBacktest"
        >
          开始新回测
        </van-button>
      </div>

      <!-- 详情内容 -->
      <div v-else class="detail-content">
        <!-- 回测报告标题 -->
        <div class="report-title card">
          <h2>回测报告</h2>
          <div class="report-meta">
            <span class="report-date">生成时间：{{ formatDate(record.date) }}</span>
            <div class="result-badge" :class="resultColor(record.result.finalReturn)">
              {{ record.result.finalReturn > 0 ? '+' : '' }}{{ record.result.finalReturn }}%
            </div>
          </div>
        </div>

        <!-- 收益曲线图卡片 - 移到前面突出显示 -->
        <div class="chart-section card">
          <h3 class="section-title">
            收益曲线
          </h3>
          <div ref="chartContainer" class="performance-chart" />
        </div>

        <!-- 回测结果概览卡片 -->
        <div class="result-overview card">
          <h3 class="section-title">
            回测结果概览
          </h3>
          <div class="metrics-grid">
            <div class="metric-item">
              <div class="metric-value" :class="resultColor(record.result.finalReturn)">
                {{ record.result.finalReturn > 0 ? '+' : '' }}{{ formatMetricValue(record.result.finalReturn) }}%
              </div>
              <div class="metric-label">
                总收益率
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-value" :class="resultColor(record.result.annualReturn)">
                {{ record.result.annualReturn > 0 ? '+' : '' }}{{ record.result.annualReturn }}%
              </div>
              <div class="metric-label">
                年化收益
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-value negative">
                {{ record.result.maxDrawdown }}%
              </div>
              <div class="metric-label">
                最大回撤
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-value">
                {{ formatMetricValue(record.result.winRate) }}%
              </div>
              <div class="metric-label">
                胜率
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-value">
                {{ formatMetricValue(record.result.sharpeRatio) }}
              </div>
              <div class="metric-label">
                夏普比率
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-value">
                {{ record.result.totalTrades || 0 }}
              </div>
              <div class="metric-label">
                交易次数
              </div>
            </div>
          </div>
        </div>

        <!-- 策略信息卡片 -->
        <div class="strategy-info card">
          <h3 class="section-title">
            策略信息
          </h3>
          <div class="strategy-header">
            <div class="strategy-name">
              {{ record.strategy?.name || '未知策略' }}
            </div>
            <div class="strategy-assets">
              <span v-if="!isMultiAsset">{{ record.asset?.name || '未知资产' }}</span>
              <span v-else>{{ assetCount }}个交易标的</span>
            </div>
          </div>
          <div class="strategy-description">
            <h4>策略简介</h4>
            <p>{{ record.strategy?.description || '暂无策略说明' }}</p>
          </div>

          <!-- 标的列表 (仅在多标的时显示) -->
          <div v-if="isMultiAsset" class="assets-section">
            <h4 class="param-group-title">
              回测标的 ({{ assetCount }})
            </h4>
            <div class="assets-list">
              <van-tag
                v-for="asset in assetList"
                :key="asset.code"
                type="primary"
                size="medium"
                class="asset-tag"
              >
                {{ asset.name }} ({{ asset.code }})
              </van-tag>
            </div>
          </div>
        </div>

        <!-- 回测参数卡片 -->
        <div class="params-section card">
          <h3 class="section-title">
            回测参数配置
          </h3>

          <!-- 基础参数 -->
          <div class="param-group">
            <h4 class="param-group-title">
              基础参数
            </h4>
            <div class="params-grid">
              <div class="param-item">
                <div class="param-label">
                  回测区间
                </div>
                <div class="param-value">
                  {{ formatDate(record.parameters.startDate) }} 至 {{ formatDate(record.parameters.endDate) }}
                </div>
              </div>
              <div class="param-item">
                <div class="param-label">
                  初始资金
                </div>
                <div class="param-value">
                  {{ record.parameters.initialCapital }}元
                </div>
              </div>
              <div class="param-item">
                <div class="param-label">
                  手续费率
                </div>
                <div class="param-value">
                  {{ record.parameters.commission || '0' }}%
                </div>
              </div>
              <div class="param-item">
                <div class="param-label">
                  滑点设置
                </div>
                <div class="param-value">
                  {{ record.parameters.slippage || '0' }}%
                </div>
              </div>
            </div>
          </div>

          <!-- 策略参数 -->
          <div v-if="record.parameters.strategyParams" class="param-group">
            <h4 class="param-group-title">
              策略参数
            </h4>
            <div class="params-grid">
              <div
                v-for="(value, key) in record.parameters.strategyParams"
                :key="key"
                class="param-item"
              >
                <div class="param-label">
                  {{ formatParamName(key) }}
                </div>
                <div class="param-value">
                  {{ formatParamValue(value) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 回测总结卡片 -->
        <div class="summary-section card">
          <h3 class="section-title">
            回测总结
          </h3>

          <!-- 策略分析 -->
          <div class="strategy-analysis">
            <p class="analysis-text">
              该策略在回测期间表现{{ getPerformanceComment(record.result) }}。
              总收益率为{{ formatMetricValue(record.result.finalReturn) }}%，
              年化收益率达到{{ formatMetricValue(record.result.annualReturn) }}%，
              最大回撤为{{ formatMetricValue(record.result.maxDrawdown) }}%。
              {{ getStrategyComment(record.result) }}
            </p>
          </div>

          <!-- 详细指标 -->
          <div class="key-metrics">
            <div class="metric-group">
              <h4 class="metric-group-title">
                风险指标
              </h4>
              <div class="metrics-grid">
                <div class="metric-item">
                  <div class="metric-label">
                    波动率
                  </div>
                  <div class="metric-value">
                    {{ formatMetricValue(record.result.volatility) }}%
                  </div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">
                    平均持仓时间
                  </div>
                  <div class="metric-value">
                    {{ record.result.avgHoldingPeriod || '-' }}天
                  </div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">
                    盈亏比
                  </div>
                  <div class="metric-value">
                    {{ formatMetricValue(record.result.profitLossRatio) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 交易记录卡片 -->
        <div class="trades-section card">
          <h3 class="section-title">
            交易记录
          </h3>

          <div v-if="trades.length === 0" class="empty-trades">
            <van-empty description="暂无交易记录" image="search" />
          </div>

          <div v-else class="trades-table">
            <!-- 表头 -->
            <div class="trades-header">
              <div class="trade-cell trade-date">
                日期
              </div>
              <div class="trade-cell trade-type">
                类型
              </div>
              <div class="trade-cell trade-symbol">
                标的
              </div>
              <div class="trade-cell trade-price">
                价格
              </div>
              <div class="trade-cell trade-amount">
                数量
              </div>
              <div class="trade-cell trade-value">
                金额
              </div>
            </div>

            <!-- 表格内容 -->
            <div class="trades-body">
              <div
                v-for="(trade, index) in trades"
                :key="index"
                class="trade-row"
                :class="{'odd-row': index % 2 === 0}"
              >
                <div class="trade-cell trade-date">
                  {{ formatDate(trade.date) }}
                </div>
                <div class="trade-cell trade-type" :class="trade.type === '买入' ? 'buy-type' : 'sell-type'">
                  {{ trade.type }}
                </div>
                <div class="trade-cell trade-symbol">
                  {{ trade.symbol }}
                </div>
                <div class="trade-cell trade-price">
                  {{ trade.price }}
                </div>
                <div class="trade-cell trade-amount">
                  {{ trade.amount }}
                </div>
                <div class="trade-cell trade-value" :class="trade.type === '买入' ? 'negative' : 'positive'">
                  {{ trade.type === '买入' ? '-' : '+' }}{{ trade.value }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <van-button
            class="action-button"
            type="primary"
            round
            block
            icon="star-o"
            @click="saveToRecord"
          >
            保存到记录
          </van-button>

          <van-button
            class="action-button"
            type="success"
            round
            block
            icon="share-o"
            @click="shareToSquare"
          >
            分享到广场
          </van-button>

          <van-button
            class="action-button"
            type="default"
            round
            block
            icon="replay"
            @click="rerunBacktest"
          >
            重新回测
          </van-button>
        </div>
      </div>
    </LoadingContainer>
  </ErrorBoundary>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick, inject } from 'vue'
import { useBacktestStore } from '@/stores/backtest'
import { createChart } from 'lightweight-charts'
import { showToast, showDialog } from 'vant'
import LoadingContainer from '@/components/LoadingContainer.vue'
import ErrorBoundary from '@/components/ErrorBoundary.vue'

const props = defineProps({
  recordId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['reset'])

const backtestStore = useBacktestStore()
const globalLoading = inject('globalLoading', { show: () => {}, hide: () => {} })

const loading = ref(true)
const record = ref(null)
const chartContainer = ref(null)
const trades = ref([])
let chart = null
let series = null

/**
 * 判断当前回测是否为多标的回测
 * @returns {boolean} 是否为多标的回测
 */
const isMultiAsset = computed(() => {
  if (!record.value || !record.value.asset) return false
  return Array.isArray(record.value.asset)
})

/**
 * 获取回测标的列表
 * @returns {Array} 标的列表数组
 */
const assetList = computed(() => {
  if (!record.value) return []

  if (isMultiAsset.value) {
    return record.value.asset
  } else if (record.value.asset) {
    return [record.value.asset]
  }
  return []
})

/**
 * 获取回测标的数量
 * @returns {number} 标的数量
 */
const assetCount = computed(() => {
  return assetList.value.length
})

// 获取回测记录
const fetchRecord = async () => {
  loading.value = true
  globalLoading.show()

  try {
    const id = parseInt(props.recordId)
    if (id) {
      // 模拟API请求延迟
      await new Promise(resolve => setTimeout(resolve, 800))

      record.value = backtestStore.getRecordById(id)
      if (!record.value) {
        showToast({
          type: 'fail',
          message: '未找到回测记录',
          position: 'middle'
        })
        throw new Error('未找到回测记录')
      }

      // 生成模拟交易记录数据
      generateTrades()
    }
  } catch (error) {
    console.error('获取回测记录失败:', error)
    showToast({
      type: 'fail',
      message: '加载数据失败',
      position: 'middle'
    })
    throw error
  } finally {
    loading.value = false
    globalLoading.hide()
  }
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  const d = new Date(date)
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
}

// 结果颜色
const resultColor = (value) => {
  return parseFloat(value) > 0 ? 'positive' : parseFloat(value) < 0 ? 'negative' : 'neutral'
}

// 格式化指标值
const formatMetricValue = (value) => {
  if (value === undefined || value === null) return '-'
  const num = parseFloat(value)
  return isNaN(num) ? '-' : num.toFixed(2)
}

// 获取策略表现评价
const getPerformanceComment = (result) => {
  const finalReturn = parseFloat(result.finalReturn)
  if (finalReturn >= 20) return '优异'
  if (finalReturn >= 10) return '良好'
  if (finalReturn >= 0) return '一般'
  if (finalReturn >= -10) return '欠佳'
  return '不理想'
}

// 获取策略分析评论
const getStrategyComment = (result) => {
  const comments = []

  // 分析夏普比率
  if (result.sharpeRatio) {
    if (result.sharpeRatio >= 2) {
      comments.push('策略的风险调整后收益表现优异')
    } else if (result.sharpeRatio >= 1) {
      comments.push('策略的风险收益比较合理')
    } else {
      comments.push('策略的风险收益比有待改善')
    }
  }

  // 分析最大回撤
  if (result.maxDrawdown) {
    const drawdown = parseFloat(result.maxDrawdown)
    if (drawdown <= 10) {
      comments.push('最大回撤控制得当')
    } else if (drawdown <= 20) {
      comments.push('最大回撤在可接受范围内')
    } else {
      comments.push('需要加强风险控制以降低最大回撤')
    }
  }

  // 分析胜率
  if (result.winRate) {
    const winRate = parseFloat(result.winRate)
    if (winRate >= 60) {
      comments.push('交易胜率较高')
    } else if (winRate >= 50) {
      comments.push('交易胜率一般')
    } else {
      comments.push('建议优化入场时机以提高胜率')
    }
  }

  return comments.join('，') + '。'
}

// 保存到记录
const saveToRecord = () => {
  if (!record.value) return

  showDialog({
    title: '保存到记录',
    message: '确定要将此回测报告保存到个人记录吗？',
    showCancelButton: true,
    confirmButtonText: '确定保存',
    cancelButtonText: '取消'
  })
    .then(() => {
      // 模拟保存过程
      globalLoading.show()

      setTimeout(() => {
        globalLoading.hide()

        // 实际项目中，这里应该调用API将回测结果保存到个人记录
        showToast({
          type: 'success',
          message: '保存成功！',
          position: 'bottom'
        })
      }, 1000)
    })
    .catch(() => {
      // 用户取消保存
    })
}

// 分享到回测广场
const shareToSquare = () => {
  if (!record.value) return

  showDialog({
    title: '分享到回测广场',
    message: '确定要将此回测结果分享到回测广场吗？其他用户将能看到您的回测结果。',
    showCancelButton: true,
    confirmButtonText: '确定分享',
    cancelButtonText: '取消'
  })
    .then(() => {
      // 模拟分享过程
      globalLoading.show()

      setTimeout(() => {
        globalLoading.hide()

        // 实际项目中，这里应该调用API将回测结果分享到策略广场
        showToast({
          type: 'success',
          message: '分享成功！',
          position: 'bottom'
        })

        // 不跳转到策略广场，只显示成功提示
        // 实际项目中，这里应该调用API将回测结果分享到策略广场，但不改变当前页面
      }, 1000)
    })
    .catch(() => {
      // 用户取消分享
    })
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value || !record.value) return

  if (chart) {
    chart.remove()
    chart = null
  }

  chart = createChart(chartContainer.value, {
    width: chartContainer.value.clientWidth,
    height: 280,
    layout: {
      backgroundColor: '#ffffff',
      textColor: '#333333',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      fontSize: 12
    },
    grid: {
      vertLines: {
        color: '#f0f0f0',
        style: 1,
        visible: true
      },
      horzLines: {
        color: '#f0f0f0',
        style: 1,
        visible: true
      },
    },
    timeScale: {
      timeVisible: true,
      secondsVisible: false,
      borderColor: '#e0e0e0',
      borderVisible: true,
      tickMarkFormatter: (time) => {
        const date = new Date(time * 1000);
        return `${date.getMonth() + 1}/${date.getDate()}`;
      }
    },
    rightPriceScale: {
      borderColor: '#e0e0e0',
      borderVisible: true,
      scaleMargins: {
        top: 0.1,
        bottom: 0.1,
      },
    },
    crosshair: {
      mode: 1,
      vertLine: {
        color: 'rgba(25, 137, 250, 0.5)',
        width: 1,
        style: 1,
        labelBackgroundColor: '#1989fa',
        labelVisible: true,
      },
      horzLine: {
        color: 'rgba(25, 137, 250, 0.5)',
        width: 1,
        style: 1,
        labelBackgroundColor: '#1989fa',
        labelVisible: true,
      }
    },
    handleScroll: {
      mouseWheel: true,
      pressedMouseMove: true,
      horzTouchDrag: true,
      vertTouchDrag: false
    },
    handleScale: {
      axisPressedMouseMove: true,
      mouseWheel: true,
      pinch: true
    }
  })

  // 根据收益率选择颜色
  const finalReturn = parseFloat(record.value.result.finalReturn)

  // 更丰富的颜色选择
  let lineColor, areaTopColor, areaBottomColor

  if (finalReturn >= 20) {
    // 高收益 - 深绿色
    lineColor = '#07a048'
    areaTopColor = 'rgba(7, 160, 72, 0.4)'
    areaBottomColor = 'rgba(7, 160, 72, 0.1)'
  } else if (finalReturn > 0) {
    // 正收益 - 绿色
    lineColor = '#07c160'
    areaTopColor = 'rgba(7, 193, 96, 0.4)'
    areaBottomColor = 'rgba(7, 193, 96, 0.1)'
  } else if (finalReturn >= -10) {
    // 小幅亏损 - 橙色
    lineColor = '#ff9800'
    areaTopColor = 'rgba(255, 152, 0, 0.4)'
    areaBottomColor = 'rgba(255, 152, 0, 0.1)'
  } else {
    // 大幅亏损 - 红色
    lineColor = '#ee0a24'
    areaTopColor = 'rgba(238, 10, 36, 0.4)'
    areaBottomColor = 'rgba(238, 10, 36, 0.1)'
  }

  // 添加面积图
  series = chart.addAreaSeries({
    topColor: areaTopColor,
    bottomColor: areaBottomColor,
    lineColor: lineColor,
    lineWidth: 2,
    priceFormat: {
      type: 'percent',
      precision: 2,
      minMove: 0.01,
    },
    lastValueVisible: true,
    priceLineVisible: true,
    priceLineWidth: 1,
    priceLineColor: lineColor,
    priceLineStyle: 2, // 虚线
  })

  // 生成更自然的模拟数据
  const startDate = new Date(record.value.parameters.startDate)
  const endDate = new Date(record.value.parameters.endDate)
  const days = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24))

  // 确保合理的数据点间隔
  const pointInterval = days > 10 ? Math.floor(days / 10) : 1

  const data = []
  let value = 0
  let prevValue = 0
  let trend = 0

  // 使用更自然的随机波动生成曲线
  for (let i = 0; i <= days; i++) {
    const currentDate = new Date(startDate)
    currentDate.setDate(startDate.getDate() + i)

    if (i === 0) {
      // 起始点
      value = 0
    } else if (i === days) {
      // 终点 - 确保达到最终收益率
      value = finalReturn
    } else {
      // 生成带有趋势的随机波动
      if (i % 5 === 0) {
        // 每5天可能改变趋势方向
        trend = (Math.random() - 0.5) * 0.5 + (finalReturn > 0 ? 0.1 : -0.1)
      }

      // 添加随机波动和趋势
      const volatility = Math.random() * 2 - 1 // 随机波动
      const dailyChange = volatility + trend

      // 确保曲线最终会接近目标值
      const remainingDays = days - i
      const targetDiff = (finalReturn - value) / remainingDays

      // 综合考虑随机波动和目标方向
      value += dailyChange * 0.7 + targetDiff * 0.3

      // 平滑处理
      value = prevValue * 0.3 + value * 0.7
    }

    prevValue = value

    // 只添加有意义的数据点，减少数据量
    if (i === 0 || i === days || i % pointInterval === 0) {
      data.push({
        time: Math.floor(currentDate.getTime() / 1000),
        value: parseFloat(value.toFixed(2))
      })
    }
  }

  // 设置数据并适配视图
  series.setData(data)

  // 添加标记点
  if (Math.abs(finalReturn) >= 10) {
    // 为重要点位添加标记
    const maxPoint = data.reduce((max, point) => point.value > max.value ? point : max, data[0])
    const minPoint = data.reduce((min, point) => point.value < min.value ? point : min, data[0])

    // 添加最高点标记
    if (maxPoint.value > finalReturn * 0.5 && maxPoint.time !== data[data.length-1].time) {
      series.createPriceLine({
        price: maxPoint.value,
        color: '#1989fa',
        lineWidth: 1,
        lineStyle: 2,
        axisLabelVisible: true,
        title: '最高点',
      })
    }

    // 添加最低点标记
    if (minPoint.value < finalReturn * 0.5 && minPoint.time !== data[0].time) {
      series.createPriceLine({
        price: minPoint.value,
        color: '#ff9800',
        lineWidth: 1,
        lineStyle: 2,
        axisLabelVisible: true,
        title: '最低点',
      })
    }
  }

  // 适配内容并设置可见范围
  chart.timeScale().fitContent()

  // 添加图表标题
  chart.applyOptions({
    watermark: {
      visible: true,
      fontSize: 24,
      horzAlign: 'center',
      vertAlign: 'center',
      color: 'rgba(171, 71, 188, 0.05)',
      text: '收益曲线',
    }
  })
}

// 处理窗口大小变化
const handleResize = () => {
  if (chart && chartContainer.value) {
    chart.applyOptions({
      width: chartContainer.value.clientWidth
    })
  }
}

// 生成模拟交易记录
const generateTrades = () => {
  if (!record.value) return

  const tradeCount = Math.floor(Math.random() * 8) + 3 // 3-10条交易记录
  const startDate = new Date(record.value.parameters.startDate)
  const endDate = new Date(record.value.parameters.endDate)
  const dateRange = endDate.getTime() - startDate.getTime()
  const asset = isMultiAsset.value ? record.value.asset[0] : record.value.asset
  const symbol = asset?.code || '600000'
  const name = asset?.name || '浦发银行'
  const initialCapital = record.value.parameters.initialCapital || 100000

  const generatedTrades = []
  let remainingCapital = initialCapital
  let stockHolding = 0
  let lastPrice = Math.floor(Math.random() * 20) + 10 // 10-30元的随机价格

  for (let i = 0; i < tradeCount; i++) {
    // 随机日期，按时间顺序排列
    const tradeDate = new Date(startDate.getTime() + (dateRange * (i + 1) / (tradeCount + 1)))

    // 价格波动 (-5% 到 +5%)
    const priceChange = (Math.random() * 10 - 5) / 100
    const price = parseFloat((lastPrice * (1 + priceChange)).toFixed(2))
    lastPrice = price

    // 交易类型和数量
    let type, amount, value

    if (i === 0 || (stockHolding === 0 && i < tradeCount - 1)) {
      // 第一笔交易或者没有持仓时买入
      type = '买入'
      // 使用50-80%的资金买入
      const ratio = Math.random() * 0.3 + 0.5
      amount = Math.floor((remainingCapital * ratio) / price / 100) * 100
      value = (price * amount).toFixed(2)
      remainingCapital -= price * amount
      stockHolding += amount
    } else if (i === tradeCount - 1 || stockHolding > 0) {
      // 最后一笔交易或者有持仓时卖出
      type = '卖出'
      // 卖出50-100%的持仓
      const ratio = Math.random() * 0.5 + 0.5
      amount = Math.floor(stockHolding * ratio / 100) * 100
      if (amount === 0 && stockHolding > 0) amount = stockHolding
      value = (price * amount).toFixed(2)
      remainingCapital += price * amount
      stockHolding -= amount
    } else {
      // 随机买入或卖出
      type = Math.random() > 0.5 ? '买入' : '卖出'
      if (type === '买入') {
        // 使用20-40%的资金买入
        const ratio = Math.random() * 0.2 + 0.2
        amount = Math.floor((remainingCapital * ratio) / price / 100) * 100
        value = (price * amount).toFixed(2)
        remainingCapital -= price * amount
        stockHolding += amount
      } else {
        // 卖出20-40%的持仓
        const ratio = Math.random() * 0.2 + 0.2
        amount = Math.floor(stockHolding * ratio / 100) * 100
        if (amount === 0 && stockHolding > 0) amount = Math.min(100, stockHolding)
        value = (price * amount).toFixed(2)
        remainingCapital += price * amount
        stockHolding -= amount
      }
    }

    // 确保交易数量有效
    if (amount > 0) {
      generatedTrades.push({
        date: tradeDate,
        type,
        symbol: `${name} (${symbol})`,
        price: price.toFixed(2),
        amount,
        value
      })
    }
  }

  trades.value = generatedTrades
}

// 重置回测
const resetBacktest = () => {
  emit('reset')
}

// 重新回测
const rerunBacktest = () => {
  if (!record.value) return

  emit('reset', {
    strategyId: record.value.strategy?.id,
    rerun: true,
    recordId: props.recordId
  })
}

onMounted(async () => {
  await fetchRecord()

  if (record.value) {
    nextTick(() => {
      initChart()
    })
  }

  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  if (chart) {
    chart.remove()
    chart = null
  }
})
</script>

<style scoped>
/* 主要样式 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
  gap: 16px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 8px;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  padding: 12px;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
}

.report-title h2 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.report-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #666;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1a1a1a;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 3px;
  height: 16px;
  background: var(--primary-color, #1989fa);
  margin-right: 8px;
  border-radius: 1.5px;
}

.param-group-title {
  font-size: 14px;
  color: #333;
  margin: 16px 0 8px 0;
  font-weight: 500;
}

/* 结果概览卡片样式 */
.result-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 14px;
}

.positive {
  background-color: rgba(7, 193, 96, 0.1);
  color: #07c160;
}

.negative {
  background-color: rgba(238, 10, 36, 0.1);
  color: #ee0a24;
}

.neutral {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 4px 0;
  width: 100%;
  box-sizing: border-box;
}

@media (min-width: 375px) {
  .metrics-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.metric-item {
  text-align: left;
  padding: 10px;
  background: #f8f9fb;
  border-radius: 8px;
  transition: transform 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.metric-item:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.metric-value.positive {
  color: #07c160;
}

.metric-value.negative {
  color: #ee0a24;
}

.metric-label {
  font-size: 12px;
  color: #666;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.metric-group {
  margin-bottom: 16px;
}

.metric-group:last-child {
  margin-bottom: 0;
}

.metric-group-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

/* 图表区域样式 */
.performance-chart {
  width: 100%;
  height: 280px;
  margin-top: 8px;
  border-radius: 4px;
  overflow: hidden;
}

/* 标的列表样式 */
.assets-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.asset-tag {
  margin-bottom: 4px;
}

/* 策略信息样式 */
.strategy-header {
  margin-bottom: 12px;
}

.strategy-name {
  font-size: 15px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.strategy-assets {
  font-size: 13px;
  color: #666;
}

.strategy-description h4 {
  font-size: 14px;
  color: #333;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.strategy-description p {
  font-size: 13px;
  line-height: 1.5;
  color: #666;
  margin: 0;
}

/* 参数区域样式 */
.params-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 4px 0;
  width: 100%;
  box-sizing: border-box;
}

@media (max-width: 360px) {
  .params-grid {
    grid-template-columns: 1fr;
  }
}

.param-item {
  padding: 10px;
  background: #f8f9fb;
  border-radius: 6px;
  margin-bottom: 0;
  box-sizing: border-box;
}

.param-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.param-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.action-button {
  margin-bottom: 0;
}

/* 回测总结样式 */
.summary-section .key-metrics {
  margin-bottom: 20px;
}

.strategy-analysis {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.strategy-analysis h4 {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0 0 8px 0;
}

.analysis-text {
  font-size: 13px;
  line-height: 1.6;
  color: #606266;
  margin: 0;
}

/* 交易记录样式 */
.trades-section {
  margin-top: 0;
}

.empty-trades {
  padding: 16px 0;
  text-align: center;
}

.trades-table {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
  font-size: 13px;
  box-sizing: border-box;
}

.trades-header {
  display: flex;
  background-color: #f8f9fb;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #ebeef5;
}

.trade-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
}

.trade-row:last-child {
  border-bottom: none;
}

.odd-row {
  background-color: #fafafa;
}

.trade-cell {
  padding: 8px 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box;
}

.trade-date {
  flex: 1.2;
  min-width: 80px;
}

.trade-type {
  flex: 0.8;
  min-width: 40px;
  text-align: center;
}

.trade-symbol {
  flex: 1.5;
  min-width: 90px;
}

.trade-price, .trade-amount {
  flex: 1;
  min-width: 50px;
  text-align: right;
}

.trade-value {
  flex: 1.2;
  min-width: 60px;
  text-align: right;
  font-weight: 500;
}

.buy-type {
  color: #ee0a24;
}

.sell-type {
  color: #07c160;
}

@media (max-width: 360px) {
  .trades-table {
    font-size: 12px;
  }

  .trade-cell {
    padding: 6px 3px;
  }

  .trade-symbol {
    min-width: 70px;
  }
}
</style>

