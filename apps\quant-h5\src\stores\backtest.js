import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useBacktestStore = defineStore('backtest', () => {
  // State
  const history = ref([])
  const isLoading = ref(false)
  const error = ref(null)

  // Getters (computed)
  const recentRecords = computed(() => history.value.slice(0, 10))
  const recordCount = computed(() => history.value.length)
  const hasRecords = computed(() => history.value.length > 0)

  // Actions
  const addRecord = (record) => {
    try {
      // Add timestamp and ID to record
      const newRecord = {
        id: Date.now(),
        date: new Date(),
        ...record
      }

      // Add to history
      history.value.unshift(newRecord)

      return newRecord.id
    } catch (err) {
      console.error('Error adding record:', err)
      error.value = 'Failed to add record'
      return null
    }
  }

  const deleteRecord = (id) => {
    try {
      const initialLength = history.value.length
      history.value = history.value.filter(r => r.id !== id)
      return initialLength !== history.value.length
    } catch (err) {
      console.error('Error deleting record:', err)
      error.value = 'Failed to delete record'
      return false
    }
  }

  const getRecordById = (id) => {
    try {
      // Convert id to number if it's a string (e.g. from URL query)
      const numId = typeof id === 'string' ? parseInt(id, 10) : id
      return history.value.find(r => r.id === numId) || null
    } catch (err) {
      console.error('Error getting record:', err)
      error.value = 'Failed to get record'
      return null
    }
  }

  const getRecentRecords = (limit = 10) => {
    return history.value.slice(0, limit)
  }

  const clearHistory = () => {
    history.value = []
  }

  return {
    // State
    history,
    isLoading,
    error,

    // Getters
    recentRecords,
    recordCount,
    hasRecords,

    // Actions
    addRecord,
    deleteRecord,
    getRecordById,
    getRecentRecords,
    clearHistory
  }
}, {
  // Enable persistence
  persist: {
    key: 'quant-h5-backtest-history',
    storage: localStorage,
    paths: ['history']
  }
})
