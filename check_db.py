import pymysql
import sys

# 数据库连接参数
db_config = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '1234',
    'charset': 'utf8mb4'
}

try:
    # 尝试连接到MySQL服务器
    connection = pymysql.connect(**db_config)
    print("成功连接到MySQL服务器")

    # 检查mxtt数据库是否存在
    with connection.cursor() as cursor:
        cursor.execute("SHOW DATABASES")
        databases = [db[0] for db in cursor.fetchall()]

        if 'mxtt' in databases:
            print("数据库'mxtt'已存在")
        else:
            print("数据库'mxtt'不存在")

            # 尝试创建数据库
            try:
                cursor.execute("CREATE DATABASE mxtt CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                connection.commit()
                print("已成功创建数据库'mxtt'")
            except Exception as e:
                print(f"创建数据库失败: {e}")

    # 尝试连接到mxtt数据库
    try:
        db_config['database'] = 'mxtt'
        mxtt_connection = pymysql.connect(**db_config)
        print("成功连接到'mxtt'数据库")
        mxtt_connection.close()
    except Exception as e:
        print(f"连接到'mxtt'数据库失败: {e}")

    connection.close()

except Exception as e:
    print(f"连接到MySQL服务器失败: {e}")
    sys.exit(1)
