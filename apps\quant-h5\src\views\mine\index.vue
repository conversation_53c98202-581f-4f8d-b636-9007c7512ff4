<template>
  <div class="user-center">
    <van-nav-bar
      title="个人中心"
      class="nav-bar"
    />

    <!-- 用户资料卡片 -->
    <ErrorBoundary>
      <LoadingContainer :loading="profileLoading">
        <Profile class="profile-card" @refresh="refreshUserData" />
      </LoadingContainer>
    </ErrorBoundary>

    <!-- 内容标签页 -->
    <van-tabs
      v-model="activeTab"
      animated
      swipeable
      color="var(--primary-color)"
      title-active-color="var(--primary-color)"
      title-inactive-color="var(--text-color-regular)"
      class="content-tabs"
    >
      <van-tab title="回测记录" name="history">
        <History ref="historyRef" />
      </van-tab>
      <van-tab title="我的收藏" name="collection">
        <Collection ref="collectionRef" />
      </van-tab>
      <van-tab title="消息通知" name="message" :badge="unreadCount || ''">
        <MessageCenter @read="updateUnreadCount" ref="messageRef" />
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, inject } from 'vue'
import { useRoute } from 'vue-router'
import Profile from './components/Profile.vue'
import History from './components/History.vue'
import Collection from './components/Collection.vue'
import MessageCenter from './components/MessageCenter.vue'
import LoadingContainer from '@/components/LoadingContainer.vue'
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import { useUserStore } from '@/stores/user'

// Store
const userStore = useUserStore()

// 路由
const route = useRoute()

// 活动标签页
const activeTab = ref('history')
const unreadCount = ref(0)
const profileLoading = ref(true)
const globalLoading = inject('globalLoading', { show: () => {}, hide: () => {} })

// 根据路由参数设置活动标签
watch(() => route.query.tab, (tab) => {
  if (tab && ['history', 'collection', 'message'].includes(tab)) {
    activeTab.value = tab
  }
}, { immediate: true })

// 更新未读消息数量
const updateUnreadCount = (count) => {
  unreadCount.value = count
}

// 刷新用户数据
const refreshUserData = async () => {
  try {
    profileLoading.value = true
    await userStore.fetchUserInfo()
  } catch (error) {
    console.error('刷新用户数据失败:', error)
  } finally {
    profileLoading.value = false
  }
}

// 初始化页面数据
onMounted(async () => {
  try {
    profileLoading.value = true
    globalLoading.show()

    // 获取用户信息
    await userStore.fetchUserInfo()

    // 模拟获取未读消息数量
    unreadCount.value = Math.floor(Math.random() * 5)
  } catch (error) {
    console.error('加载个人中心数据失败:', error)
  } finally {
    profileLoading.value = false
    globalLoading.hide()
  }
})
</script>

<style scoped>
.user-center {
  min-height: 100vh;
  background-color: var(--background-color);
  padding-bottom: calc(var(--tabbar-height) + var(--safe-area-inset-bottom));
}

.nav-bar {
  background-color: var(--background-color-light);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
}

.profile-card {
  margin: 0 var(--content-padding) 20px;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

/* 标签页 */
.content-tabs {
  margin-top: 0;
  background-color: var(--background-color);
}

:deep(.van-tabs__wrap) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background-color: var(--background-color-light);
  margin-bottom: 12px;
  position: relative;
  z-index: 1;
  height: 46px;
}

:deep(.van-tabs__nav) {
  padding: 0 8px;
}

:deep(.van-tabs__line) {
  height: 3px;
  border-radius: 3px;
  bottom: 8px;
  width: 20px !important;
}

:deep(.van-tabs__content) {
  background-color: var(--background-color);
  min-height: 300px;
  padding-top: 0;
}

:deep(.van-tab) {
  font-size: 15px;
  padding: 12px 0;
  font-weight: 500;
}

:deep(.van-tab--active) {
  font-weight: 600;
}

:deep(.van-badge) {
  transform: scale(0.85);
  transform-origin: top right;
  margin-top: -6px;
  margin-right: -8px;
  border: 1px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-weight: 500;
  padding: 0 5px;
  height: 16px;
  min-width: 16px;
  line-height: 14px;
  border-radius: 8px;
  font-size: 10px;
}
</style>
