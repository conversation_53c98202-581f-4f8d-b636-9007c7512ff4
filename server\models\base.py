from datetime import datetime
from sqlalchemy.orm import Mapped, mapped_column
from core.database import Base
from sqlalchemy import DateTime, Integer, func, Boolean, inspect


class BaseModel(Base):
    """
    数据库公共 ORM 基础模型类
    
    所有数据库模型都应继承此类，提供以下功能：
    1. 公共字段定义(id, 创建时间, 更新时间, 软删除标记)
    2. 模型字段信息获取方法
    3. 软删除支持
    
    特性:
    - __abstract__ = True: 声明为抽象基类，不会创建实际数据库表
    - 自动记录创建和更新时间
    - 支持软删除功能
    
    使用示例:
    class User(BaseModel):
        __tablename__ = "users"
        
        username: Mapped[str] = mapped_column(String(50), comment='用户名')
        email: Mapped[str] = mapped_column(String(100), unique=True, comment='邮箱')
    """
    __abstract__ = True  # 声明为抽象基类，SQLAlchemy不会为此类创建数据库表

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        comment='主键ID，自增整数类型'
    )
    create_datetime: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        comment='记录创建时间，自动设置为当前时间'
    )
    update_datetime: Mapped[datetime] = mapped_column(
        DateTime,
        server_default=func.now(),
        onupdate=func.now(),  # 记录更新时自动设置为当前时间
        comment='记录最后更新时间，自动更新'
    )
    delete_datetime: Mapped[datetime | None] = mapped_column(
        DateTime,
        nullable=True,
        comment='软删除时间，为None表示未删除'
    )
    is_delete: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        comment="软删除标记，True表示已删除"
    )

    @classmethod
    def get_column_attrs(cls) -> list[str]:
        """
        获取模型中除relationships外的所有普通字段名称
        
        返回:
            list[str]: 字段名称列表，不包含关系字段
            
        示例:
            User.get_column_attrs()
            返回: ['id', 'create_datetime', 'update_datetime', ...]
        """
        mapper = inspect(cls)

        # for attr_name, column_property in mapper.column_attrs.items():
        #     # 假设它是单列属性
        #     column = column_property.columns[0]
        #     # 访问各种属性
        #     print(f"属性: {attr_name}")
        #     print(f"类型: {column.type}")
        #     print(f"默认值: {column.default}")
        #     print(f"服务器默认值: {column.server_default}")

        return mapper.column_attrs.keys()

    @classmethod
    def get_attrs(cls) -> list[str]:
        """
        获取模型所有字段名称(包括普通字段和关系字段)
        
        返回:
            list[str]: 所有字段名称列表
            
        示例:
            User.get_attrs()
            返回: ['id', 'create_datetime', 'posts', ...]
        """
        mapper = inspect(cls)
        return mapper.attrs.keys()

    @classmethod
    def get_relationships_attrs(cls) -> list[str]:
        """
        获取模型中所有关系字段名称
        
        返回:
            list[str]: 关系字段名称列表
            
        示例:
            User.get_relationships_attrs()
            返回: ['posts', 'comments']
            
        注意:
            仅返回通过relationship()定义的关系字段
        """
        mapper = inspect(cls)
        return mapper.relationships.keys()
