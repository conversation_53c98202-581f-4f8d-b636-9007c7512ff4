#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2023/8/25 13:15
# @File           : crud.py
# @IDE            : PyCharm
# @desc           : 简要说明

from sqlalchemy.ext.asyncio import AsyncSession

from apps.admin.schemas.resource import ImagesSimpleOut
from core.crud import DalBase
from models.admin.resource.images import AdminImages


class ImagesDal(DalBase):

    def __init__(self, db: AsyncSession):
        super(ImagesDal, self).__init__()
        self.db = db
        self.model = AdminImages
        self.schema = ImagesSimpleOut
