from datetime import datetime
from sqlalchemy.orm import Mapped, mapped_column
from models.base import BaseModel
from sqlalchemy import String, Boolean, DateTime, Integer, ForeignKey
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')


class AppUser(BaseModel):
    __tablename__ = "app_user"
    __table_args__ = ({'comment': 'App用户表'})

    user_id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True, comment="用户ID")
    avatar: Mapped[str | None] = mapped_column(String(500), comment='头像')
    telephone: Mapped[str] = mapped_column(String(11), nullable=False, index=True, comment="手机号", unique=False)
    email: Mapped[str | None] = mapped_column(String(50), comment="邮箱地址")
    name: Mapped[str] = mapped_column(String(50), index=True, nullable=False, comment="姓名")
    nickname: Mapped[str | None] = mapped_column(String(50), nullable=True, comment="昵称")
    password: Mapped[str] = mapped_column(String(255), nullable=True, comment="密码")
    gender: Mapped[str | None] = mapped_column(String(8), nullable=True, comment="性别")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否可用")
    last_ip: Mapped[str | None] = mapped_column(String(50), comment="最后一次登录IP")
    last_login: Mapped[datetime | None] = mapped_column(DateTime, comment="最近一次登录时间")
    is_staff: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否为工作人员")
    wx_server_openid: Mapped[str | None] = mapped_column(String(255), comment="服务端微信平台openid")
    is_wx_server_openid: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否已有服务端微信平台openid")
    compute_power: Mapped[int] = mapped_column(Integer, default=0, comment="算力值")

    @staticmethod
    def get_password_hash(password: str) -> str:
        """
        生成哈希密码
        :param password: 原始密码
        :return: 哈希密码
        """
        return pwd_context.hash(password)

    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """
        验证原始密码是否与哈希密码一致
        :param password: 原始密码
        :param hashed_password: 哈希密码
        :return:
        """
        return pwd_context.verify(password, hashed_password)


class AppUserBase(BaseModel):
    __tablename__ = "app_user_base"
    __table_args__ = ({'comment': 'App用户和appbase之间的关联表'})

    user_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("app_user.id", ondelete='CASCADE'),
        comment="关联用户"
    )
    app_id: Mapped[int] = mapped_column(
        String(255),
        ForeignKey("app_base.id", ondelete='CASCADE'),
        comment="关联app"
    )
