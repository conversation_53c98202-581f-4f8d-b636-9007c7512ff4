<template>
  <div class="backtest-list">
    <!-- 列表内容 -->
    <div v-if="backtests.length > 0">
      <BacktestCard
        v-for="backtest in backtests"
        :key="backtest.id"
        :backtest="backtest"
        :is-liked="isLiked(backtest.id)"
        :is-collected="isCollected(backtest.id)"
        :like-count="getLikeCount(backtest.id)"
        :collect-count="getCollectCount(backtest.id)"
        :comment-count="getCommentCount(backtest.id)"
        @click="onCardClick(backtest)"
        @like="onLike"
        @collect="onCollect"
        @comment="onComment"
        @view-detail="onViewDetail"
      />
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <van-empty description="暂无回测案例" image="search" />
    </div>

    <!-- 加载更多 -->
    <div v-if="backtests.length > 0 && hasMore" class="load-more">
      <van-button
        plain
        type="primary"
        size="small"
        block
        :loading="loading"
        @click="loadMore"
      >
        {{ loading ? '加载中...' : '加载更多' }}
      </van-button>
    </div>

    <!-- 全部加载完毕 -->
    <div v-if="backtests.length > 0 && !hasMore" class="no-more">
      已经到底啦~
    </div>
  </div>
</template>

<script setup>

import { useStrategyStore } from '@/stores/strategy'
import BacktestCard from './Card.vue'

const props = defineProps({
  backtests: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  hasMore: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['item-click', 'like', 'collect', 'comment', 'view-detail', 'load-more'])

// 获取策略存储
const strategyStore = useStrategyStore()

// 检查是否点赞
const isLiked = (backtestId) => {
  return strategyStore.isLiked(backtestId)
}

// 检查是否收藏
const isCollected = (backtestId) => {
  return strategyStore.collectedStrategies.some(s => s.id === backtestId)
}

// 获取点赞数
const getLikeCount = (backtestId) => {
  return strategyStore.getLikeCount(backtestId)
}

// 获取收藏数（模拟数据）
const getCollectCount = (backtestId) => {
  // 这里可以从策略存储中获取收藏数，暂时返回随机数
  return Math.floor(Math.random() * 50) + 5
}

// 获取评论数
const getCommentCount = (backtestId) => {
  return strategyStore.getComments(backtestId).length
}

// 卡片点击事件
const onCardClick = (backtest) => {
  emit('item-click', backtest)
}

// 点赞事件
const onLike = (data) => {
  emit('like', data)
}

// 收藏事件
const onCollect = (backtestId) => {
  emit('collect', backtestId)
}

// 评论事件
const onComment = (backtest) => {
  emit('comment', backtest)
}

// 查看详情事件
const onViewDetail = (backtest) => {
  emit('view-detail', backtest)
}

// 加载更多
const loadMore = () => {
  emit('load-more')
}
</script>

<style scoped>
.backtest-list {
  padding: 12px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.load-more {
  margin: 16px 0;
  padding: 0 16px;
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 16px 0;
}
</style>
