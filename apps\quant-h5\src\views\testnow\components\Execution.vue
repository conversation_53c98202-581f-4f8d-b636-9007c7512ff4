<template>
  <div>
    <!-- 已选信息（在信息确认步骤中默认展开） -->
    <div v-if="status === 'idle'" class="card info-card">
      <van-collapse v-model="activeCollapse" class="info-collapse">
        <van-collapse-item name="info" class="info-item">
          <template #title>
            <div class="collapse-title">
              <span>已选信息</span>
            </div>
          </template>

          <div class="selected-info-content">
            <!-- 回测策略 -->
            <div class="info-section">
              <h4 class="info-subtitle">（一）回测策略：</h4>
              <div class="info-item-row">
                <span class="info-label">策略名称：</span>
                <span class="info-value">{{ backtestData?.strategy?.name || '-' }}（{{ getAssetTypeText(backtestData?.strategy) }}）</span>
              </div>
              <div class="info-item-row">
                <span class="info-label">策略标签：</span>
                <span class="info-value">
                  {{ getStrategyTypeText(backtestData?.strategy?.type) }}、
                  <span v-for="(market, index) in backtestData?.strategy?.markets" :key="market">
                    {{ market }}{{ index < backtestData?.strategy?.markets.length - 1 ? '、' : '' }}
                  </span>
                </span>
              </div>
              <div class="info-item-row">
                <span class="info-label">策略简介：</span>
                <span class="info-value">{{ backtestData?.strategy?.description || '-' }}</span>
              </div>
            </div>

            <!-- 回测标的 -->
            <div class="info-section">
              <h4 class="info-subtitle">（二）回测标的：</h4>
              <div class="info-item-row">
                <span class="info-label">标的类型：</span>
                <span class="info-value">{{ getAssetTypeText(backtestData?.strategy) }}</span>
              </div>
              <div class="info-item-row">
                <span class="info-label">标的选择：</span>
                <span class="info-value">
                  <template v-if="isMultiAsset">
                    <span v-for="(asset, index) in assetList" :key="asset.code">
                      {{ asset.name }}{{ index < assetList.length - 1 ? '、' : '' }}
                    </span>
                    （共 {{ assetCount }} 个标的）
                  </template>
                  <template v-else>
                    {{ backtestData?.asset?.name || '-' }}
                  </template>
                </span>
              </div>
            </div>

            <!-- 回测参数 -->
            <div class="info-section">
              <h4 class="info-subtitle">（三）回测参数：</h4>
              <div class="info-item-row">
                <span class="info-label">初始资金：</span>
                <span class="info-value">{{ backtestData?.parameters?.initialCapital || 0 }}元</span>
              </div>
              <div class="info-item-row">
                <span class="info-label">回测周期：</span>
                <span class="info-value">{{ formatDate(backtestData?.parameters?.startDate) || '-' }} 至 {{ formatDate(backtestData?.parameters?.endDate) || '-' }}</span>
              </div>
              <!-- 策略特定参数 -->
              <template v-if="backtestData?.parameters?.strategyParams">
                <div
                  v-for="(value, key) in backtestData.parameters.strategyParams"
                  :key="key"
                  class="info-item-row"
                >
                  <span class="info-label">{{ key }}：</span>
                  <span class="info-value">{{ value }}</span>
                </div>
              </template>
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>

    <!-- 信息确认面板 -->
    <div v-if="status === 'idle'" class="ready-panel card">
      <h3>信息确认</h3>
      <div class="compute-power-info">
        <p class="compute-power-text">
          本次回测将消耗 <span class="compute-power-value">{{ requiredComputePower }}</span> 点回测算力
        </p>
        <p class="compute-power-status" :class="{ 'power-insufficient': !hasEnoughPower }">
          当前剩余: <span class="compute-power-value">{{ userInfo.computePower }}</span> 点
          <template v-if="!hasEnoughPower">
            <span class="power-warning">回测算力不足</span>
          </template>
        </p>
        <div v-if="!hasEnoughPower" class="power-insufficient-tips">
          <p>您的回测算力不足，可以：</p>
          <ul>
            <li>修改回测参数，减少回测周期或标的数量</li>
            <li v-if="!userInfo.isGuest"><van-button type="primary" size="small" @click="goToRecharge">立即充值算力</van-button></li>
            <li v-if="userInfo.isGuest"><van-button type="primary" size="small" @click="goToLogin">登录/注册获取免费算力</van-button></li>
          </ul>
        </div>
        <p v-else>请确认以上信息，点击下方按钮开始执行回测</p>
      </div>
    </div>

    <div v-else-if="status === 'running'" class="progress-panel card">
      <van-circle
        :current-rate="progress"
        layer-color="#ebedf0"
        :speed="100"
        :text="progressText"
      />
      <p class="progress-step">{{ currentStepName }}</p>
    </div>

    <div v-else class="result-panel">
      <div class="result-summary">
        <h3>回测结果</h3>
        <van-grid :column-num="2" border>
          <van-grid-item>
            <div class="result-value" :class="resultColor(result.finalReturn)">
              {{ result.finalReturn > 0 ? '+' : '' }}{{ result.finalReturn }}%
            </div>
            <div class="result-label">最终收益率</div>
          </van-grid-item>
          <van-grid-item>
            <div class="result-value">{{ result.annualizedReturn }}%</div>
            <div class="result-label">年化收益</div>
          </van-grid-item>
          <van-grid-item>
            <div class="result-value">{{ result.maxDrawdown }}%</div>
            <div class="result-label">最大回撤</div>
          </van-grid-item>
          <van-grid-item>
            <div class="result-value">{{ result.winRate }}%</div>
            <div class="result-label">胜率</div>
          </van-grid-item>
        </van-grid>
      </div>

      <div class="chart-container">
        <div ref="chartContainer" class="performance-chart"></div>
      </div>
    </div>

    <!-- 按钮已移到父组件中处理 -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { createChart } from 'lightweight-charts'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'

const props = defineProps({
  backtestData: {
    type: Object,
    required: true
  }
})

/**
 * 定义组件事件
 * @emits complete 回测完成时触发，传递结果数据
 */
const emit = defineEmits(['complete'])

/**
 * 回测状态
 * @type {import('vue').Ref<'idle'|'running'|'completed'>}
 */
const status = ref('idle') // idle: 待开始, running: 执行中, completed: 已完成

/**
 * 回测进度百分比
 * @type {import('vue').Ref<number>}
 */
const progress = ref(0)

/**
 * 当前步骤名称
 * @type {import('vue').Ref<string>}
 */
const currentStepName = ref('准备开始')

/**
 * 进度显示文本
 * @type {import('vue').Ref<string>}
 */
const progressText = ref('0%')

/**
 * 展开的折叠面板项
 * @type {import('vue').Ref<string[]>}
 */
const activeCollapse = ref(['info'])
const result = ref({
  finalReturn: 0,
  annualizedReturn: 0,
  maxDrawdown: 0,
  winRate: 0
})

// 获取路由和用户商店
const router = useRouter()
const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)

// 计算所需的回测算力
const requiredComputePower = computed(() => {
  // 基础算力消耗
  let basePower = 1

  // 根据策略类型增加算力消耗
  if (props.backtestData.strategy) {
    if (props.backtestData.strategy.type === 'arbitrage' || props.backtestData.strategy.type === 'hedge') {
      basePower += 1 // 套利和对冲策略需要更多算力
    } else if (props.backtestData.strategy.type === 'dynamic') {
      basePower += 2 // 动态策略需要更多算力
    }
  }

  // 根据标的数量增加算力消耗
  if (Array.isArray(props.backtestData.asset)) {
    basePower += Math.min(props.backtestData.asset.length - 1, 5) // 每增加一个标的增加1点算力，最多增加5点
  }

  // 根据回测周期增加算力消耗
  if (props.backtestData.parameters && props.backtestData.parameters.startDate && props.backtestData.parameters.endDate) {
    const startDate = new Date(props.backtestData.parameters.startDate)
    const endDate = new Date(props.backtestData.parameters.endDate)
    const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))

    // 超过3个月(90天)的回测增加算力消耗
    if (days > 90) {
      basePower += Math.min(Math.floor((days - 90) / 30), 6) // 每增加1个月增加1点算力，最多增加6点
    }
  }

  return basePower
})

// 判断用户是否有足够的算力
const hasEnoughPower = computed(() => {
  if (userInfo.value.isGuest) return false
  return userInfo.value.computePower >= requiredComputePower.value
})

// 判断是否是多标的回测
const isMultiAsset = computed(() => {
  return Array.isArray(props.backtestData.asset)
})

// 获取标的列表
const assetList = computed(() => {
  if (isMultiAsset.value) {
    return props.backtestData.asset
  } else if (props.backtestData.asset) {
    return [props.backtestData.asset]
  }
  return []
})

// 获取标的数量
const assetCount = computed(() => {
  return assetList.value.length
})

// 获取标的类型文本
const getAssetTypeText = (strategy) => {
  if (!strategy) return ''
  if (strategy.type === 'arbitrage' || strategy.type === 'hedge') {
    return '多标的'
  } else if (strategy.type === 'dynamic') {
    return '动态标的'
  }
  return '单标的'
}

// 获取策略类型文本
const getStrategyTypeText = (type) => {
  switch(type) {
    case 'trend':
      return '趋势型'
    case 'mean_reversion':
      return '均值回归'
    case 'arbitrage':
      return '套利策略'
    case 'hedge':
      return '对冲策略'
    case 'dynamic':
      return '动态策略'
    default:
      return '其他策略'
  }
}

const chartContainer = ref(null)
let chart = null
let series = null

/**
 * 回测步骤定义
 * @type {Array<{name: string, progress: number}>}
 */
const steps = [
  { name: '准备数据', progress: 30 },
  { name: '执行回测', progress: 70 },
  { name: '生成报告', progress: 100 }
]

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const resultColor = (value) => {
  return value > 0 ? 'positive' : value < 0 ? 'negative' : ''
}

/**
 * 初始化回测结果图表
 * @description 创建并配置Lightweight Charts实例
 */
const initChart = () => {
  if (!chartContainer.value) return

  chart = createChart(chartContainer.value, {
    width: chartContainer.value.clientWidth,
    height: 300,
    layout: {
      backgroundColor: '#ffffff',
      textColor: '#333',
    },
    grid: {
      vertLines: {
        color: '#f0f0f0',
      },
      horzLines: {
        color: '#f0f0f0',
      },
    },
  })

  series = chart.addAreaSeries({
    topColor: 'rgba(25, 137, 250, 0.4)',
    bottomColor: 'rgba(25, 137, 250, 0.1)',
    lineColor: 'rgba(25, 137, 250, 1)',
    lineWidth: 2,
  })

  // 模拟数据
  const now = new Date()
  const data = []
  for (let i = 0; i < 100; i++) {
    const time = new Date(now.getTime() - (99 - i) * 24 * 60 * 60 * 1000)
    data.push({
      time: time.getTime() / 1000,
      value: 100 + Math.sin(i / 5) * 20 + Math.random() * 10
    })
  }
  series.setData(data)
  chart.timeScale().fitContent()
}

// 跳转到充值页面
const goToRecharge = () => {
  // 触发显示充值弹窗事件
  document.dispatchEvent(new CustomEvent('show-recharge-dialog'))
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login')
}

// 模拟回测启动
/**
 * 开始执行回测模拟
 * @function startSimulation
 * @description 触发回测流程，更新状态和进度
 */
const startSimulation = async () => {
  // 检查算力是否足够
  if (!hasEnoughPower.value) {
    showToast({
      type: 'fail',
      message: '回测算力不足，无法执行回测',
      position: 'middle'
    })
    return
  }

  // 消费算力
  if (!userInfo.value.isGuest) {
    try {
      const success = await userStore.consumeComputePower(requiredComputePower.value)
      if (!success) {
        showToast({
          type: 'fail',
          message: '扣除算力失败，请稍后重试',
          position: 'middle'
        })
        return
      }
    } catch (error) {
      console.error('消费算力失败:', error)
      showToast({
        type: 'fail',
        message: '扣除算力失败，请稍后重试',
        position: 'middle'
      })
      return
    }
  }

  status.value = 'running'

  // 使用steps定义的回测步骤
  steps.forEach(step => {
    currentStepName.value = step.name
    progress.value = step.progress
    progressText.value = `${step.progress}%`
  })

  // 模拟结果
  result.value = {
    finalReturn: (Math.random() * 50 - 10).toFixed(2),
    annualizedReturn: (Math.random() * 40 - 5).toFixed(2),
    maxDrawdown: (Math.random() * 30).toFixed(2),
    winRate: (Math.random() * 30 + 50).toFixed(2)
  }

  status.value = 'completed'
  initChart()
  emit('complete', result.value)
}

// 暴露方法给父组件调用
defineExpose({
  startSimulation
})

onMounted(() => {
  window.addEventListener('resize', handleResize)
  // 初始化时显示空图表
  initChart()

  // 不再自动启动模拟，必须通过按钮触发
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  if (chart) {
    chart.remove()
    chart = null
  }
})

const handleResize = () => {
  if (chart && chartContainer.value) {
    chart.applyOptions({ width: chartContainer.value.clientWidth })
  }
}
</script>

<style scoped>
/* ========== 布局样式 ========== */
.card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 16px;
  margin-bottom: 16px;
  margin-left: 12px;
  margin-right: 12px;
  overflow: hidden;
  width: calc(100% - 24px);
}

/* ========== 信息面板样式 ========== */

/* 信息卡片 */
.info-card {
  padding: 0;
}

/* 折叠面板样式 */
.info-collapse {
  width: 100%;
}

/* 折叠面板标题 */
.collapse-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
}

/* 自定义折叠面板样式 */
:deep(.van-collapse-item__title) {
  font-weight: 500;
  color: var(--text-color-primary);
  background-color: white;
  padding: 16px;
  height: auto;
  line-height: 1.5;
}

:deep(.van-collapse-item__content) {
  padding: 0 16px 16px;
  background-color: white;
}

/* 已选信息内容 */
.selected-info-content {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-section {
  margin-bottom: 12px;
}

/* 信息项行 */
.info-item-row {
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-label {
  font-weight: 500;
  color: var(--text-color-secondary);
}

.info-value {
  color: var(--text-color-primary);
}

/* 区块标题 */
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
  padding: 0;
}

/* 子标题 */
.info-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
}

/* 策略卡片样式 */
.selected-strategy-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 16px;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.card-title {
  margin: 0;
  font-size: 15px;
  font-weight: bold;
  color: var(--text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90%;
}

.strategy-badge {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 0 8px 0 8px;
  color: white;
  font-weight: 500;
  z-index: 1;
}

.badge-new {
  background-color: #1989fa;
}

.badge-hot {
  background-color: #ff976a;
}

.badge-popular {
  background-color: #07c160;
}

.card-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-bottom: 0;
}

.card-description {
  color: var(--text-color-secondary);
  font-size: 13px;
  margin-bottom: 5px;
  flex-grow: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 2px;
  margin-top: 2px;
}

.type-tag,
.market-tag {
  transform: scale(0.95);
  transform-origin: left center;
  font-size: 11px;
}

/* 标的信息样式 */
.asset-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.asset-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.asset-count {
  font-size: 12px;
  color: #969799;
  margin-top: 8px;
}

.single-asset {
  margin-bottom: 8px;
}

/* 参数信息样式 */
.params-list {
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 16px;
  width: 100%;
}

.param-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.param-item:last-child {
  margin-bottom: 0;
}

.param-label {
  color: var(--text-color-secondary);
  flex: 0 0 40%;
}

.param-value {
  color: var(--text-color-primary);
  font-weight: 500;
  text-align: right;
  flex: 0 0 60%;
}

/* 准备面板 */
.ready-panel {
  text-align: center;
  padding: 24px 16px;
}

.ready-panel h3 {
  margin-bottom: 16px;
  font-size: 18px;
  color: var(--text-color-primary);
}

.ready-panel p {
  color: var(--text-color-secondary);
  font-size: 14px;
  margin-bottom: 8px;
}

/* 算力信息样式 */
.compute-power-info {
  margin-top: 16px;
}

.compute-power-text {
  font-size: 15px;
  margin-bottom: 8px;
}

.compute-power-value {
  font-weight: bold;
  color: #ff976a;
}

.compute-power-status {
  font-size: 14px;
  margin-bottom: 12px;
}

.power-insufficient {
  color: #ee0a24;
}

.power-warning {
  background-color: #ee0a24;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
}

.power-insufficient-tips {
  background-color: #fff7f7;
  border: 1px solid #ffccc7;
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
  text-align: left;
}

.power-insufficient-tips p {
  color: #ee0a24;
  margin-bottom: 8px;
  font-weight: 500;
}

.power-insufficient-tips ul {
  padding-left: 16px;
  margin: 0;
}

.power-insufficient-tips li {
  margin-bottom: 8px;
  color: #666;
}

.power-insufficient-tips .van-button {
  margin-top: 4px;
}

/* 进度面板 */
.progress-panel {
  text-align: center;
  padding: 24px 16px;
}

.progress-step {
  margin-top: 16px;
  color: #1989fa;
  font-size: 16px;
  font-weight: 500;
}

/* 结果面板 */
.result-panel {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 16px;
  margin-bottom: 16px;
  margin-left: 12px;
  margin-right: 12px;
  width: calc(100% - 24px);
}

.result-summary {
  margin-bottom: 24px;
}

.result-summary h3 {
  text-align: center;
  margin-bottom: 16px;
  font-size: 18px;
  color: var(--text-color-primary);
}

.result-value {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.result-value.positive {
  color: #ee0a24;
}

.result-value.negative {
  color: #07c160;
}

.result-label {
  font-size: 12px;
  color: #969799;
}

.performance-chart {
  width: 100%;
  height: 300px;
}

.action-buttons {
  margin-top: 24px;
}
</style>
