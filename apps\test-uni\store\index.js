// 导入Vue和Vuex
import Vue from 'vue'
import Vuex from 'vuex'
// 导入getters
import getters from './getters'

// 在Vue中使用Vuex插件
Vue.use(Vuex)

// 使用Webpack的require.context方法动态加载模块
// 参数说明：
// - './modules'：要加载的目录
// - true：是否递归查找子目录
// - /\.js$/：匹配文件的正则表达式（所有.js文件）
const modulesFiles = require.context('./modules', true, /\.js$/)

// 动态加载modules目录中的所有Vuex模块
// 不需要手动使用 `import app from './modules/app'` 引入模块
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // 将模块路径转换为模块名，例如 './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  // 获取模块内容
  const value = modulesFiles(modulePath)
  // 将模块添加到modules对象中
  modules[moduleName] = value.default
  return modules
}, {})

// 创建Vuex Store实例
const store = new Vuex.Store({
  modules, // 动态加载的模块
  getters // 全局getters
})

// 导出store实例
export default store