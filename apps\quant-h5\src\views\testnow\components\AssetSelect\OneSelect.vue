<template>
  <div class="asset-select-container">
    <!-- 已选策略信息（可折叠面板） -->
    <div v-if="strategy" class="card strategy-info-card">
      <van-collapse v-model="activeCollapse" class="info-collapse">
        <van-collapse-item name="info" class="info-item">
          <template #title>
            <div class="collapse-title">
              <span>已选信息</span>
            </div>
          </template>

          <div class="selected-info-content">
            <!-- 回测策略 -->
            <div class="info-section">
              <h4 class="info-subtitle">（一）回测策略：</h4>
              <div class="info-item-row">
                <span class="info-label">策略名称：</span>
                <span class="info-value">{{ strategy.name }}（{{ getAssetTypeText(strategy) }}）</span>
              </div>
              <div class="info-item-row">
                <span class="info-label">策略标签：</span>
                <span class="info-value">
                  {{ getStrategyTypeText(strategy.type) }}、
                  <span v-for="(market, index) in strategy.markets" :key="market">
                    {{ market }}{{ index < strategy.markets.length - 1 ? '、' : '' }}
                  </span>
                </span>
              </div>
              <div class="info-item-row">
                <span class="info-label">策略简介：</span>
                <span class="info-value">{{ strategy.description }}</span>
              </div>
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>

    <!-- 选择标的区域 -->
    <div class="card asset-select-card">
      <h3 class="section-title-underline">选择标的</h3>

      <van-field
        v-model="searchQuery"
        placeholder="输入代码/拼音/名称搜索"
        clearable
      />

      <van-radio-group v-model="assetType" direction="horizontal">
        <van-radio name="all">全部</van-radio>
        <van-radio name="stock">股票</van-radio>
        <van-radio name="fund">基金</van-radio>
        <van-radio name="index">指数</van-radio>
      </van-radio-group>

      <van-radio-group v-model="selectedAsset">
        <van-cell-group>
          <van-cell
            v-for="asset in filteredAssets"
            :key="asset.code"
            :title="`${asset.name} (${asset.code})`"
            clickable
            @click="selectAsset(asset)"
          >
            <template #right-icon>
              <van-radio :name="asset.code" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-radio-group>

      <div class="recent-assets" v-if="recentAssets.length">
        <h3>最近选择</h3>
        <van-tag
          v-for="asset in recentAssets"
          :key="asset.code"
          type="primary"
          size="medium"
          @click="selectAsset(asset)"
        >
          {{ asset.name }}
        </van-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAssetStore } from '@/stores/asset'
import { useUserStore } from '@/stores/user'
import { pinyin } from 'pinyin-pro'
import { showToast } from 'vant'

const emit = defineEmits(['update:modelValue', 'select'])
const props = defineProps({
  modelValue: {
    type: Object,
    default: null
  },
  strategy: {
    type: Object,
    default: null
  }
})

// 存储相关
const assetStore = useAssetStore()
const userStore = useUserStore()

// 搜索相关状态
const searchQuery = ref('') // 搜索关键词
const selectedAsset = ref('') // 当前选中的资产code
const assetType = ref('all') // 资产类型筛选: all|stock|fund|index
const isLoading = ref(false) // 加载状态

// UI相关状态
const activeCollapse = ref([]) // 控制折叠面板展开状态

// 初始化已选择的标的
onMounted(() => {
  if (props.modelValue) {
    selectedAsset.value = props.modelValue.code
  }
})

/**
 * 获取所有资产数据（计算属性）
 * @returns {Array} 资产列表
 */
const allAssets = computed(() => assetStore.assets)

/**
 * 获取用户最近选择的资产（计算属性）
 * 根据用户存储的最近资产code列表，匹配完整的资产信息
 * @returns {Array} 最近使用的资产列表
 */
const recentAssets = computed(() => {
  return userStore.recentAssets
    .map(code => assetStore.assets.find(asset => asset.code === code))
    .filter(Boolean) // 过滤掉未找到的资产
})

// 搜索状态watch
watch(searchQuery, () => {
  isLoading.value = true
  setTimeout(() => {
    isLoading.value = false
  }, 300)
})

/**
 * 获取筛选后的资产列表（计算属性）
 * 根据策略、资产类型和搜索条件进行多维度筛选
 * @returns {Array} 筛选后的资产列表
 */
const filteredAssets = computed(() => {
  if (!allAssets.value) return []

  let filtered = [...allAssets.value]

  // 1. 根据策略过滤适用的标的
  if (props.strategy?.markets?.length) {
    filtered = filtered.filter(asset => {
      // 1.1 资产有明确市场属性时直接匹配
      if (asset.market) {
        return props.strategy.markets.includes(asset.market)
      }

      // 1.2 资产无市场属性时根据类型映射到市场
      const typeMarketMap = {
        stock: 'A股',
        fund: '基金',
        index: '指数'
      }
      return props.strategy.markets.includes(typeMarketMap[asset.type])
    })
  }

  // 2. 资产类型筛选
  if (assetType.value !== 'all') {
    filtered = filtered.filter(asset => asset.type === assetType.value)
  }

  // 3. 搜索关键词筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()

    filtered = filtered.filter(asset => {
      // 3.1 代码匹配
      if (asset.code.includes(query)) return true

      // 3.2 名称中文匹配
      if (asset.name.toLowerCase().includes(query)) return true

      // 3.3 名称拼音全拼匹配
      const namePinyin = pinyin(asset.name, { toneType: 'none' }).toLowerCase()
      if (namePinyin.includes(query)) return true

      // 3.4 名称拼音首字母匹配
      const namePinyinInitials = pinyin(asset.name, {
        toneType: 'none',
        pattern: 'first'
      }).toLowerCase()
      return namePinyinInitials.includes(query)
    })
  }

  return filtered
})

/**
 * 选择资产处理函数
 * @param {Object} asset 选中的资产对象
 */
const selectAsset = (asset) => {
  // 1. 更新选中状态
  selectedAsset.value = asset.code

  // 2. 添加到用户最近使用记录
  userStore.addRecentAsset(asset)

  // 3. 更新父组件v-model绑定
  emit('update:modelValue', asset)

  // 4. 显示选择成功提示
  showToast({
    type: 'success',
    message: `已选择: ${asset.name}`,
    position: 'bottom'
  })
}

// 获取标的类型文本
const getAssetTypeText = (strategy) => {
  if (strategy.type === 'arbitrage' || strategy.type === 'hedge') {
    return '多标的'
  } else if (strategy.type === 'dynamic') {
    return '动态标的'
  }
  return '单标的'
}

// 获取策略类型文本
const getStrategyTypeText = (type) => {
  switch(type) {
    case 'trend':
      return '趋势型'
    case 'mean_reversion':
      return '均值回归'
    case 'arbitrage':
      return '套利策略'
    case 'hedge':
      return '对冲策略'
    case 'dynamic':
      return '动态策略'
    default:
      return '其他策略'
  }
}

// 初始化加载资产数据
onMounted(async () => {
  isLoading.value = true
  await assetStore.fetchAssets()
  isLoading.value = false
})
</script>

<style scoped>
/* 主要样式 */

/* ======================
   布局和容器样式
   ====================== */
.asset-select-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 12px;
}

/* 卡片基础样式 */
.card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 16px;
  margin-bottom: 16px;
  overflow: hidden;
}

/* 策略信息卡片 */
.strategy-info-card {
  padding: 0;
}

/* 资产选择卡片 */
.asset-select-card {
  padding: 16px;
}

/* 折叠面板样式 */
.info-collapse {
  width: 100%;
}

/* 折叠面板标题 */
.collapse-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
}

/* 自定义折叠面板样式 */
:deep(.van-collapse-item__title) {
  font-weight: 500 !important;
  color: var(--text-color-primary) !important;
  background-color: white !important;
  padding: 16px !important;
  height: auto !important;
  line-height: 1.5 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

:deep(.van-cell__title) {
  flex: 1 !important;
}

:deep(.van-cell__right-icon) {
  position: static !important;
  margin-left: 4px !important;
  transform: rotate(0deg) !important;
  transition: transform 0.3s !important;
}

:deep(.van-cell--expanded) .van-cell__right-icon {
  transform: rotate(180deg) !important;
}

:deep(.van-collapse-item__content) {
  padding: 0 16px 16px;
  background-color: white;
}

/* 已选信息内容 */
.selected-info-content {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-section {
  margin-bottom: 12px;
}

/* 信息项行 */
.info-item-row {
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-label {
  font-weight: 500;
  color: var(--text-color-secondary);
}

.info-value {
  color: var(--text-color-primary);
}

/* 区块标题 */
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
  padding: 0;
}

/* 子标题 */
.info-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
}

/* 区域标题带下划线 */
.section-title-underline {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin-top: 0;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color-light);
}

.van-radio-group {
  margin: 16px 0;
  padding: 0 8px;
}

.van-radio {
  margin-right: 16px;
}

.van-radio:last-child {
  margin-right: 0;
}

.recent-assets {
  margin-top: 16px;
}

.recent-assets h3 {
  font-size: 14px;
  color: #969799;
  margin-bottom: 8px;
}

.van-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 策略卡片样式 */
.selected-strategy-card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.card-title {
  margin: 0;
  font-size: 15px;
  font-weight: bold;
  color: var(--text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90%;
}

.strategy-badge {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 0 8px 0 8px;
  color: white;
  font-weight: 500;
  z-index: 1;
}

.badge-new {
  background-color: #1989fa;
}

.badge-hot {
  background-color: #ff976a;
}

.badge-popular {
  background-color: #07c160;
}

.card-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-bottom: 0;
}

.card-description {
  color: var(--text-color-secondary);
  font-size: 13px;
  margin-bottom: 5px;
  flex-grow: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 2px;
  margin-top: 2px;
}

.type-tag,
.market-tag {
  transform: scale(0.95);
  transform-origin: left center;
  font-size: 11px;
}

.card-metrics {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-top: 4px;
  border-top: 1px solid var(--border-color-light);
  margin-top: 4px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 0 0 25%;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 13px;
  font-weight: bold;
  color: var(--primary-color);
  line-height: 1.2;
}

.metric-label {
  font-size: 9px;
  color: var(--text-color-secondary);
  margin-top: 1px;
  text-align: center;
  width: 100%;
}
</style>
