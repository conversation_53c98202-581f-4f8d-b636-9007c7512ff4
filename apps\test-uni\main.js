// 导入Vue
import Vue from 'vue'
// 导入根组件App
import App from './App'
// 导入Vuex Store
import store from './store' // store
// 导入自定义插件
import plugins from './plugins' // plugins
// 导入路由及路由拦截器
import { router, RouterMount } from './permission.js' // 路由拦截
// 导入uView UI库
import uView from 'uview-ui'

// 使用uView UI库
Vue.use(uView)
// 使用路由
Vue.use(router)
// 使用自定义插件
Vue.use(plugins)

// 配置uView的全局设置
// 文档：https://www.uviewui.com/components/setting.html
// 注意：配置后，很多组件的默认尺寸会改变，需要手动调整，不熟悉不建议开启
// 需要在Vue.use(uView)之后执行
uni.$u.setConfig({
  // 修改$u.config对象的属性
  config: {
    // 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
    unit: 'rpx'
  },
  // 修改$u.props对象的属性
  props: {
    // 修改radio组件的size参数的默认值，相当于执行 uni.$u.props.radio.size = 30
    radio: {
      size: 33,
      labelSize: 30
    },
    checkbox: {
      size: 33,
      labelSize: 30,
      iconSize: 20
    },
    button: {
      loadingSize: 28
    },
    text: {
      size: 30,
      color: '#000'
    }
    // 其他组件属性配置
    // ......
  }
})

// 关闭生产环境提示
Vue.config.productionTip = false
// 将store挂载到Vue原型上，方便在组件中通过this.$store访问
Vue.prototype.$store = store

// 设置App的mpType为'app'，用于标识当前应用类型
App.mpType = 'app'

// 创建Vue实例
const app = new Vue({
  ...App
})

// v1.3.5起，H5端需要使用路由自带的渲染方式
// #ifdef H5
RouterMount(app, router, '#app')
// #endif

// 非H5端（如小程序、App端）使用传统的挂载方式
// #ifndef H5
app.$mount() // 为了兼容小程序及App端必须这样写才有效果
// #endif