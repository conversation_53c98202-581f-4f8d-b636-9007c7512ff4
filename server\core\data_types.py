"""
自定义数据类型 - 官方文档：https://docs.pydantic.dev/dev-v2/usage/types/custom/#adding-validation-and-serialization
"""
import datetime
from typing import Annotated, Any
from bson import ObjectId
from pydantic import AfterValidator, PlainSerializer, WithJsonSchema
from .validator import *


def datetime_str_vali(value: str | datetime.datetime | int | float | dict):
    """
    日期时间字符串验证器
    
    支持多种输入类型转换为标准日期时间字符串格式:
    1. 字符串: 验证是否符合"%Y-%m-%d %H:%M:%S"格式
    2. datetime对象: 转换为标准字符串格式
    3. MongoDB日期格式: 从{"$date": "ISO格式"}中提取并转换
    4. 其他类型(int/float): 抛出异常
    
    参数:
        value: 输入值，可以是:
            - 字符串(格式: "2023-01-01 12:00:00")
            - datetime.datetime对象
            - MongoDB日期格式字典({"$date": "2023-01-01T12:00:00.000Z"})
            
    返回:
        str: 标准格式的日期时间字符串
        
    异常:
        ValueError: 当输入值无法转换为有效日期时间时抛出
        
    示例:
        >>> datetime_str_vali("2023-01-01 12:00:00")
        "2023-01-01 12:00:00"
        
        >>> datetime_str_vali(datetime.datetime(2023,1,1,12,0,0))
        "2023-01-01 12:00:00"
    """
    if isinstance(value, str):
        pattern = "%Y-%m-%d %H:%M:%S"
        try:
            datetime.datetime.strptime(value, pattern)
            return value
        except ValueError:
            pass
    elif isinstance(value, datetime.datetime):
        return value.strftime("%Y-%m-%d %H:%M:%S")
    elif isinstance(value, dict):
        # 用于处理 mongodb 日期时间数据类型
        date_str = value.get("$date")
        date_format = '%Y-%m-%dT%H:%M:%S.%fZ'
        # 将字符串转换为datetime.datetime类型
        datetime_obj = datetime.datetime.strptime(date_str, date_format)
        # 将datetime.datetime对象转换为指定的字符串格式
        return datetime_obj.strftime('%Y-%m-%d %H:%M:%S')
    raise ValueError("无效的日期时间或字符串数据")


# 自定义日期时间字符串类型
# 用于Pydantic模型中处理日期时间字段，确保统一格式
DatetimeStr = Annotated[
    str | datetime.datetime | int | float | dict,
    AfterValidator(datetime_str_vali),  # 验证和转换输入值
    PlainSerializer(lambda x: x, return_type=str),  # 序列化为字符串
    WithJsonSchema({'type': 'string'}, mode='serialization')  # OpenAPI/Swagger文档中显示为字符串
]
"""
Pydantic自定义日期时间字符串类型

特性:
1. 支持多种输入类型自动转换为标准格式字符串
2. 严格的格式验证
3. 序列化为字符串格式
4. 在API文档中显示为string类型

使用示例:
class User(BaseModel):
    create_time: DatetimeStr  # 会自动验证和转换

user = User(create_time="2023-01-01 12:00:00")  # 有效
user = User(create_time=datetime.datetime.now())  # 有效
user = User(create_time={"$date": "2023-01-01T12:00:00Z"})  # MongoDB格式有效
"""


# 自定义手机号类型
# 用于验证和标准化手机号格式
Telephone = Annotated[
    str,
    AfterValidator(lambda x: vali_telephone(x)),  # 验证手机号格式
    PlainSerializer(lambda x: x, return_type=str),  # 序列化为字符串
    WithJsonSchema({'type': 'string'}, mode='serialization')  # OpenAPI/Swagger文档中显示为字符串
]
"""
Pydantic自定义手机号类型

特性:
1. 验证手机号格式(11位数字，有效号段)
2. 序列化为字符串格式
3. 在API文档中显示为string类型

使用示例:
class User(BaseModel):
    phone: Telephone  # 会自动验证手机号格式

user = User(phone="13800138000")  # 有效
user = User(phone="123456")  # 会抛出验证错误
"""


# 实现自定义一个邮箱类型
Email = Annotated[
    str,
    AfterValidator(lambda x: vali_email(x)),
    PlainSerializer(lambda x: x, return_type=str),
    WithJsonSchema({'type': 'string'}, mode='serialization')
]


def date_str_vali(value: str | datetime.date | int | float):
    """
    日期字符串验证
    如果我传入的是字符串，那么直接返回，如果我传入的是一个日期类型，那么会转为字符串格式后返回
    因为在 pydantic 2.0 中是支持 int 或 float 自动转换类型的，所以我这里添加进去，但是在处理时会使这两种类型报错

    官方文档：https://docs.pydantic.dev/dev-v2/usage/types/datetime/
    """
    if isinstance(value, str):
        pattern = "%Y-%m-%d"
        try:
            datetime.datetime.strptime(value, pattern)
            return value
        except ValueError:
            pass
    elif isinstance(value, datetime.date):
        return value.strftime("%Y-%m-%d")
    raise ValueError("无效的日期时间或字符串数据")


# 实现自定义一个日期字符串的数据类型
DateStr = Annotated[
    str | datetime.date | int | float,
    AfterValidator(date_str_vali),
    PlainSerializer(lambda x: x, return_type=str),
    WithJsonSchema({'type': 'string'}, mode='serialization')
]


def object_id_str_vali(value: str | dict | ObjectId):
    """
    MongoDB ObjectId 验证器
    
    支持多种输入类型转换为ObjectId字符串:
    1. 字符串: 直接返回(假设已经是有效的ObjectId字符串)
    2. ObjectId对象: 转换为字符串
    3. MongoDB格式字典: 从{"$oid": "..."}中提取
    
    参数:
        value: 输入值，可以是:
            - ObjectId字符串
            - bson.ObjectId对象
            - MongoDB格式字典({"$oid": "..."})
            
    返回:
        str: ObjectId字符串
        
    异常:
        ValueError: 当输入值无法转换为有效ObjectId时抛出
        
    示例:
        >>> object_id_str_vali("507f1f77bcf86cd799439011")
        "507f1f77bcf86cd799439011"
        
        >>> object_id_str_vali(ObjectId("507f1f77bcf86cd799439011"))
        "507f1f77bcf86cd799439011"
    """
    if isinstance(value, str):
        return value
    elif isinstance(value, dict):
        return value.get("$oid")
    elif isinstance(value, ObjectId):
        return str(value)
    raise ValueError("无效的 ObjectId 数据类型")


ObjectIdStr = Annotated[
    Any,  # 这里不能直接使用 any，需要使用 typing.Any
    AfterValidator(object_id_str_vali),
    PlainSerializer(lambda x: x, return_type=str),
    WithJsonSchema({'type': 'string'}, mode='serialization')
]
