2025-07-22 12:15:48.948 | ERROR    | core.exception:unicorn_exception_handler:125 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\myproject\mxtt\server\main.py", line 116, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002C96EC33AF0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002C96EC33AF0>
           └ <function get_command at 0x000002C9059E29E0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x000002C905A1D6C0>
           └ <TyperGroup >

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x000002C905A1C940>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002C905A5A500>
         │    └ <function MultiCommand.invoke at 0x000002C972186C20>
         └ <TyperGroup >

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000002C905AA2E60>
           │               │       │       └ <function Command.invoke at 0x000002C972186710>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x000002C905AA2E60>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x000002C905ABD630>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x000002C905AA2E60>
           │   │      │    └ <function run at 0x000002C905ABD240>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002C972185480>
           └ <click.core.Context object at 0x000002C905AA2E60>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x000002C905A1F010>

  File "F:\share\myproject\mxtt\server\main.py", line 69, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002C9721DE7A0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\MyProjects\\venv\\.venv\\lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002C9721DE200>
    └ <uvicorn.server.Server object at 0x000002C905AA0370>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002C9721DE290>
           │       │   └ <uvicorn.server.Server object at 0x000002C905AA0370>
           │       └ <function run at 0x000002C96ECC9B40>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002C905A9D1C0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002C97095E170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 633, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002C970A0AB90>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 600, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002C97095FBE0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1896, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002C9708EB2E0>
    └ <Handle <TaskStepMethWrapper object at 0x000002C905D68070>()>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <TaskStepMethWrapper object at 0x000002C905D68070>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <TaskStepMethWrapper object at 0x000002C905D68070>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <TaskStepMethWrapper object at 0x000002C905D68070>()>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002C905D45090>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905D44AF0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002C905CE71F0>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000002C905CE7220>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002C905D45090>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905D44AF0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002C905D68100>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002C905B78D90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002C905CE71F0>
          └ <function wrap_app_handling_exceptions at 0x000002C971FB1090>

> File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002C905D45BD0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905D44AF0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002C905B78D90>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002C905D45BD0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905D44AF0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002C905B78D90>>
          └ <fastapi.routing.APIRouter object at 0x000002C905B78D90>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 808, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002C905D45BD0>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905D44AF0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x000002C905B78D90>>
          └ <fastapi.routing.APIRouter object at 0x000002C905B78D90>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 692, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-22 12:20:05.671 | ERROR    | core.exception:all_exception_handler:219 - (asyncmy.errors.ProgrammingError) (1146, "Table 'mxtt.admin_auth_user' doesn't exist")
[SQL: SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickname, admin_auth_user.password, admin_auth_user.gender, admin_auth_user.is_active, admin_auth_user.is_reset_password, admin_auth_user.last_ip, admin_auth_user.last_login, admin_auth_user.is_staff, admin_auth_user.wx_server_openid, admin_auth_user.is_wx_server_openid, admin_auth_user.id, admin_auth_user.create_datetime, admin_auth_user.update_datetime, admin_auth_user.delete_datetime, admin_auth_user.is_delete 
FROM admin_auth_user 
WHERE admin_auth_user.is_delete = false AND admin_auth_user.telephone = %s]
[parameters: ('***********',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x000002C973B23A30>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000002C9746D8610>
    └ <sqlalchemy.engine.base.Connection object at 0x000002C905D689D0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('***********',)
    │      │       └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000002C97474DBD0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002C97257E7A0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 92, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('***********',)
           │    │      │    │              └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000002C97474DCF0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002C97257E7A0>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002C97257E7A0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 130, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
           │                     └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000002C905E03C30>
           └ <_AsyncIoGreenlet object at 0x000002C9057151C0 (otid=0x000002C97245EEE0) dead>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 195, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000002C905E03C30>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 104, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('***********',)
                   │    │               └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002C97257E7A0>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 494, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 682, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1069, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 644, in read_packet
    packet.raise_for_error()
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x000002C9747C5220>
    └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\MyProjects\\venv\\.venv\\lib\\site-packages\\asyncmy\\errors.cp310-win_amd64...
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x000002C9747C5220>
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

asyncmy.errors.ProgrammingError: (1146, "Table 'mxtt.admin_auth_user' doesn't exist")


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "F:\share\myproject\mxtt\server\main.py", line 116, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x000002C96EC33AF0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x000002C96EC33AF0>
           └ <function get_command at 0x000002C9059E29E0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x000002C905A1D6C0>
           └ <TyperGroup >

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x000002C905A1C940>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x000002C905A5A500>
         │    └ <function MultiCommand.invoke at 0x000002C972186C20>
         └ <TyperGroup >

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x000002C905AA2E60>
           │               │       │       └ <function Command.invoke at 0x000002C972186710>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x000002C905AA2E60>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x000002C905ABD630>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x000002C905AA2E60>
           │   │      │    └ <function run at 0x000002C905ABD240>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x000002C972185480>
           └ <click.core.Context object at 0x000002C905AA2E60>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x000002C905A1F010>

  File "F:\share\myproject\mxtt\server\main.py", line 69, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x000002C9721DE7A0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\MyProjects\\venv\\.venv\\lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x000002C9721DE200>
    └ <uvicorn.server.Server object at 0x000002C905AA0370>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000002C9721DE290>
           │       │   └ <uvicorn.server.Server object at 0x000002C905AA0370>
           │       └ <function run at 0x000002C96ECC9B40>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x000002C905A9D1C0>
           │    └ <function BaseEventLoop.run_until_complete at 0x000002C97095E170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 633, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000002C970A0AB90>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 600, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000002C97095FBE0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1896, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000002C9708EB2E0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002C905B7C400>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002C905...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002C...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000002C905AF06A0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000002C905B7C400>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002C905...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002C...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002C905...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002C...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002C905CE7CD0>
          └ <fastapi.applications.FastAPI object at 0x000002C905AF06A0>

> File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002C905D45AB0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002C...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000002C905CE7CA0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000002C905CE7CD0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:9000', 'connection': 'keep-alive', 'content-length': '83', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000002C905D45AB0>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002C...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000002C972231990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002C905CE7CA0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000002C905CE7CA0...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000002C...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000002C905CE72B0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000002C905CE7CA0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002C905D45510>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002C905D6B0D0>
                     │    └ <function register_jwt_refresh_middleware.<locals>.jwt_refresh_middleware at 0x000002C905B6DAB0>
                     └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000002C905CE72B0>

  File "F:\share\myproject\mxtt\server\core\middleware.py", line 235, in jwt_refresh_middleware
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002C905D6B0D0>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002C905D45510>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'mxtt.admin_auth_user\' doesn\'t exist")')

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002C905D44F70>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905D44B80>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000002C905CE7220>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000002C905CE72B0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002C905DE5240>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x000002C905D6B670>
                     │    └ <function register_request_log_middleware.<locals>.request_log_middleware at 0x000002C905B6DA20>
                     └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000002C905CE7220>

  File "F:\share\myproject\mxtt\server\core\middleware.py", line 81, in request_log_middleware
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x000002C905D6B670>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x000002C905DE5240>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'mxtt.admin_auth_user\' doesn\'t exist")')

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002C905DE53F0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905DE52D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002C905CE71F0>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x000002C905CE7220>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x000002C905DE53F0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905DE52D0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000002C905D6BA60>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000002C905B78D90>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000002C905CE71F0>
          └ <function wrap_app_handling_exceptions at 0x000002C971FB1090>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002C905DE56C0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905DE52D0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000002C905B78D90>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002C905DE56C0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905DE52D0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000002C905B78D90>>
          └ <fastapi.routing.APIRouter object at 0x000002C905B78D90>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002C905DE56C0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905DE52D0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Route.handle at 0x000002C971FB2440>
          └ APIRoute(path='/admin/auth/api/login', name='api_login_for_access_token', methods=['POST'])

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 299, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002C905DE56C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905DE52D0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function request_response.<locals>.app at 0x000002C905B6EF80>
          └ APIRoute(path='/admin/auth/api/login', name='api_login_for_access_token', methods=['POST'])

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 79, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002C905DE56C0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905DE52D0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000002C905D6BD30>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000002C905DE5750>
          └ <function wrap_app_handling_exceptions at 0x000002C971FB1090>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000002C905DE57E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x000002C905DE52D0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000002C905DE5750>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 74, in app
    response = await func(request)
                     │    └ <starlette.requests.Request object at 0x000002C905D6BD30>
                     └ <function get_request_handler.<locals>.app at 0x000002C905B6EEF0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\fastapi\routing.py", line 278, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000002C971FB32E0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\fastapi\routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'data': <fastapi.security.oauth2.OAuth2PasswordRequestForm object at 0x000002C905D690C0>, 'db': <sqlalchemy.ext.asyncio.sess...
                 │         └ <function api_login_for_access_token at 0x000002C974814430>
                 └ <fastapi.dependencies.models.Dependant object at 0x000002C905B7C190>

  File "F:\share\myproject\mxtt\server\apps\admin\apis\auth.py", line 49, in api_login_for_access_token
    user = await UserDal(db).get_data(telephone=data.username, v_return_none=True)
                 │       │                      │    └ '***********'
                 │       │                      └ <fastapi.security.oauth2.OAuth2PasswordRequestForm object at 0x000002C905D690C0>
                 │       └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002C905D68B50>
                 └ <class 'apps.admin.services.organ.UserDal'>

  File "F:\share\myproject\mxtt\server\core\crud.py", line 89, in get_data
    queryset: ScalarResult = await self.filter_core(
                                   │    └ <function DalBase.filter_core at 0x000002C9752CC8B0>
                                   └ <apps.admin.services.organ.UserDal object at 0x000002C905D69090>

  File "F:\share\myproject\mxtt\server\core\crud.py", line 461, in filter_core
    queryset = await self.db.scalars(sql)
                     │    │  │       └ <sqlalchemy.sql.selectable.Select object at 0x000002C905D6AB60>
                     │    │  └ <function AsyncSession.scalars at 0x000002C973ED7370>
                     │    └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002C905D68B50>
                     └ <apps.admin.services.organ.UserDal object at 0x000002C905D69090>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 572, in scalars
    result = await self.execute(
                   │    └ <function AsyncSession.execute at 0x000002C973ED7250>
                   └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000002C905D68B50>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 455, in execute
    result = await greenlet_spawn(
                   └ <function greenlet_spawn at 0x000002C9724B8DC0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 200, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x000002C9057151C0 (otid=0x000002C97245EEE0) dead>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2308, in execute
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x000002C973E24700>
           └ <sqlalchemy.orm.session.Session object at 0x000002C905D68CA0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2190, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod(<function AbstractORMCompileState.orm_execute_statement at 0x000002C973CCB9A0>)>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x000002C973AA43A0>
             └ <sqlalchemy.engine.base.Connection object at 0x000002C905D689D0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x000002C905D6AB60>>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\sql\elements.py", line 517, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x000002C973AA4670>
           └ <sqlalchemy.engine.base.Connection object at 0x000002C905D689D0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1639, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x000002C973AA4820>
          └ <sqlalchemy.engine.base.Connection object at 0x000002C905D689D0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1848, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x000002C973AA48B0>
           └ <sqlalchemy.engine.base.Connection object at 0x000002C905D689D0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1988, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x000002C973AA4AF0>
    └ <sqlalchemy.engine.base.Connection object at 0x000002C905D689D0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 2344, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ ProgrammingError(1146, "Table 'mxtt.admin_auth_user' doesn't exist")
          │                    │              └ (<class 'asyncmy.errors.ProgrammingError'>, ProgrammingError(1146, "Table 'mxtt.admin_auth_user' doesn't exist"), <traceback ...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'mxtt.admin_auth_user\' doesn\'t exist")')

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x000002C973B23A30>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000002C9746D8610>
    └ <sqlalchemy.engine.base.Connection object at 0x000002C905D689D0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('***********',)
    │      │       └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000002C97474DBD0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002C97257E7A0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 92, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('***********',)
           │    │      │    │              └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000002C97474DCF0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002C97257E7A0>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002C97257E7A0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 130, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
           │                     └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000002C905E03C30>
           └ <_AsyncIoGreenlet object at 0x000002C9057151C0 (otid=0x000002C97245EEE0) dead>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 195, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000002C905E03C30>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 104, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('***********',)
                   │    │               └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000002C97257E7A0>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 494, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 682, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1069, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 644, in read_packet
    packet.raise_for_error()
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x000002C9747C5220>
    └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\MyProjects\\venv\\.venv\\lib\\site-packages\\asyncmy\\errors.cp310-win_amd64...
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x000002C9747C5220>
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

sqlalchemy.exc.ProgrammingError: (asyncmy.errors.ProgrammingError) (1146, "Table 'mxtt.admin_auth_user' doesn't exist")
[SQL: SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickname, admin_auth_user.password, admin_auth_user.gender, admin_auth_user.is_active, admin_auth_user.is_reset_password, admin_auth_user.last_ip, admin_auth_user.last_login, admin_auth_user.is_staff, admin_auth_user.wx_server_openid, admin_auth_user.is_wx_server_openid, admin_auth_user.id, admin_auth_user.create_datetime, admin_auth_user.update_datetime, admin_auth_user.delete_datetime, admin_auth_user.is_delete 
FROM admin_auth_user 
WHERE admin_auth_user.is_delete = false AND admin_auth_user.telephone = %s]
[parameters: ('***********',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-22 12:26:57.970 | ERROR    | core.exception:validation_exception_handler:164 - [{'type': 'string_pattern_mismatch', 'loc': ('body', 'grant_type'), 'msg': "String should match pattern '^password$'", 'input': '', 'ctx': {'pattern': '^password$'}}]
Traceback (most recent call last):

  File "F:\share\myproject\mxtt\server\main.py", line 116, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000014E46AFEF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000014E46AFEF90>
           └ <function get_command at 0x0000014E5DD3A840>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000014E5DD27420>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000014E5DD26520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000014E5DC56450>
         │    └ <function MultiCommand.invoke at 0x0000014E4A056480>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000014E5DC1FBC0>
           │               │       │       └ <function Command.invoke at 0x0000014E4A055E40>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000014E5DC1FBC0>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000014E5DD59300>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000014E5DC1FBC0>
           │   │      │    └ <function run at 0x0000014E5DD58E00>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000014E4A0547C0>
           └ <click.core.Context object at 0x0000014E5DC1FBC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000014E5DD584A0>

  File "F:\share\myproject\mxtt\server\main.py", line 69, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000014E4A08FC40>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000014E4A08FF60>
    └ <uvicorn.server.Server object at 0x0000014E5D8BB7A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000014E4A0C0040>
           │       │   └ <uvicorn.server.Server object at 0x0000014E5D8BB7A0>
           │       └ <function run at 0x0000014E488CE7A0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000014E4A044B40>
           │      └ <function Runner.run at 0x0000014E4893E0C0>
           └ <asyncio.runners.Runner object at 0x0000014E5D02C110>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000014E4893BC40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000014E5D02C110>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000014E489F7920>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000014E4893D9E0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000014E488B5D00>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000014E5DFEB6A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014E5DFEB600>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014E5DDAC5F0>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000014E5DB6F560>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000014E5DFEB6A0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014E5DFEB600>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000014E5DFA50A0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000014E5DBC60C0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000014E5DDAC5F0>
          └ <function wrap_app_handling_exceptions at 0x0000014E49E60EA0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000014E5DFEB9C0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014E5DFEB600>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000014E5DBC60C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000014E5DFEB9C0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014E5DFEB600>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000014E5DBC60C0>>
          └ <fastapi.routing.APIRouter object at 0x0000014E5DBC60C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000014E5DFEB9C0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014E5DFEB600>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Route.handle at 0x0000014E49E623E0>
          └ APIRoute(path='/admin/auth/api/login', name='api_login_for_access_token', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000014E5DFEB9C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014E5DFEB600>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000014E5DE06160>
          └ APIRoute(path='/admin/auth/api/login', name='api_login_for_access_token', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000014E5DFEB9C0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014E5DFEB600>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000014E5DFE5820>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000014E5DFEBA60>
          └ <function wrap_app_handling_exceptions at 0x0000014E49E60EA0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000014E5DFEBBA0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000014E5DFEB600>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000014E5DFEBA60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x0000014E5DFE5820>
                     └ <function get_request_handler.<locals>.app at 0x0000014E5DE06A20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py", line 346, in app
    raise validation_error
          └ RequestValidationError([{'type': 'string_pattern_mismatch', 'loc': ('body', 'grant_type'), 'msg': "String should match patter...

fastapi.exceptions.RequestValidationError: [{'type': 'string_pattern_mismatch', 'loc': ('body', 'grant_type'), 'msg': "String should match pattern '^password$'", 'input': '', 'ctx': {'pattern': '^password$'}}]
2025-07-22 12:28:16.917 | ERROR    | core.exception:all_exception_handler:219 - (asyncmy.errors.ProgrammingError) (1146, "Table 'mxtt.admin_auth_user' doesn't exist")
[SQL: SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickname, admin_auth_user.password, admin_auth_user.gender, admin_auth_user.is_active, admin_auth_user.is_reset_password, admin_auth_user.last_ip, admin_auth_user.last_login, admin_auth_user.is_staff, admin_auth_user.wx_server_openid, admin_auth_user.is_wx_server_openid, admin_auth_user.id, admin_auth_user.create_datetime, admin_auth_user.update_datetime, admin_auth_user.delete_datetime, admin_auth_user.is_delete 
FROM admin_auth_user 
WHERE admin_auth_user.is_delete = false AND admin_auth_user.telephone = %s]
[parameters: ('***********',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
Traceback (most recent call last):

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x0000026D35F93A30>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x0000026D36B485E0>
    └ <sqlalchemy.engine.base.Connection object at 0x0000026D48123F10>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('***********',)
    │      │       └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x0000026D36BBDBD0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026D481BB1F0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 92, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('***********',)
           │    │      │    │              └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026D36BBDCF0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026D481BB1F0>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026D481BB1F0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 130, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
           │                     └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026D480ABF40>
           └ <_AsyncIoGreenlet object at 0x0000026D47E81A00 (otid=0x0000026D348CEEE0) dead>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 195, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026D480ABF40>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 104, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('***********',)
                   │    │               └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026D481BB1F0>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 494, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 682, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1069, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 644, in read_packet
    packet.raise_for_error()
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x0000026D36C35220>
    └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\MyProjects\\venv\\.venv\\lib\\site-packages\\asyncmy\\errors.cp310-win_amd64...
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x0000026D36C35220>
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

asyncmy.errors.ProgrammingError: (1146, "Table 'mxtt.admin_auth_user' doesn't exist")


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "F:\share\myproject\mxtt\server\main.py", line 116, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026D310B3AF0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\main.py", line 311, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026D310B3AF0>
           └ <function get_command at 0x0000026D47DB29E0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 1157, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026D47DED6C0>
           └ <TyperGroup >

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\core.py", line 778, in main
    return _main(
           └ <function _main at 0x0000026D47DEC940>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\core.py", line 216, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026D47E57C40>
         │    └ <function MultiCommand.invoke at 0x0000026D345F2C20>
         └ <TyperGroup >

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 1688, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026D47E79060>
           │               │       │       └ <function Command.invoke at 0x0000026D345F2710>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026D47E79060>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026D47E85750>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 1434, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026D47E79060>
           │   │      │    └ <function run at 0x0000026D47E85360>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026D345F1480>
           └ <click.core.Context object at 0x0000026D47E79060>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\click\core.py", line 783, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\typer\main.py", line 683, in wrapper
    return callback(**use_params)  # type: ignore
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026D47DEF0A0>

  File "F:\share\myproject\mxtt\server\main.py", line 69, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026D3464A7A0>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\MyProjects\\venv\\.venv\\lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026D3464A200>
    └ <uvicorn.server.Server object at 0x0000026D47E79570>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026D3464A290>
           │       │   └ <uvicorn.server.Server object at 0x0000026D47E79570>
           │       └ <function run at 0x0000026D31149B40>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py'>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\runners.py", line 44, in run
    return loop.run_until_complete(main)
           │    │                  └ <coroutine object Server.serve at 0x0000026D47E6D2A0>
           │    └ <function BaseEventLoop.run_until_complete at 0x0000026D32DCE170>
           └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 633, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026D32E7AB90>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 600, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026D32DCFBE0>
    └ <ProactorEventLoop running=True closed=False debug=False>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\base_events.py", line 1896, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026D32D5B2E0>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000026D47F39600>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\uvicorn\middleware\proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026D48...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x0000026D34607130>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x0000026D47F39600>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026D48...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\applications.py", line 123, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026D48...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000026D480E7970>
          └ <fastapi.applications.FastAPI object at 0x0000026D34607130>

> File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000026D4800AB90>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026D480E7940>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x0000026D480E7970>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\cors.py", line 91, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:9000', 'connection': 'keep-alive', 'content-length': '83', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x0000026D4800AB90>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000026D346A1990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026D480E7940>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\cors.py", line 146, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000026D480E7940...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x0000026...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026D480E6F50>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026D480E7940>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000026D48008940>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000026D480E5A80>
                     │    └ <function register_jwt_refresh_middleware.<locals>.jwt_refresh_middleware at 0x0000026D47F3DCF0>
                     └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026D480E6F50>

  File "F:\share\myproject\mxtt\server\core\middleware.py", line 235, in jwt_refresh_middleware
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000026D480E5A80>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000026D48008940>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'mxtt.admin_auth_user\' doesn\'t exist")')

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026D48082290>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026D48081E10>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026D480E6EC0>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026D480E6F50>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 191, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000026D481408B0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x0000026D48122F80>
                     │    └ <function register_request_log_middleware.<locals>.request_log_middleware at 0x0000026D47F3DC60>
                     └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026D480E6EC0>

  File "F:\share\myproject\mxtt\server\core\middleware.py", line 81, in request_log_middleware
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x0000026D48122F80>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x0000026D481408B0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 165, in call_next
    raise app_exc
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'mxtt.admin_auth_user\' doesn\'t exist")')

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\base.py", line 151, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026D48140A60>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026D48140940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026D480E6E90>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026D480E6EC0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026D48140A60>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026D48140940>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026D48121EA0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026D47F389A0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026D480E6E90>
          └ <function wrap_app_handling_exceptions at 0x0000026D34311090>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026D48140D30>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026D48140940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026D47F389A0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 758, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026D48140D30>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026D48140940>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026D47F389A0>>
          └ <fastapi.routing.APIRouter object at 0x0000026D47F389A0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 778, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026D48140D30>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026D48140940>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Route.handle at 0x0000026D34312440>
          └ APIRoute(path='/admin/auth/api/login', name='api_login_for_access_token', methods=['POST'])

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 299, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026D48140D30>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026D48140940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function request_response.<locals>.app at 0x0000026D47F3F010>
          └ APIRoute(path='/admin/auth/api/login', name='api_login_for_access_token', methods=['POST'])

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 79, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026D48140D30>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026D48140940>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x0000026D481235B0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x0000026D48140E50>
          └ <function wrap_app_handling_exceptions at 0x0000026D34311090>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\_exception_handler.py", line 64, in wrapped_app
    raise exc

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026D48140EE0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026D48140940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x0000026D48140E50>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\starlette\routing.py", line 74, in app
    response = await func(request)
                     │    └ <starlette.requests.Request object at 0x0000026D481235B0>
                     └ <function get_request_handler.<locals>.app at 0x0000026D47F3F0A0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\fastapi\routing.py", line 278, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x0000026D343132E0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\fastapi\routing.py", line 191, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'data': <fastapi.security.oauth2.OAuth2PasswordRequestForm object at 0x0000026D48122650>, 'db': <sqlalchemy.ext.asyncio.sess...
                 │         └ <function api_login_for_access_token at 0x0000026D36C84430>
                 └ <fastapi.dependencies.models.Dependant object at 0x0000026D47F39870>

  File "F:\share\myproject\mxtt\server\apps\admin\apis\auth.py", line 49, in api_login_for_access_token
    user = await UserDal(db).get_data(telephone=data.username, v_return_none=True)
                 │       │                      │    └ '***********'
                 │       │                      └ <fastapi.security.oauth2.OAuth2PasswordRequestForm object at 0x0000026D48122650>
                 │       └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000026D481225C0>
                 └ <class 'apps.admin.services.organ.UserDal'>

  File "F:\share\myproject\mxtt\server\core\crud.py", line 89, in get_data
    queryset: ScalarResult = await self.filter_core(
                                   │    └ <function DalBase.filter_core at 0x0000026D3773C8B0>
                                   └ <apps.admin.services.organ.UserDal object at 0x0000026D481220E0>

  File "F:\share\myproject\mxtt\server\core\crud.py", line 461, in filter_core
    queryset = await self.db.scalars(sql)
                     │    │  │       └ <sqlalchemy.sql.selectable.Select object at 0x0000026D48123FD0>
                     │    │  └ <function AsyncSession.scalars at 0x0000026D3634B370>
                     │    └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000026D481225C0>
                     └ <apps.admin.services.organ.UserDal object at 0x0000026D481220E0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 572, in scalars
    result = await self.execute(
                   │    └ <function AsyncSession.execute at 0x0000026D3634B250>
                   └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x0000026D481225C0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 455, in execute
    result = await greenlet_spawn(
                   └ <function greenlet_spawn at 0x0000026D34928DC0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 200, in greenlet_spawn
    result = context.throw(*sys.exc_info())
             │       │      │   └ <built-in function exc_info>
             │       │      └ <module 'sys' (built-in)>
             │       └ <method 'throw' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x0000026D47E81A00 (otid=0x0000026D348CEEE0) dead>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2308, in execute
    return self._execute_internal(
           │    └ <function Session._execute_internal at 0x0000026D36294700>
           └ <sqlalchemy.orm.session.Session object at 0x0000026D48122B60>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\orm\session.py", line 2190, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                   │      │                 └ <classmethod(<function AbstractORMCompileState.orm_execute_statement at 0x0000026D3613B9A0>)>
                   │      └ <class 'sqlalchemy.orm.context.ORMSelectCompileState'>
                   └ typing.Any

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
             │    └ <function Connection.execute at 0x0000026D35F143A0>
             └ <sqlalchemy.engine.base.Connection object at 0x0000026D48123F10>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.selectable.Select object at 0x0000026D48123FD0>>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\sql\elements.py", line 517, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x0000026D35F14670>
           └ <sqlalchemy.engine.base.Connection object at 0x0000026D48123F10>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1639, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x0000026D35F14820>
          └ <sqlalchemy.engine.base.Connection object at 0x0000026D48123F10>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1848, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x0000026D35F148B0>
           └ <sqlalchemy.engine.base.Connection object at 0x0000026D48123F10>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1988, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x0000026D35F14AF0>
    └ <sqlalchemy.engine.base.Connection object at 0x0000026D48123F10>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 2344, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ ProgrammingError(1146, "Table 'mxtt.admin_auth_user' doesn't exist")
          │                    │              └ (<class 'asyncmy.errors.ProgrammingError'>, ProgrammingError(1146, "Table 'mxtt.admin_auth_user' doesn't exist"), <traceback ...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ ProgrammingError('(asyncmy.errors.ProgrammingError) (1146, "Table \'mxtt.admin_auth_user\' doesn\'t exist")')

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\base.py", line 1969, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x0000026D35F93A30>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x0000026D36B485E0>
    └ <sqlalchemy.engine.base.Connection object at 0x0000026D48123F10>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\engine\default.py", line 922, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('***********',)
    │      │       └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x0000026D36BBDBD0>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026D481BB1F0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 92, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('***********',)
           │    │      │    │              └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026D36BBDCF0>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026D481BB1F0>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026D481BB1F0>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 130, in await_only
    return current.driver.switch(awaitable)  # type: ignore[no-any-return]
           │                     └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026D480ABF40>
           └ <_AsyncIoGreenlet object at 0x0000026D47E81A00 (otid=0x0000026D348CEEE0) dead>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 195, in greenlet_spawn
    value = await result
                  └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x0000026D480ABF40>

  File "C:\Users\<USER>\MyProjects\venv\.venv\lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 104, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('***********',)
                   │    │               └ 'SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickn...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x0000026D481BB1F0>

  File "asyncmy\\cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy\\cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy\\connection.pyx", line 494, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy\\connection.pyx", line 682, in _read_query_result
    await result.read()
  File "asyncmy\\connection.pyx", line 1069, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy\\connection.pyx", line 644, in read_packet
    packet.raise_for_error()
  File "asyncmy\\protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy\\protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x0000026D36C35220>
    └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\MyProjects\\venv\\.venv\\lib\\site-packages\\asyncmy\\errors.cp310-win_amd64...
  File "asyncmy\\errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x0000026D36C35220>
  File "asyncmy\\errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

sqlalchemy.exc.ProgrammingError: (asyncmy.errors.ProgrammingError) (1146, "Table 'mxtt.admin_auth_user' doesn't exist")
[SQL: SELECT admin_auth_user.avatar, admin_auth_user.telephone, admin_auth_user.email, admin_auth_user.name, admin_auth_user.nickname, admin_auth_user.password, admin_auth_user.gender, admin_auth_user.is_active, admin_auth_user.is_reset_password, admin_auth_user.last_ip, admin_auth_user.last_login, admin_auth_user.is_staff, admin_auth_user.wx_server_openid, admin_auth_user.is_wx_server_openid, admin_auth_user.id, admin_auth_user.create_datetime, admin_auth_user.update_datetime, admin_auth_user.delete_datetime, admin_auth_user.is_delete 
FROM admin_auth_user 
WHERE admin_auth_user.is_delete = false AND admin_auth_user.telephone = %s]
[parameters: ('***********',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-07-22 12:34:08.902 | ERROR    | core.exception:validation_exception_handler:164 - [{'type': 'string_pattern_mismatch', 'loc': ('body', 'grant_type'), 'msg': "String should match pattern '^password$'", 'input': '', 'ctx': {'pattern': '^password$'}}]
Traceback (most recent call last):

  File "F:\share\myproject\mxtt\server\main.py", line 116, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x00000285C520EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x00000285C520EF90>
           └ <function get_command at 0x00000285DC3AD3A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x00000285DC38DF80>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x00000285DC38D080>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x00000285DBF23470>
         │    └ <function MultiCommand.invoke at 0x00000285C86D6480>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x00000285DC288440>
           │               │       │       └ <function Command.invoke at 0x00000285C86D5E40>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x00000285DC288440>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x00000285DC3AFC40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x00000285DC288440>
           │   │      │    └ <function run at 0x00000285DC3AF740>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x00000285C86D47C0>
           └ <click.core.Context object at 0x00000285DC288440>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x00000285DC3AEDE0>

  File "F:\share\myproject\mxtt\server\main.py", line 69, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x00000285C870FC40>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x00000285C870FF60>
    └ <uvicorn.server.Server object at 0x00000285DC3C0560>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x00000285C8740040>
           │       │   └ <uvicorn.server.Server object at 0x00000285DC3C0560>
           │       └ <function run at 0x00000285C6F4E7A0>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x00000285C86C4B40>
           │      └ <function Runner.run at 0x00000285C6FBE0C0>
           └ <asyncio.runners.Runner object at 0x00000285DBEC8680>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x00000285C6FBBC40>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x00000285DBEC8680>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x00000285C7077920>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x00000285C6FBD9E0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x00000285C6F35D00>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000285DC651F80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000285DC651EE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000285DC522AE0>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x00000285DC49E450>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000285DC651F80>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000285DC651EE0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x00000285DC60CCE0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x00000285DBDD66C0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x00000285DC522AE0>
          └ <function wrap_app_handling_exceptions at 0x00000285C84E0EA0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000285DC6522A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000285DC651EE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x00000285DBDD66C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000285DC6522A0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000285DC651EE0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x00000285DBDD66C0>>
          └ <fastapi.routing.APIRouter object at 0x00000285DBDD66C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000285DC6522A0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000285DC651EE0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Route.handle at 0x00000285C84E23E0>
          └ APIRoute(path='/admin/auth/api/login', name='api_login_for_access_token', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000285DC6522A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000285DC651EE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <function request_response.<locals>.app at 0x00000285DC46CA40>
          └ APIRoute(path='/admin/auth/api/login', name='api_login_for_access_token', methods=['POST'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000285DC6522A0>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000285DC651EE0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x00000285DC55A840>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x00000285DC652340>
          └ <function wrap_app_handling_exceptions at 0x00000285C84E0EA0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x00000285DC652480>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000285DC651EE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x00000285DC652340>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x00000285DC55A840>
                     └ <function get_request_handler.<locals>.app at 0x00000285DC46D300>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\fastapi\routing.py", line 346, in app
    raise validation_error
          └ RequestValidationError([{'type': 'string_pattern_mismatch', 'loc': ('body', 'grant_type'), 'msg': "String should match patter...

fastapi.exceptions.RequestValidationError: [{'type': 'string_pattern_mismatch', 'loc': ('body', 'grant_type'), 'msg': "String should match pattern '^password$'", 'input': '', 'ctx': {'pattern': '^password$'}}]
