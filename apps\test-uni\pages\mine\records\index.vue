<template>
  <view class="records-container">
    <!-- 空状态 -->
    <u-empty
      v-if="backtestList.length === 0"
      mode="data"
      icon="http://cdn.uviewui.com/uview/empty/data.png"
      text="暂无回测记录"
    >
    </u-empty>

    <!-- 回测记录列表 -->
    <view class="records-list" v-else>
      <view
        class="record-item"
        v-for="(item, index) in backtestList"
        :key="index"
      >
        <view class="record-header">
          <text class="strategy-name">{{ item.strategyName }}</text>
          <text class="backtest-time">{{ item.createTime }}</text>
        </view>
        <view class="record-stats">
          <view class="stat-item">
            <text>收益率:</text>
            <text :class="item.profitRate >= 0 ? 'profit' : 'loss'">
              {{ item.profitRate }}%
            </text>
          </view>
          <view class="stat-item">
            <text>最大回撤:</text>
            <text>{{ item.maxDrawdown }}%</text>
          </view>
          <view class="stat-item">
            <text>胜率:</text>
            <text>{{ item.winRate }}%</text>
          </view>
        </view>
        <view class="record-actions">
          <u-button
            type="primary"
            size="mini"
            @click="viewDetail(item)"
          >查看详情</u-button>
          <u-button
            type="error"
            size="mini"
            @click="deleteRecord(item.id)"
          >删除</u-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      backtestList: [
        {
          id: 1,
          strategyName: 'MACD金叉策略',
          createTime: '2025-04-20 14:30',
          profitRate: 15.6,
          maxDrawdown: 8.2,
          winRate: 62.5
        },
        {
          id: 2,
          strategyName: 'RSI超买超卖策略',
          createTime: '2025-04-18 10:15',
          profitRate: -3.2,
          maxDrawdown: 12.5,
          winRate: 45.8
        }
      ]
    }
  },
  methods: {
    viewDetail(item) {
      uni.navigateTo({
        url: `/pages/mine/records/detail?id=${item.id}`
      })
    },
    deleteRecord(id) {
      this.$modal.confirm('确定删除该回测记录吗？').then(() => {
        this.backtestList = this.backtestList.filter(item => item.id !== id)
        this.$modal.showToast('删除成功')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.records-container {
  padding: 20rpx;

  .records-list {
    .record-item {
      background-color: #fff;
      border-radius: 16rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;

      .record-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15rpx;

        .strategy-name {
          font-size: 32rpx;
          font-weight: bold;
        }

        .backtest-time {
          font-size: 24rpx;
          color: #999;
        }
      }

      .record-stats {
        display: flex;
        margin-bottom: 15rpx;

        .stat-item {
          margin-right: 30rpx;
          font-size: 26rpx;

          .profit {
            color: #f56c6c;
          }

          .loss {
            color: #67c23a;
          }
        }
      }

      .record-actions {
        display: flex;
        justify-content: flex-end;

        .u-button {
          margin-left: 20rpx;
        }
      }
    }
  }
}
</style>