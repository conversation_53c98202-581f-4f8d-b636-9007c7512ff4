{"name": "vue-element-plus-admin", "version": "2.7.0", "description": "一套基于vue3、element-plus、typesScript、vite4的后台集成方案。", "author": "Archer <<EMAIL>>", "private": false, "scripts": {"i": "pnpm install", "dev": "pnpm vite --mode dev", "ts:check": "pnpm vue-tsc --noEmit --skipLib<PERSON><PERSON>ck", "build:pro": "pnpm vite build --mode pro", "build:dev": "pnpm vite build --mode dev", "serve:pro": "pnpm vite preview --mode pro", "serve:dev": "pnpm vite preview --mode dev", "npm:check": "pnpx npm-check-updates -u", "clean": "pnpx rimraf node_modules", "clean:cache": "pnpx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,vue,html,md}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "p": "plop", "icon": "esno ./scripts/icon.ts"}, "dependencies": {"@iconify/iconify": "3.1.1", "@iconify/vue": "4.1.1", "@vueuse/core": "10.9.0", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.10", "@zxcvbn-ts/core": "3.0.4", "animate.css": "4.1.1", "axios": "1.6.7", "cropperjs": "1.6.1", "dayjs": "1.11.10", "driver.js": "1.3.1", "echarts": "5.5.0", "echarts-wordcloud": "2.1.0", "element-plus": "2.5.6", "lodash-es": "4.17.21", "mitt": "3.0.1", "nprogress": "0.2.0", "pinia": "2.1.7", "pinia-plugin-persistedstate": "3.2.1", "qrcode": "1.5.3", "qs": "6.11.2", "url": "0.11.3", "vue": "3.4.20", "vue-draggable-plus": "0.3.5", "vue-i18n": "9.9.1", "vue-json-pretty": "2.3.0", "vue-router": "4.3.0", "vue-types": "5.1.1", "xgplayer": "3.0.13"}, "devDependencies": {"@amap/amap-jsapi-loader": "1.0.1", "@commitlint/cli": "19.0.1", "@commitlint/config-conventional": "19.0.0", "@iconify/json": "2.2.187", "@intlify/unplugin-vue-i18n": "2.0.0", "@kjgl77/datav-vue3": "1.6.1", "@types/fs-extra": "11.0.4", "@types/inquirer": "9.0.7", "@types/lodash-es": "4.17.12", "@types/node": "20.11.21", "@types/nprogress": "0.2.3", "@types/qrcode": "1.5.5", "@types/qs": "6.9.12", "@types/sortablejs": "1.15.8", "@typescript-eslint/eslint-plugin": "7.1.0", "@typescript-eslint/parser": "7.1.0", "@unocss/transformer-variant-group": "0.58.5", "@vitejs/plugin-legacy": "5.3.1", "@vitejs/plugin-vue": "5.0.4", "@vitejs/plugin-vue-jsx": "3.1.0", "autoprefixer": "10.4.17", "chalk": "5.3.0", "consola": "3.2.3", "cron-validate": "1.4.5", "eslint": "^8.57.0", "eslint-config-prettier": "9.1.0", "eslint-define-config": "2.1.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-vue": "9.22.0", "esno": "4.0.0", "fs-extra": "11.2.0", "inquirer": "9.2.15", "less": "4.2.0", "lint-staged": "15.2.2", "lodash": "4.17.21", "moment": "2.30.1", "plop": "4.0.1", "postcss": "8.4.35", "postcss-html": "1.6.0", "postcss-less": "6.0.0", "prettier": "3.2.5", "rimraf": "5.0.5", "rollup": "4.12.0", "rollup-plugin-visualizer": "5.12.0", "stylelint": "16.2.1", "stylelint-config-html": "1.1.0", "stylelint-config-recommended": "14.0.0", "stylelint-config-standard": "36.0.0", "stylelint-order": "6.0.4", "terser": "5.28.1", "typescript": "5.3.3", "unocss": "0.58.5", "vite": "5.1.4", "vite-plugin-ejs": "1.7.0", "vite-plugin-eslint": "1.8.1", "vite-plugin-progress": "0.0.7", "vite-plugin-purge-icons": "0.10.0", "vite-plugin-style-import": "2.0.0", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-url-copy": "1.1.3", "vue-draggable-plus": "0.2.6", "vue-tsc": "1.8.27", "vue3-json-viewer": "2.2.2", "zipson": "0.2.12"}, "packageManager": "pnpm@8.1.0", "engines": {"node": ">=18 <19", "pnpm": ">=8.1.0 <10.0.0"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/kailong321200875/vue-element-plus-admin.git"}, "bugs": {"url": "https://github.com/kailong321200875/vue-element-plus-admin/issues"}, "homepage": "https://github.com/kailong321200875/vue-element-plus-admin"}