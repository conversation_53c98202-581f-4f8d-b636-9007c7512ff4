<template>
  <div class="login-page">
    <div class="login-header">
      <h1 class="login-title">量化回测系统</h1>
      <p class="login-subtitle">专业的量化交易回测平台</p>
    </div>

    <van-tabs v-model="activeTab" animated swipeable class="login-tabs">
      <van-tab title="登录" name="login">
        <van-form @submit="onLogin" class="login-form">
          <van-cell-group inset class="form-cell-group">
            <van-field
              v-model="loginForm.telephone"
              name="telephone"
              label="手机号"
              placeholder="请输入手机号"
              :rules="[{ required: true, message: '请输入手机号' }]"
              class="login-field"
            />
            <van-field
              v-model="loginForm.password"
              type="password"
              name="password"
              label="密码"
              placeholder="请输入密码"
              :rules="[{ required: true, message: '请输入密码' }]"
              class="login-field"
            />
          </van-cell-group>

          <div class="form-actions">
            <van-button
              round
              block
              type="primary"
              native-type="submit"
              :loading="loginLoading"
            >
              登录
            </van-button>
            <van-button
              round
              block
              type="primary"
              @click="onGuestLogin"
              :loading="guestLoading"
              class="guest-button"
            >
              游客模式
            </van-button>
          </div>
        </van-form>
      </van-tab>

      <van-tab title="注册" name="register">
        <van-form @submit="onRegister" class="login-form">
          <van-cell-group inset class="form-cell-group">
            <van-field
              v-model="registerForm.telephone"
              name="telephone"
              label="手机号"
              placeholder="请输入手机号"
              :rules="[{ required: true, message: '请输入手机号' }]"
              class="login-field"
            />
            <van-field
              v-model="registerForm.code"
              center
              clearable
              label="验证码"
              placeholder="请输入验证码"
              :rules="[{ required: true, message: '请输入验证码' }]"
              class="login-field"
            >
              <template #button>
                <van-button
                  size="small"
                  type="primary"
                  @click="sendVerificationCode"
                  :disabled="codeSending || cooldown > 0"
                  class="verification-code-button"
                >
                  {{ cooldown > 0 ? `${cooldown}秒后重试` : '发送验证码' }}
                </van-button>
              </template>
            </van-field>
            <van-field
              v-model="registerForm.password"
              type="password"
              name="password"
              label="密码"
              placeholder="请输入密码"
              :rules="[{ required: true, message: '请输入密码' }]"
              class="login-field"
            />
            <van-field
              v-model="registerForm.confirmPassword"
              type="password"
              name="confirmPassword"
              label="确认密码"
              placeholder="请再次输入密码"
              :rules="[
                { required: true, message: '请确认密码' },
                { validator: validateConfirmPassword, message: '两次输入的密码不一致' }
              ]"
              class="login-field"
            />
          </van-cell-group>

          <div class="form-actions">
            <van-button
              round
              block
              type="primary"
              native-type="submit"
              :loading="registerLoading"
            >
              注册
            </van-button>
          </div>
        </van-form>
      </van-tab>
    </van-tabs>

    <div class="login-footer">
      <p>登录即表示您同意<a href="#" @click.prevent="showAgreement">《用户协议》</a>和<a href="#" @click.prevent="showPrivacy">《隐私政策》</a></p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { useUserStore } from '@/stores/user'
// 实际项目中需要导入这些API
// import { login, register, sendCode } from '@/api/user'

// 路由和状态
const router = useRouter()
const userStore = useUserStore()

// 表单状态
const activeTab = ref('login')
const loginForm = ref({
  telephone: '',
  password: '',
  method: 'password'
})
const registerForm = ref({
  telephone: '',
  code: '',
  password: '',
  confirmPassword: ''
})

// 加载状态
const loginLoading = ref(false)
const registerLoading = ref(false)
const guestLoading = ref(false)
const codeSending = ref(false)
const cooldown = ref(0)
let cooldownTimer = null

// 验证确认密码
const validateConfirmPassword = (val) => {
  return val === registerForm.value.password
}

// 登录方法
const onLogin = async () => {
  try {
    loginLoading.value = true

    // 模拟登录成功
    const mockLoginSuccess = true

    if (mockLoginSuccess) {
      // 模拟设置token (实际项目中应该从API获取)
      const mockToken = 'mock_token_' + Date.now()
      const mockRefreshToken = 'mock_refresh_token_' + Date.now()

      // 更新store中的token
      userStore.token = mockToken
      userStore.refreshToken = mockRefreshToken

      // 模拟用户信息
      userStore.userInfo = {
        id: 1001,
        nickname: loginForm.value.telephone || '量化用户',
        avatar: '',
        riskLevel: 3,
        computePower: 10, // 初始赠送10点算力
        isGuest: false,
        settings: {
          defaultCapital: 100000,
          defaultPeriod: 30
        }
      }

      showToast({
        type: 'success',
        message: '登录成功',
        onClose: () => {
          router.replace('/testnow')
        }
      })
    } else {
      showToast({
        type: 'fail',
        message: '登录失败，请重试'
      })
    }

    /* 实际API调用代码（暂时注释）
    const res = await login({
      telephone: loginForm.value.telephone,
      password: loginForm.value.password,
      method: 'password'
    })

    if (res.code === 200) {
      userStore.token = res.data.access_token
      userStore.refreshToken = res.data.refresh_token
      await userStore.fetchUserInfo()

      showToast({
        type: 'success',
        message: '登录成功',
        onClose: () => {
          router.replace('/testnow')
        }
      })
    } else {
      showToast({
        type: 'fail',
        message: res.msg || '登录失败，请重试'
      })
    }
    */
  } catch (error) {
    console.error('登录失败:', error)
    showToast({
      type: 'fail',
      message: '登录失败，请重试'
    })
  } finally {
    loginLoading.value = false
  }
}

// 游客登录
const onGuestLogin = () => {
  guestLoading.value = true

  // 设置游客状态
  userStore.userInfo.isGuest = true
  userStore.userInfo.nickname = '游客'
  userStore.userInfo.computePower = 0

  setTimeout(() => {
    guestLoading.value = false
    showToast({
      type: 'success',
      message: '已进入游客模式',
      onClose: () => {
        router.replace('/testnow')
      }
    })
  }, 1000)
}

// 发送验证码
const sendVerificationCode = async () => {
  if (!registerForm.value.telephone) {
    showToast('请输入手机号')
    return
  }

  try {
    codeSending.value = true

    // 模拟发送验证码成功
    const mockSendCodeSuccess = true

    if (mockSendCodeSuccess) {
      // 自动填充验证码（仅用于模拟）
      registerForm.value.code = '123456'

      showToast({
        type: 'success',
        message: '验证码已发送（模拟：123456）'
      })

      // 开始倒计时
      cooldown.value = 60
      cooldownTimer = setInterval(() => {
        cooldown.value--
        if (cooldown.value <= 0) {
          clearInterval(cooldownTimer)
        }
      }, 1000)
    } else {
      showToast({
        type: 'fail',
        message: '发送验证码失败'
      })
    }

    /* 实际API调用代码（暂时注释）
    const res = await sendCode({
      telephone: registerForm.value.telephone,
      type: 'register'
    })

    if (res.code === 200) {
      showToast({
        type: 'success',
        message: '验证码已发送'
      })

      // 开始倒计时
      cooldown.value = 60
      cooldownTimer = setInterval(() => {
        cooldown.value--
        if (cooldown.value <= 0) {
          clearInterval(cooldownTimer)
        }
      }, 1000)
    } else {
      showToast({
        type: 'fail',
        message: res.msg || '发送验证码失败'
      })
    }
    */
  } catch (error) {
    console.error('发送验证码失败:', error)
    showToast({
      type: 'fail',
      message: '发送验证码失败'
    })
  } finally {
    codeSending.value = false
  }
}

// 注册方法
const onRegister = async () => {
  if (registerForm.value.password !== registerForm.value.confirmPassword) {
    showToast('两次输入的密码不一致')
    return
  }

  try {
    registerLoading.value = true

    // 模拟注册成功
    const mockRegisterSuccess = true

    if (mockRegisterSuccess) {
      showToast({
        type: 'success',
        message: '注册成功，请登录'
      })

      // 清空注册表单，切换到登录标签
      registerForm.value = {
        telephone: registerForm.value.telephone,
        code: '',
        password: '',
        confirmPassword: ''
      }
      activeTab.value = 'login'
      loginForm.value.telephone = registerForm.value.telephone
    } else {
      showToast({
        type: 'fail',
        message: '注册失败，请重试'
      })
    }

    /* 实际API调用代码（暂时注释）
    const res = await register({
      telephone: registerForm.value.telephone,
      password: registerForm.value.password,
      code: registerForm.value.code
    })

    if (res.code === 200) {
      showToast({
        type: 'success',
        message: '注册成功，请登录'
      })

      // 清空注册表单，切换到登录标签
      registerForm.value = {
        telephone: registerForm.value.telephone,
        code: '',
        password: '',
        confirmPassword: ''
      }
      activeTab.value = 'login'
      loginForm.value.telephone = registerForm.value.telephone
    } else {
      showToast({
        type: 'fail',
        message: res.msg || '注册失败，请重试'
      })
    }
    */
  } catch (error) {
    console.error('注册失败:', error)
    showToast({
      type: 'fail',
      message: '注册失败，请重试'
    })
  } finally {
    registerLoading.value = false
  }
}

// 显示用户协议
const showAgreement = () => {
  showDialog({
    title: '用户协议',
    message: '这是用户协议内容...',
    confirmButtonText: '我已阅读并同意'
  })
}

// 显示隐私政策
const showPrivacy = () => {
  showDialog({
    title: '隐私政策',
    message: '这是隐私政策内容...',
    confirmButtonText: '我已阅读并同意'
  })
}

// 生命周期钩子
onMounted(() => {
  // 如果已登录，直接跳转到首页
  if (userStore.isLoggedIn) {
    router.replace('/testnow')
  }
})

onUnmounted(() => {
  // 清除定时器
  if (cooldownTimer) {
    clearInterval(cooldownTimer)
  }
})
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  padding: 20px;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
}

.login-header {
  text-align: center;
  margin: 30px 0;
}

.login-title {
  font-size: 28px;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.login-subtitle {
  font-size: 16px;
  color: var(--text-color-secondary);
}

.login-form {
  margin-top: 20px;
}

.form-actions {
  margin-top: 30px;
  padding: 0 16px;
}

.van-button {
  height: 44px;
  border-radius: 22px;
}

.guest-button {
  margin-top: 16px;
}

/* 确保按钮内容垂直居中 */
.van-button__content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保按钮文字和图标为白色 */
.van-button--primary:not(.van-button--plain) {
  color: var(--text-color-inverse);
}

.van-button--primary:not(.van-button--plain) .van-icon {
  color: var(--text-color-inverse);
}

/* 验证码按钮样式 */
.verification-code-button {
  height: 32px;
  border-radius: 16px;
  font-size: var(--font-size-sm);
  padding: 0 12px;
}

.login-footer {
  margin-top: auto;
  text-align: center;
  padding: 20px 0;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.login-footer a {
  color: var(--primary-color);
  text-decoration: none;
}

/* 表单样式优化 */
.form-cell-group {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.login-field {
  padding: 12px 16px;
}

.login-field .van-field__label {
  width: 70px;
  color: var(--text-color-regular);
}

.login-field .van-field__control {
  height: 24px;
  font-size: var(--font-size-md);
}

/* 标签页样式优化 */
:deep(.van-tabs__nav) {
  padding: 0 20px;
}

:deep(.van-tab) {
  font-size: var(--font-size-lg);
  padding: 0 12px;
  line-height: 44px;
  height: 44px;
}

:deep(.van-tabs__line) {
  bottom: 15px;
  height: 3px;
  border-radius: 3px;
}

/* 登录标签页整体样式 */
.login-tabs {
  margin-top: 10px;
}

:deep(.van-tabs__wrap) {
  height: 50px;
}

:deep(.van-tabs__content) {
  padding-top: 10px;
}
</style>
