#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : 认证核心

from typing import Annotated
from sqlalchemy.ext.asyncio import AsyncSession
from apps.front.organ.crud import UserDal, AppDal
from apps.front.organ.models import AppUser
from core.exception import CustomException
from utils import status
from .validation import AuthValidation
from fastapi import Request, Depends
from config import settings
from core.database import db_getter
from ..schemas.auth import Auth


class OpenAuth(AuthValidation):
    """
    开放认证，无认证也可以访问
    认证了以后可以获取到用户信息，无认证则获取不到
    """

    async def __call__(
        self,
        request: Request,
        token: str | None = Depends(settings.oauth2_scheme),
        db: AsyncSession = Depends(db_getter)
    ) -> Auth:
        """每次调用依赖此类的接口会执行该方法"""
        if not settings.OAUTH_ENABLE:
            return Auth(db=db)
        if not token:
            return Auth(db=db)

        try:
            user_id, app_id = self.validate_token(request, token)
            user = await UserDal(db).get_data(id=user_id, v_return_none=True)
            return await self.validate_user(request, user, app_id, db)
        except CustomException:
            return Auth(db=db)


class AllUserAuth(AuthValidation):
    """
    支持所有用户认证
    获取用户基本信息
    """

    async def __call__(
        self,
        request: Request,
        token: str = Depends(settings.oauth2_scheme),
        db: AsyncSession = Depends(db_getter)
    ) -> Auth:
        """每次调用依赖此类的接口会执行该方法"""
        if not settings.OAUTH_ENABLE:
            return Auth(db=db)
        user_id, app_id = self.validate_token(request, token)
        user = await UserDal(db).get_data(id=user_id, v_return_none=True)
        return await self.validate_user(request, user, app_id, db)


class GuestAuth(AuthValidation):
    """
    游客认证
    如果应用允许游客访问，则无需认证也可访问
    如果应用不允许游客访问，则需要用户认证
    """

    async def __call__(
        self,
        request: Request,
        app_id: int,
        token: str | None = Depends(settings.oauth2_scheme),
        db: AsyncSession = Depends(db_getter)
    ) -> Auth:
        """每次调用依赖此类的接口会执行该方法"""
        if not settings.OAUTH_ENABLE:
            return Auth(db=db)

        # 如果有token，验证用户
        if token:
            try:
                user_id, token_app_id = self.validate_token(request, token)
                if token_app_id != app_id:
                    raise CustomException(msg="应用ID不匹配", code=status.HTTP_403_FORBIDDEN)
                user = await UserDal(db).get_data(id=user_id, v_return_none=True)
                return await self.validate_user(request, user, app_id, db)
            except CustomException:
                pass

        # 验证应用是否允许游客访问
        app = await AppDal(db).get_app_full_info(app_id)
        if not app.settings or not app.settings.allow_guest:
            raise CustomException(msg="该应用不允许游客访问", code=status.HTTP_403_FORBIDDEN)

        return Auth(db=db)
