import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAssetStore = defineStore('asset', () => {
  const assets = ref([])
  const recentSelected = ref([])

  const fetchAssets = async () => {
    // 实际应从API获取数据
    assets.value = [
      { code: '600519', name: '贵州茅台', type: 'stock' },
      { code: '000858', name: '五粮液', type: 'stock' },
      { code: '300750', name: '宁德时代', type: 'stock' },
      { code: '161725', name: '招商中证白酒', type: 'fund' },
    ]
  }

  const addRecent = (asset) => {
    recentSelected.value = [
      asset,
      ...recentSelected.value.filter(a => a.code !== asset.code)
    ].slice(0, 5) // 保留最近5个
  }

  return {
    assets,
    recentSelected,
    fetchAssets,
    addRecent
  }
})
