import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import Vant from 'vant'
import 'vant/lib/index.css'
import './styles/global.css'
import { registerSW } from 'virtual:pwa-register'
import { initWechatEnvironment, ensureScrollable } from './utils/wechat'

// 初始化微信环境
initWechatEnvironment()

// 确保页面可以滚动
ensureScrollable()

// 添加滚动检测，确保在DOM变化后页面仍然可以滚动
document.addEventListener('DOMContentLoaded', () => {
  ensureScrollable()

  // 监听DOM变化，确保页面始终可滚动
  const observer = new MutationObserver(() => {
    setTimeout(ensureScrollable, 100)
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true
  })
})

// Register service worker for PWA
const updateSW = registerSW({
  onNeedRefresh() {
    // Show a notification to the user that there's an update available
    console.log('New content available, click on reload button to update.')
  },
  onOfflineReady() {
    // Show a notification to the user that the app is ready for offline use
    console.log('App ready to work offline')
  }
})

// Create Pinia instance with persistence plugin
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// Create Vue app
const app = createApp(App)

// Register plugins
app.use(router)
app.use(pinia)
app.use(Vant)

// Make updateSW available globally
app.config.globalProperties.$updateSW = updateSW

// Error handling
app.config.errorHandler = (err, _vm, info) => {
  console.error('Vue Error:', err)
  console.error('Info:', info)
  // Here you could add error reporting to a service like Sentry
}

// Performance tracking in development
if (import.meta.env.DEV) {
  app.config.performance = true
}

// Mount app
app.mount('#app')
