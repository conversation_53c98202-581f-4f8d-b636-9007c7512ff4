#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : pydantic 模型，用于数据库序列化操作

from pydantic import BaseModel, ConfigDict, field_validator
from pydantic_core.core_schema import FieldValidationInfo
from core.data_types import Telephone, DatetimeStr, Email


class User(BaseModel):
    """基础用户模型"""
    telephone: Telephone
    nickname: str | None = None
    avatar: str | None = None
    is_active: bool | None = True
    gender: str | None = "0"
    wx_openid: str | None = None
    wx_unionid: str | None = None
    email: Email | None = None


class UserIn(User):
    """创建用户"""
    password: str | None = None
    app_id: int  # 关联的应用ID


class UserUpdateBaseInfo(BaseModel):
    """更新用户基本信息"""
    nickname: str | None = None
    gender: str | None = "0"
    email: Email | None = None


class UserUpdate(User):
    """更新用户详细信息"""
    telephone: Telephone | None = None
    nickname: str | None = None
    avatar: str | None = None
    is_active: bool | None = True
    gender: str | None = "0"
    email: Email | None = None


class UserSimpleOut(User):
    """用户基本信息输出"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    update_datetime: DatetimeStr
    create_datetime: DatetimeStr
    last_login: DatetimeStr | None = None
    last_ip: str | None = None


class UserOut(UserSimpleOut):
    """用户详细信息输出"""
    model_config = ConfigDict(from_attributes=True)

    app_id: int


class ResetPwd(BaseModel):
    """重置密码"""
    password: str
    password_two: str

    @field_validator('password_two')
    def check_passwords_match(cls, v, info: FieldValidationInfo):
        if 'password' in info.data and v != info.data['password']:
            raise ValueError('两次密码不一致!')
        return v
