<template>
  <div>
    <!-- 已选信息（可折叠） -->
    <div v-if="strategy" class="card info-card">
      <van-collapse v-model="activeCollapse" class="info-collapse">
        <van-collapse-item name="info" class="info-item">
          <template #title>
            <div class="collapse-title">
              <span>已选信息</span>
            </div>
          </template>

          <div class="selected-info-content">
            <!-- 回测策略 -->
            <div class="info-section">
              <h4 class="info-subtitle">（一）回测策略：</h4>
              <div class="info-item-row">
                <span class="info-label">策略名称：</span>
                <span class="info-value">{{ strategy.name }}（{{ getAssetTypeText(strategy) }}）</span>
              </div>
              <div class="info-item-row">
                <span class="info-label">策略标签：</span>
                <span class="info-value">
                  {{ getStrategyTypeText(strategy.type) }}、
                  <span v-for="(market, index) in strategy.markets" :key="market">
                    {{ market }}{{ index < strategy.markets.length - 1 ? '、' : '' }}
                  </span>
                </span>
              </div>
              <div class="info-item-row">
                <span class="info-label">策略简介：</span>
                <span class="info-value">{{ strategy.description }}</span>
              </div>
            </div>

            <!-- 回测标的 -->
            <div class="info-section">
              <h4 class="info-subtitle">（二）回测标的：</h4>
              <div class="info-item-row">
                <span class="info-label">标的类型：</span>
                <span class="info-value">{{ getAssetTypeText(strategy) }}</span>
              </div>
              <div class="info-item-row">
                <span class="info-label">标的选择：</span>
                <span class="info-value">
                  <template v-if="isMultiAsset">
                    <span v-for="(asset, index) in assetList" :key="asset.code">
                      {{ asset.name }}{{ index < assetList.length - 1 ? '、' : '' }}
                    </span>
                    （共 {{ assetCount }} 个标的）
                  </template>
                  <template v-else>
                    {{ asset?.name || '-' }}
                  </template>
                </span>
              </div>
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>

    <van-form @submit="saveParameters" ref="formRef">
      <!-- 基础参数 -->
      <div class="card parameter-card">
        <h3 class="section-title-underline">基础参数</h3>

        <!-- 初始资金 -->
        <van-field
          v-model.number="formData.initialCapital"
          type="number"
          label="初始资金"
          placeholder="请输入初始资金"
          :rules="[{ required: true, message: '请输入初始资金', validator: validateCapital }]"
          class="parameter-field"
        >
          <template #right-icon>
            <span class="unit-text">元</span>
          </template>
        </van-field>

        <!-- 回测期间 -->
        <div class="date-range-title">
          回测期间
          <span v-if="isValidDateRange" class="date-range-days-inline">
            （{{ calculateDaysFromInputs() }}天）
          </span>
        </div>

        <div class="date-range-inputs">
          <!-- 开始日期 -->
          <SimpleDateInput
            v-model="dateInputs.startDate"
            placeholder="YYYY-MM-DD"
            :max-date="dateInputs.endDate || undefined"
            @update:model-value="updateStartDate"
            class="parameter-field date-input"
          />

          <div class="date-separator">至</div>

          <!-- 结束日期 -->
          <SimpleDateInput
            v-model="dateInputs.endDate"
            placeholder="YYYY-MM-DD"
            :min-date="dateInputs.startDate || undefined"
            @update:model-value="updateEndDate"
            class="parameter-field date-input"
          />
        </div>

        <!-- 快捷日期选择 -->
        <div class="quick-date-buttons">
          <van-button
            size="small"
            type="default"
            @click="setQuickDateRange('1m')"
            :class="{ active: isQuickDateActive('1m') }"
          >
            近1月
          </van-button>
          <van-button
            size="small"
            type="default"
            @click="setQuickDateRange('3m')"
            :class="{ active: isQuickDateActive('3m') }"
          >
            近3月
          </van-button>
          <van-button
            size="small"
            type="default"
            @click="setQuickDateRange('6m')"
            :class="{ active: isQuickDateActive('6m') }"
          >
            近6月
          </van-button>
          <van-button
            size="small"
            type="default"
            @click="setQuickDateRange('1y')"
            :class="{ active: isQuickDateActive('1y') }"
          >
            近1年
          </van-button>
        </div>
      </div>

      <!-- 策略参数分组 -->
      <!-- eslint-disable vue/no-v-for-template-key -->
      <template v-for="(group, groupIndex) in parameterGroups" :key="`group-${groupIndex}`">
        <div class="card parameter-card">
          <h3 class="section-title-underline">{{ group.name || '策略参数' }}</h3>

          <template v-for="param in group.params">
            <!-- 数字类型 -->
            <van-field
              v-if="param.type === 'number'"
              :key="`number-${param.name}`"
              v-model.number="formData.strategyParams[param.name]"
              type="number"
              :label="param.label"
              :placeholder="`请输入${param.label}`"
              class="parameter-field"
              :rules="[{
                required: param.required !== false,
                message: `请输入${param.label}`,
                validator: (val) => validateParameter(param, val)
              }]"
            >
              <template #extra>
                <van-icon
                  name="question-o"
                  @click.stop="showParamHelp(param)"
                  class="param-help"
                />
              </template>
            </van-field>

            <!-- 选择类型 -->
            <van-field
              v-else-if="param.type === 'select'"
              :key="`select-${param.name}`"
              v-model="formData.strategyParams[param.name]"
              is-link
              readonly
              :label="param.label"
              :placeholder="`请选择${param.label}`"
              @click="showOptionPicker(param)"
              class="parameter-field"
              :rules="[{
                required: param.required !== false,
                message: `请选择${param.label}`
              }]"
            >
              <template #extra>
                <van-icon
                  name="question-o"
                  @click.stop="showParamHelp(param)"
                  class="param-help"
                />
              </template>
            </van-field>

            <!-- 开关类型 -->
            <van-field
              v-else-if="param.type === 'boolean'"
              :key="`boolean-${param.name}`"
              v-model="formData.strategyParams[param.name]"
              :label="param.label"
              class="parameter-field"
              :rules="[{
                required: param.required !== false,
                message: `请选择${param.label}`
              }]"
            >
              <template #input>
                <van-switch
                  v-model="formData.strategyParams[param.name]"
                  size="20"
                />
              </template>
              <template #extra>
                <van-icon
                  name="question-o"
                  @click.stop="showParamHelp(param)"
                  class="param-help"
                />
              </template>
            </van-field>

            <!-- 文本类型 -->
            <van-field
              v-else
              :key="`text-${param.name}`"
              v-model="formData.strategyParams[param.name]"
              :label="param.label"
              :placeholder="`请输入${param.label}`"
              class="parameter-field"
              :rules="[{
                required: param.required !== false,
                message: `请输入${param.label}`
              }]"
            >
              <template #extra>
                <van-icon
                  name="question-o"
                  @click.stop="showParamHelp(param)"
                  class="param-help"
                />
              </template>
            </van-field>
          </template>
        </div>
      </template>

      <!-- 移除按钮区域 -->
    </van-form>

    <!-- 参数帮助弹窗 -->
    <van-dialog
      v-model="showHelpDialog"
      title="参数说明"
      show-cancel-button
    >
      <div class="param-help-content">
        <h4>{{ currentHelpParam?.label }}</h4>
        <p v-if="currentHelpParam?.default !== undefined">默认值: {{ currentHelpParam.default }}</p>
        <p v-if="currentHelpParam?.min !== undefined || currentHelpParam?.max !== undefined">
          范围: {{ currentHelpParam?.min ?? '无限制' }} ~ {{ currentHelpParam?.max ?? '无限制' }}
        </p>
        <p class="help-desc">{{ currentHelpParam?.description || '暂无详细说明' }}</p>
      </div>
    </van-dialog>

    <!-- 选项选择器 -->
    <van-popup v-model="showOptionDialog" position="bottom">
      <van-picker
        v-if="currentOptionParam"
        :columns="currentOptionParam.options || []"
        @confirm="onOptionConfirm"
        @cancel="showOptionDialog = false"
      />
    </van-popup>

    <!-- 移除日期选择弹窗 -->
  </div>
</template>

<script setup>
/* eslint-disable vue/no-v-for-template-key */
import { ref, computed } from 'vue'
import { showToast } from 'vant'
import SimpleDateInput from '@/components/SimpleDateInput.vue'

const props = defineProps({
  strategy: {
    type: Object,
    required: true
  },
  initialParams: {
    type: Object,
    default: () => ({})
  },
  asset: {
    type: [Object, Array],
    default: null
  }
})

const emit = defineEmits(['submit'])

// 表单引用
const formRef = ref(null)
// 表单提交状态 (当前未使用)
// const submitLoading = ref(false)
const activeCollapse = ref([]) // 默认折叠已选信息

// 日期范围配置
const now = new Date()
// 日期范围限制 (当前未使用)
// const minDate = new Date(now.getFullYear() - 10, 0, 1) // 10年前
// const maxDate = new Date(now.getFullYear() + 1, 11, 31) // 明年年底

// 表单数据
const formData = ref({
  initialCapital: props.initialParams.initialCapital || 100000,
  startDate: props.initialParams.startDate || new Date(now.getFullYear(), now.getMonth(), now.getDate()),
  endDate: props.initialParams.endDate || new Date(now.getFullYear(), now.getMonth(), now.getDate()),
  strategyParams: props.initialParams.strategyParams || {}
})

// 判断是否是多标的回测
const isMultiAsset = computed(() => {
  return Array.isArray(props.asset)
})

// 获取标的列表
const assetList = computed(() => {
  if (isMultiAsset.value) {
    return props.asset
  } else if (props.asset) {
    return [props.asset]
  }
  return []
})

// 获取标的数量
const assetCount = computed(() => {
  return assetList.value.length
})

// 获取标的名称显示
// 获取标的显示名称 (当前未使用)
// const getAssetName = computed(() => {
//   if (isMultiAsset.value) {
//     return `已选${assetCount.value}个标的`
//   } else if (props.asset) {
//     return props.asset.name
//   }
//   return '未选择标的'
// })

// 获取策略角标文本

// 获取标的类型文本
const getAssetTypeText = (strategy) => {
  if (!strategy) return ''
  if (strategy.type === 'arbitrage' || strategy.type === 'hedge') {
    return '多标的'
  } else if (strategy.type === 'dynamic') {
    return '动态标的'
  }
  return '单标的'
}

// 获取策略类型文本
const getStrategyTypeText = (type) => {
  switch(type) {
    case 'trend':
      return '趋势型'
    case 'mean_reversion':
      return '均值回归'
    case 'arbitrage':
      return '套利策略'
    case 'hedge':
      return '对冲策略'
    case 'dynamic':
      return '动态策略'
    default:
      return '其他策略'
  }
}


// 日期格式化
const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
}

// 日期输入状态
const currentQuickDateRange = ref(null)

// 日期输入
const dateInputs = ref({
  startDate: formatDate(props.initialParams?.startDate) || '',
  endDate: formatDate(props.initialParams?.endDate) || ''
})

// 日期范围是否有效
const isValidDateRange = computed(() => {
  if (!dateInputs.value.startDate || !dateInputs.value.endDate) return false

  try {
    const start = new Date(dateInputs.value.startDate)
    const end = new Date(dateInputs.value.endDate)
    return !isNaN(start.getTime()) && !isNaN(end.getTime()) && start <= end
  } catch (e) {
    return false
  }
})

// 日期格式化显示
// 日期范围文本显示 (当前未使用)
// const dateRangeText = computed(() => {
//   return `${formatDate(formData.value.startDate)} 至 ${formatDate(formData.value.endDate)}`
// })

// 参数分组
const parameterGroups = computed(() => {
  const params = props.strategy?.parameters || []
  const groups = {}

  params.forEach(param => {
    const groupName = param.group || 'default'
    if (!groups[groupName]) {
      groups[groupName] = {
        name: groupName === 'default' ? '' : groupName,
        params: []
      }
    }
    groups[groupName].params.push(param)
  })

  return Object.values(groups)
})

// 计算日期范围天数
const calculateDaysFromInputs = () => {
  if (!isValidDateRange.value) return 0

  const start = new Date(dateInputs.value.startDate)
  const end = new Date(dateInputs.value.endDate)
  const diffTime = Math.abs(end - start)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1 // 包含开始和结束日
  return diffDays
}

// 更新开始日期
const updateStartDate = (value) => {
  if (value) {
    formData.value.startDate = new Date(value)
    currentQuickDateRange.value = null
  }
}

// 更新结束日期
const updateEndDate = (value) => {
  if (value) {
    formData.value.endDate = new Date(value)
    currentQuickDateRange.value = null
  }
}

// 设置快捷日期范围
const setQuickDateRange = (range) => {
  currentQuickDateRange.value = range
  const end = new Date()
  let start = new Date()

  switch (range) {
    case '1m':
      start.setMonth(end.getMonth() - 1)
      break
    case '3m':
      start.setMonth(end.getMonth() - 3)
      break
    case '6m':
      start.setMonth(end.getMonth() - 6)
      break
    case '1y':
      start.setFullYear(end.getFullYear() - 1)
      break
    default:
      break
  }

  dateInputs.value.startDate = formatDate(start)
  dateInputs.value.endDate = formatDate(end)
  formData.value.startDate = start
  formData.value.endDate = end
}

// 检查当前是否是某个快捷日期范围
const isQuickDateActive = (range) => {
  return currentQuickDateRange.value === range
}

// 参数帮助相关
const showHelpDialog = ref(false)
const currentHelpParam = ref(null)

const showParamHelp = (param) => {
  currentHelpParam.value = param
  showHelpDialog.value = true
}

// 选项选择器相关
const showOptionDialog = ref(false)
const currentOptionParam = ref(null)

const showOptionPicker = (param) => {
  currentOptionParam.value = param
  showOptionDialog.value = true
}

const onOptionConfirm = (value) => {
  if (currentOptionParam.value) {
    formData.value.strategyParams[currentOptionParam.value.name] = value
  }
  showOptionDialog.value = false
}

// 验证资金输入
const validateCapital = (value) => {
  if (value === undefined || value === null || value === '') {
    return false
  }
  return value > 0
}

// 验证参数输入
const validateParameter = (param, value) => {
  if (param.required !== false && (value === undefined || value === null || value === '')) {
    return false
  }

  if (param.type === 'number') {
    if (param.min !== undefined && value < param.min) {
      return false
    }
    if (param.max !== undefined && value > param.max) {
      return false
    }
  }

  return true
}


// 保存参数
const saveParameters = () => {
  if (!isValidDateRange.value) {
    showToast({
      type: 'fail',
      message: '请选择有效的日期范围',
      position: 'bottom'
    })
    return
  }


  // 构建参数对象
  const parameters = {
    initialCapital: formData.value.initialCapital,
    startDate: formData.value.startDate,
    endDate: formData.value.endDate,
    strategyParams: formData.value.strategyParams
  }

  // 延迟一下，模拟网络请求
  setTimeout(() => {
    emit('submit', parameters)

    showToast({
      type: 'success',
      message: '参数已保存',
      position: 'bottom'
    })
  }, 500)
}

</script>

<style scoped>
/* 主要样式 */

/* 卡片通用样式 */
.card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: 16px;
  margin-left: 12px;
  margin-right: 12px;
  overflow: hidden;
  width: calc(100% - 24px);
}

/* 信息卡片 */
.info-card {
  padding: 0;
}

/* 参数卡片 */
.parameter-card {
  padding: 16px;
}

/* 折叠面板样式 */
.info-collapse {
  width: 100%;
}

/* 折叠面板标题 */
.collapse-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
}

/* 自定义折叠面板样式 */
:deep(.van-collapse-item__title) {
  font-weight: 500 !important;
  color: var(--text-color-primary) !important;
  background-color: white !important;
  padding: 16px !important;
  height: auto !important;
  line-height: 1.5 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

:deep(.van-cell__title) {
  flex: 1 !important;
}

:deep(.van-cell__right-icon) {
  position: static !important;
  margin-left: 4px !important;
  transform: rotate(0deg) !important;
  transition: transform 0.3s !important;
}

:deep(.van-cell--expanded) .van-cell__right-icon {
  transform: rotate(180deg) !important;
}

:deep(.van-collapse-item__content) {
  padding: 0 16px 16px;
  background-color: white;
}

/* 已选信息内容 */
.selected-info-content {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-section {
  margin-bottom: 12px;
}

/* 信息项行 */
.info-item-row {
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-label {
  font-weight: 500;
  color: var(--text-color-secondary);
}

.info-value {
  color: var(--text-color-primary);
}

/* 区块标题 */
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
  padding: 0;
}

/* 子标题 */
.info-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
}

/* 区域标题带下划线 */
.section-title-underline {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin-top: 0;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color-light);
}

/* 参数字段样式 */
.parameter-field {
  margin-bottom: 16px;
}

.parameter-field:last-child {
  margin-bottom: 0;
}

/* 日期范围标题 */
.date-range-title {
  font-size: 14px;
  color: var(--text-color-primary);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.date-range-days-inline {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-left: 4px;
}

/* 日期输入样式 */
.date-range-inputs {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.date-input {
  flex: 1;
  margin-bottom: 0;
}

.date-separator {
  margin: 0 8px;
  color: var(--text-color-secondary);
}

/* 快捷日期按钮 */
.quick-date-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.quick-date-buttons .van-button {
  flex: 1;
  min-width: 60px;
}

.quick-date-buttons .van-button.active {
  color: var(--primary-color);
  background-color: var(--primary-color-light);
  border-color: var(--primary-color);
}

/* 单位文本 */
.unit-text {
  color: var(--text-color-secondary);
  font-size: 14px;
}

/* 参数帮助图标 */
.param-help {
  color: var(--text-color-secondary);
  font-size: 16px;
  padding: 4px;
}

/* 参数帮助内容 */
.param-help-content {
  padding: 16px;
  text-align: left;
}

.param-help-content h4 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 16px;
  color: var(--text-color-primary);
}

.param-help-content p {
  margin: 4px 0;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.help-desc {
  margin-top: 8px;
  line-height: 1.5;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reset-button {
  margin-top: 8px;
}

/* 操作卡片 */
.action-card {
  padding: 16px;
}
</style>
