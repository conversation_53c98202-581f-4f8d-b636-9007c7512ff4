/* 导入变量 */
@import './variables.css';

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: 14px;
  line-height: var(--line-height-normal);
  color: var(--text-color-primary);
  background-color: var(--background-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 确保页面可以滚动 */
  overflow-y: auto;
  /* 使用更温和的 overscroll-behavior，允许滚动但减少过度滚动效果 */
  overscroll-behavior: auto;
}

/* 微信环境特殊处理 */
@media screen and (max-width: 768px) {
  /* 在移动端环境下增强滚动体验 */
  body {
    -webkit-overflow-scrolling: touch; /* 增强 iOS 滚动体验 */
    touch-action: pan-y; /* 允许垂直滚动 */
  }
}

/* 微信环境特定样式 */
.wechat-environment {
  /* 微信环境中的特殊样式 */
  overscroll-behavior: touch; /* 使用更自然的滚动行为 */
}

/* 确保内容可滚动 */
.wechat-environment #app {
  overflow-y: auto; /* 确保内容可以滚动 */
  height: auto; /* 自动高度 */
  min-height: 100vh; /* 最小高度为视口高度 */
}

/* 修复微信中的滚动问题 */
.wechat-environment .van-tabs__content,
.wechat-environment .van-tab__pane {
  overflow-y: visible !important; /* 确保标签页内容可滚动 */
}

/* 在微信环境中隐藏应用内部的导航栏，避免与微信自带导航栏重复 */
.wechat-environment .van-nav-bar {
  display: none !important;
}

/* 在微信环境中调整内容区域的上边距，补偿隐藏导航栏后的空间 */
.wechat-environment .content-container,
.wechat-environment .backtest-container > .content-container,
.wechat-environment .strategy-square > .content-container,
.wechat-environment .learn-center > .content-tabs,
.wechat-environment .user-center > .content-tabs {
  padding-top: 0;
}

/* 在微信环境中调整吸顶元素的位置 */
.wechat-environment .custom-steps-nav,
.wechat-environment .strategy-search,
.wechat-environment .filter-container,
.wechat-environment .van-tabs--sticky {
  top: 0 !important;
}

/* 移动端视口高度修复 */
#app {
  min-height: 100vh; /* Fallback */
  min-height: calc(var(--vh, 1vh) * 100);
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
}

/* 通用卡片样式 */
.card {
  background-color: var(--background-color-card);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  padding: var(--card-padding);
  margin-bottom: var(--card-margin);
}

/* 通用标题样式 */
.page-title {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--text-color-primary);
  margin-bottom: var(--spacing-md);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-color-primary);
  margin-bottom: var(--spacing-md);
}

/* 通用文本样式 */
.text-primary {
  color: var(--text-color-primary);
}

.text-regular {
  color: var(--text-color-regular);
}

.text-secondary {
  color: var(--text-color-secondary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-info {
  color: var(--info-color);
}

/* 通用间距类 */
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }

.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* 通用弹性布局类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-start {
  align-items: flex-start;
}

.align-center {
  align-items: center;
}

.align-end {
  align-items: flex-end;
}

/* 通用图表容器样式 */
.chart-container {
  height: var(--chart-height);
  background-color: var(--chart-background);
  border-radius: var(--chart-border-radius);
  padding: var(--chart-padding);
  margin-bottom: var(--spacing-lg);
}

/* 通用动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-duration) var(--transition-timing-function);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform var(--transition-duration) var(--transition-timing-function);
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: var(--safe-area-inset-bottom);
}

.safe-area-inset-bottom {
  height: var(--safe-area-inset-bottom);
}

/* 响应式布局辅助类 */
@media screen and (min-width: 768px) {
  .container {
    max-width: 750px;
    margin: 0 auto;
  }
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

.skeleton {
  background: linear-gradient(90deg,
    rgba(190, 190, 190, 0.2) 25%,
    rgba(129, 129, 129, 0.24) 37%,
    rgba(190, 190, 190, 0.2) 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

/* 修复 Vant 组件样式 */
.van-nav-bar {
  background-color: var(--background-color-light) !important;
  height: 44px !important; /* 减小高度 */
  padding-top: var(--safe-area-inset-top) !important; /* 确保导航栏在状态栏下方 */
  margin-bottom: 10px !important; /* 减少底部边距 */
  width: 100% !important; /* 确保宽度为100% */
}

.van-nav-bar__content {
  height: 44px !important; /* 减小高度 */
  padding: 0 !important; /* 移除内边距 */
}

.van-nav-bar__left,
.van-nav-bar__right {
  padding: 0 12px !important; /* 减少左右内边距 */
}

.van-nav-bar__title {
  color: var(--text-color-primary) !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: 16px !important;
  max-width: 80% !important; /* 增加标题宽度 */
}

.van-tabbar {
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
  background-color: var(--background-color-light) !important;
}

.van-button--primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: var(--text-color-inverse) !important;
}

.van-button--primary .van-icon {
  color: var(--text-color-inverse) !important;
}

.van-button--primary.van-button--plain {
  color: var(--primary-color) !important;
}

/* 按钮样式优化 */
.van-button {
  font-weight: var(--font-weight-medium);
  transition: opacity 0.2s, background-color 0.2s, color 0.2s;
}

.van-button:active {
  opacity: 0.9;
}

.van-button--mini {
  font-size: var(--font-size-sm) !important;
}

.van-button--small {
  font-size: var(--font-size-md) !important;
}

.van-button--normal {
  font-size: var(--font-size-md) !important;
}

.van-button--large {
  font-size: var(--font-size-lg) !important;
}

/* 按钮图标对齐 */
.van-button__content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.van-button .van-icon {
  vertical-align: middle;
}

/* 按钮加载状态优化 */
.van-button--loading .van-button__loading {
  color: inherit;
}

.van-tabs__line {
  background-color: var(--primary-color) !important;
}

.van-tab--active {
  color: var(--primary-color) !important;
  font-weight: var(--font-weight-medium) !important;
}

.van-cell {
  background-color: var(--background-color-light) !important;
}

.van-field__control {
  color: var(--text-color-primary) !important;
}

.van-field__label {
  color: var(--text-color-regular) !important;
}

/* 修复底部安全区域 */
.van-tabbar {
  padding-bottom: var(--safe-area-inset-bottom) !important;
}
