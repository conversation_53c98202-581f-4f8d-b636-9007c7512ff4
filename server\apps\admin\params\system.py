"""
类依赖项-官方文档：https://fastapi.tiangolo.com/zh/tutorial/dependencies/classes-as-dependencies/
"""
from fastapi import Depends
from core.dependencies import Paging, QueryParams


class DictTypeParams(QueryParams):
    """
    列表分页
    """

    def __init__(self, dict_name: str = None, dict_type: str = None, params: Paging = Depends()):
        super().__init__(params)
        self.dict_name = ("like", dict_name)
        self.dict_type = ("like", dict_type)


class DictDetailParams(QueryParams):
    """
    列表分页
    """

    def __init__(self, dict_type_id: int = None, label: str = None, params: Paging = Depends()):
        super().__init__(params)
        self.dict_type_id = dict_type_id
        self.label = ("like", label)


class TaskParams(QueryParams):
    """
    列表分页
    """

    def __init__(self, name: str = None, _id: str = None, group: str = None, params: Paging = Depends()):
        super().__init__(params)
        self.name = ("like", name)
        self.group = group
        self._id = ("ObjectId", _id)
        self.v_order = "desc"


class TaskRecordParams(QueryParams):
    """
    列表分页
    """

    def __init__(self, job_id: str = None, name: str = None, params: Paging = Depends()):
        super().__init__(params)
        self.job_id = ("like", job_id)
        self.name = ("like", name)
        self.v_order = "desc"
