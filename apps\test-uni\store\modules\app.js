// 导入配置文件
import config from '@/config.js'
// 导入获取系统基础配置的API
import { getSystemBaseConfigApi } from '@/common/request/api/admin/system/settings.js'

// 定义应用的初始状态
const state = {
  title: '', // 应用标题
  logo: true, // 是否显示logo
  logoImage: '', // logo图片地址
  footer: true, // 是否显示页脚
  footerContent: '', // 页脚内容
  icpNumber: '', // 备案号
  version: config.appInfo.version, // 应用版本
  privacy: config.appInfo.privacy, // 隐私政策
  agreement: config.appInfo.agreement, // 用户协议
  siteUrl: '', // 源码地址
  WXEmail: '', // 官方邮箱
  WXPhone: '' // 服务热线
}

// 定义修改状态的方法
const mutations = {
  SET_TITLE: (state, title) => {
    state.title = title
  },
  SET_LOGO: (state, logo) => {
    state.logo = logo
  },
  SET_LOGO_IMAGE: (state, logoImage) => {
    state.logoImage = logoImage
  },
  SET_FOOTER: (state, footer) => {
    state.footer = footer
  },
  SET_FOOTER_CONTENT: (state, footerContent) => {
    state.footerContent = footerContent
  },
  SET_ICPNUMBER: (state, icpNumber) => {
    state.icpNumber = icpNumber
  },
  SET_VERSION: (state, version) => {
    state.version = version
  },
  SET_SITE_URL: (state, siteUrl) => {
    state.siteUrl = siteUrl
  },
  SET_WX_EMAIL: (state, WXEmail) => {
    state.WXEmail = WXEmail
  },
  SET_WX_PHONE: (state, WXPhone) => {
    state.WXPhone = WXPhone
  }
}

// 定义异步操作
const actions = {
  // 初始化系统配置
  InitConfig({ commit }) {
    return new Promise((resolve, reject) => {
      getSystemBaseConfigApi()
        .then((res) => {
          // 根据API返回的数据更新状态
          commit('SET_TITLE', res.data.web_title || 'Mxtt')
          commit('SET_LOGO_IMAGE', config.baseUrl + (res.data.web_logo || '/media/system/logo.png'))
          commit('SET_FOOTER_CONTENT', res.data.web_copyright || 'Copyright ©2022-present K')
          commit('SET_ICPNUMBER', res.data.web_icp_number || '')
          commit('SET_SITE_URL', res.data.wx_server_site || '')
          commit('SET_WX_EMAIL', res.data.wx_server_email || '')
          commit('SET_WX_PHONE', res.data.wx_server_phone || '')
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
}

// 导出模块
export default {
  namespaced: true, // 使用命名空间去访问模块中属性，例如：user/login
  state,
  mutations,
  actions
}