<template>
  <view class="container">
    <!-- 导航栏 -->
    <uni-nav-bar
      title="策略回测"
      left-icon="back"
      @clickLeft="navBack"
    ></uni-nav-bar>

    <view style="padding: 20px">
      <!-- 策略选择 -->
      <uni-segmented-control
        :current="currentStrategy"
        :values="strategyList.map(item => item.name)"
        @clickItem="strategyChange"
        style-type="button"
      ></uni-segmented-control>

      <!-- 参数输入 -->
      <uni-forms ref="formRef" label-position="left" label-width="100px" :model="form" :rules="rules" style="margin-top: 20px">
        <template v-if="currentStrategy === 0">
          <!-- MACD参数 -->
          <uni-forms-item label="快线周期" name="fastPeriod" required>
            <uni-easyinput type="number" v-model="form.fastPeriod" placeholder="请输入快线周期"></uni-easyinput>
          </uni-forms-item>
          <uni-forms-item label="慢线周期" name="slowPeriod" required>
            <uni-easyinput type="number" v-model="form.slowPeriod" placeholder="请输入慢线周期"></uni-easyinput>
          </uni-forms-item>
          <uni-forms-item label="信号周期" name="signalPeriod" required>
            <uni-easyinput type="number" v-model="form.signalPeriod" placeholder="请输入信号周期"></uni-easyinput>
          </uni-forms-item>
        </template>
        <template v-else>
          <!-- RSI参数 -->
          <uni-forms-item label="周期" name="period" required>
            <uni-easyinput type="number" v-model="form.period" placeholder="请输入周期"></uni-easyinput>
          </uni-forms-item>
          <uni-forms-item label="超买阈值" name="overbought" required>
            <uni-easyinput type="number" v-model="form.overbought" placeholder="请输入超买阈值"></uni-easyinput>
          </uni-forms-item>
          <uni-forms-item label="超卖阈值" name="oversold" required>
            <uni-easyinput type="number" v-model="form.oversold" placeholder="请输入超卖阈值"></uni-easyinput>
          </uni-forms-item>
        </template>

        <!-- 时间范围选择 -->
        <uni-forms-item label="开始时间" prop="startTime" required>
            <uni-datetime-picker type="date" v-model="form.startTime"></uni-datetime-picker>
        </uni-forms-item>
        <uni-forms-item label="结束时间" prop="endTime" required>
            <uni-datetime-picker type="date" v-model="form.endTime"></uni-datetime-picker>
        </uni-forms-item>
      </uni-forms>

      <!-- 回测按钮 -->
      <view style="margin-top: 20px">
        <uni-button
          type="primary"
          @click="validateForm"
          :loading="loading"
        >立即回测</uni-button>
      </view>

      <!-- 结果展示 -->
      <view v-if="showResult" style="margin-top: 20px">
        <uni-grid :column="3" :show-border="false">
          <uni-grid-item v-for="(item, index) in metrics" :key="index">
            <view class="metric-item">
              <text class="metric-value">{{ item.value }}</text>
              <text class="metric-label">{{ item.label }}</text>
            </view>
          </uni-grid-item>
        </uni-grid>
      </view>
    </view>
  </view>
</template>

<script>
import uniSegmentedControl from '@/uni_modules/uni-segmented-control/components/uni-segmented-control/uni-segmented-control.vue'

export default {
  components: {
    uniSegmentedControl
  },
  data() {
    return {
      loading: false,
      showResult: false,
      strategyList: [
        { name: 'MACD', value: 0 },
        { name: 'RSI', value: 1 }
      ],
      currentStrategy: 0,
      form: {
        fastPeriod: 12,
        slowPeriod: 26,
        signalPeriod: 9,
        period: 14,
        overbought: 70,
        oversold: 30,
        startTime: new Date().getTime() - 365 * 24 * 60 * 60 * 1000,
        endTime: new Date().getTime()
      },
      rules: {
        fastPeriod: [
          { required: true, message: '请输入快线周期', trigger: ['blur', 'change'] },
          { type: 'number', min: 1, max: 100, message: '快线周期应在1-100之间', trigger: ['blur', 'change'] }
        ],
        slowPeriod: [
          { required: true, message: '请输入慢线周期', trigger: ['blur', 'change'] },
          { type: 'number', min: 1, max: 100, message: '慢线周期应在1-100之间', trigger: ['blur', 'change'] }
        ],
        signalPeriod: [
          { required: true, message: '请输入信号周期', trigger: ['blur', 'change'] },
          { type: 'number', min: 1, max: 100, message: '信号周期应在1-100之间', trigger: ['blur', 'change'] }
        ],
        period: [
          { required: true, message: '请输入周期', trigger: ['blur', 'change'] },
          { type: 'number', min: 1, max: 100, message: '周期应在1-100之间', trigger: ['blur', 'change'] }
        ],
        overbought: [
          { required: true, message: '请输入超买阈值', trigger: ['blur', 'change'] },
          { type: 'number', min: 50, max: 100, message: '超买阈值应在50-100之间', trigger: ['blur', 'change'] }
        ],
        oversold: [
          { required: true, message: '请输入超卖阈值', trigger: ['blur', 'change'] },
          { type: 'number', min: 0, max: 50, message: '超卖阈值应在0-50之间', trigger: ['blur', 'change'] }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: ['change'] }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: ['change'] }
        ]
      },
      metrics: [
        { label: '收益率', value: '0%' },
        { label: '年化收益', value: '0%' },
        { label: '最大回撤', value: '0%' },
        { label: '胜率', value: '0%' },
        { label: '交易次数', value: '0' },
        { label: '夏普比率', value: '0' }
      ]
    }
  },
  methods: {
    strategyChange(e) {
      this.currentStrategy = e.currentIndex
    },
    navBack() {
      uni.navigateBack()
    },
    validateForm() {
      this.$refs.formRef.validate().then(valid => {
        if (valid) {
          this.runBacktest()
        }
      })
    },
    runBacktest() {
      this.loading = true
      this.showResult = false
      
      // 模拟API调用
      setTimeout(() => {
        this.loading = false
        this.showResult = true
        this.metrics = [
          { label: '收益率', value: '15.2%' },
          { label: '年化收益', value: '18.5%' },
          { label: '最大回撤', value: '5.3%' },
          { label: '胜率', value: '62.1%' },
          { label: '交易次数', value: '24' },
          { label: '夏普比率', value: '1.8' }
        ]
      }, 2000)
    }
  }
}
</script>

<style lang="scss">
page {
  background-color: #ffffff;
}

.metric-item {
  text-align: center;
  padding: 20rpx 0;

  .metric-value {
    font-size: 36rpx;
    font-weight: bold;
    display: block;
    color: $u-primary;
  }

  .metric-label {
    font-size: 24rpx;
    color: $u-tips-color;
  }
}
</style>