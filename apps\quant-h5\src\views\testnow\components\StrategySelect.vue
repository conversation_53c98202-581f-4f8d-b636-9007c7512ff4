<template>
  <div class="strategy-select">

    <van-list
      :loading="loading"
      @update:loading="val => loading = val"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <div class="strategy-cards">
        <div
          v-for="strategy in sortedStrategies"
          :key="strategy.id"
          class="strategy-card"
          @click="showConfirmDialog(strategy)"
        >
          <div class="card-header">
            <h3 class="card-title">{{ strategy.name }}（{{ getAssetTypeText(strategy) }}）</h3>
            <div v-if="getStrategyBadgeText(strategy)" class="strategy-badge" :class="getStrategyBadgeClassName(strategy)">
              {{ getStrategyBadgeText(strategy) }}
            </div>
          </div>

          <div class="card-content">
            <p class="card-description">{{ strategy.description }}</p>

            <div class="card-tags">
              <!-- 策略类型标签 -->
              <van-tag
                plain
                size="small"
                :type="getStrategyTypeTagColor(strategy.type)"
                class="type-tag"
              >
                {{ getStrategyTypeText(strategy.type) }}
              </van-tag>

              <!-- 市场标签 -->
              <van-tag
                plain
                size="small"
                v-for="market in strategy.markets"
                :key="market"
                class="market-tag"
              >
                {{ market }}
              </van-tag>
            </div>
          </div>

          <div class="card-metrics">
            <div class="metric">
              <span class="metric-value">{{ strategy.stats?.backtestCount || 0 }}</span>
              <span class="metric-label">回测次数</span>
            </div>
            <div class="metric">
              <span class="metric-value">{{ strategy.stats?.likes || 0 }}</span>
              <span class="metric-label">点赞人数</span>
            </div>
            <div class="metric">
              <span class="metric-value">{{ strategy.stats?.favorites || 0 }}</span>
              <span class="metric-label">收藏人数</span>
            </div>
            <div class="metric">
              <span class="metric-value">{{ strategy.stats?.maxAnnualReturn || 0 }}%</span>
              <span class="metric-label">最大年化</span>
            </div>
          </div>
        </div>
      </div>
    </van-list>

    <!-- 移除了底部弹出层，改为使用Dialog确认 -->
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { showConfirmDialog as showVantConfirmDialog } from 'vant'

const emit = defineEmits(['update:modelValue', 'select'])

// 加载状态
const loading = ref(false) // 加载状态
const finished = ref(false) // 是否加载完成

// 排序选项 (当前未使用)
// 排序选项 (当前未使用)
// const sortOption = ref('default') // default|return|hot
const allStrategies = ref([
  {
    id: 1,
    name: '双均线策略',
    type: 'trend',
    description: '通过短期和长期均线交叉产生交易信号',
    markets: ['A股', '港股'],
    isNew: true,
    hot: 128,
    stats: {
      backtestCount: 256,
      likes: 128,
      favorites: 86,
      maxAnnualReturn: 22.5
    }
  },
  {
    id: 2,
    name: 'RSI超买超卖',
    type: 'mean_reversion',
    description: '基于RSI指标在超买超卖区域进行反向交易',
    markets: ['A股', '美股'],
    isNew: false,
    hot: 95,
    stats: {
      backtestCount: 189,
      likes: 95,
      favorites: 62,
      maxAnnualReturn: 18.3
    }
  },
  {
    id: 3,
    name: '跨市场套利',
    type: 'arbitrage',
    description: '利用不同市场间价差进行套利交易',
    markets: ['港股', '美股'],
    isNew: false,
    hot: 76,
    stats: {
      backtestCount: 124,
      likes: 76,
      favorites: 45,
      maxAnnualReturn: 25.7
    }
  },
  {
    id: 4,
    name: 'MACD金叉死叉',
    type: 'trend',
    description: '利用MACD指标的金叉和死叉信号进行交易',
    markets: ['A股', '期货'],
    isNew: true,
    hot: 145,
    stats: {
      backtestCount: 278,
      likes: 145,
      favorites: 92,
      maxAnnualReturn: 26.8
    }
  },
  {
    id: 5,
    name: '布林带突破',
    type: 'mean_reversion',
    description: '利用价格突破布林带上下轨进行交易',
    markets: ['A股', '港股', '美股'],
    isNew: false,
    hot: 112,
    stats: {
      backtestCount: 203,
      likes: 112,
      favorites: 78,
      maxAnnualReturn: 19.5
    }
  },
  {
    id: 6,
    name: '日内波动套利',
    type: 'arbitrage',
    description: '利用日内价格波动进行高抛低吸',
    markets: ['期货', '外汇'],
    isNew: false,
    hot: 88,
    stats: {
      backtestCount: 156,
      likes: 88,
      favorites: 52,
      maxAnnualReturn: 28.3
    }
  },
  {
    id: 7,
    name: '量价背离策略',
    type: 'trend',
    description: '通过分析价格和成交量的背离关系预测趋势反转',
    markets: ['A股', '港股'],
    isNew: false,
    hot: 75,
    stats: {
      backtestCount: 142,
      likes: 75,
      favorites: 48,
      maxAnnualReturn: 21.2
    }
  },
  {
    id: 8,
    name: '海龟交易法则',
    type: 'trend',
    description: '基于突破和趋势跟踪的经典交易系统',
    markets: ['期货', '外汇'],
    isNew: false,
    hot: 135,
    stats: {
      backtestCount: 245,
      likes: 135,
      favorites: 89,
      maxAnnualReturn: 32.7
    }
  },
  {
    id: 9,
    name: 'KDJ指标策略',
    type: 'mean_reversion',
    description: '利用KDJ指标的超买超卖区域进行交易',
    markets: ['A股', '美股'],
    isNew: false,
    hot: 92,
    stats: {
      backtestCount: 178,
      likes: 92,
      favorites: 58,
      maxAnnualReturn: 17.8
    }
  },
  {
    id: 10,
    name: '期现套利',
    type: 'arbitrage',
    description: '利用期货和现货之间的价差进行套利',
    markets: ['期货', 'A股'],
    isNew: false,
    hot: 65,
    stats: {
      backtestCount: 118,
      likes: 65,
      favorites: 42,
      maxAnnualReturn: 15.6
    }
  },
  {
    id: 11,
    name: '动量轮动策略',
    type: 'trend',
    description: '根据资产动量表现进行资产轮动配置',
    markets: ['ETF', '指数'],
    isNew: true,
    hot: 108,
    stats: {
      backtestCount: 196,
      likes: 108,
      favorites: 72,
      maxAnnualReturn: 24.3
    }
  },
  {
    id: 12,
    name: '波动率突破',
    type: 'trend',
    description: '基于波动率变化的突破交易策略',
    markets: ['期权', '期货'],
    isNew: false,
    hot: 82,
    stats: {
      backtestCount: 152,
      likes: 82,
      favorites: 54,
      maxAnnualReturn: 29.1
    }
  }
])

/**
 * 获取排序后的策略列表 (当前未使用)
 * @returns {Array} 排序后的策略列表
 */
const sortedStrategies = computed(() => {
  return [...strategies.value] // 当前保持原始顺序
})

/**
 * 获取策略角标文本
 * @param {Object} strategy 策略对象
 * @returns {string} 角标文本
 */
const getStrategyBadgeText = (strategy) => {
  if (strategy.isNew) return '最新'
  if (strategy.hot > 100) return '热门'
  if (strategy.stats?.favorites > 50) return '流行'
  return ''
}

/**
 * 获取角标样式类名
 * @param {Object} strategy 策略对象
 * @returns {string} 样式类名
 */
const getStrategyBadgeClassName = (strategy) => {
  if (strategy.isNew) return 'badge-new'
  if (strategy.hot > 100) return 'badge-hot'
  if (strategy.stats?.favorites > 50) return 'badge-popular'
  return ''
}

/**
 * 获取策略列表
 * @returns {Array} 策略列表
 */
const strategies = computed(() => {
  return allStrategies.value;
})

// 风险等级相关函数 (当前未使用)

/**
 * 获取标的类型标签样式
 * @param {Object} strategy 策略对象
 * @returns {string} 标签类型
 */

// 获取标的类型文本
const getAssetTypeText = (strategy) => {
  if (strategy.type === 'arbitrage' || strategy.type === 'hedge') {
    return '多标的'
  } else if (strategy.type === 'dynamic') {
    return '动态标的'
  }
  return '单标的'
}

// 获取策略类型文本
const getStrategyTypeText = (type) => {
  switch(type) {
    case 'trend':
      return '趋势型'
    case 'mean_reversion':
      return '均值回归'
    case 'arbitrage':
      return '套利策略'
    case 'hedge':
      return '对冲策略'
    case 'dynamic':
      return '动态策略'
    default:
      return '其他策略'
  }
}

// 获取策略类型标签颜色
const getStrategyTypeTagColor = (type) => {
  switch(type) {
    case 'trend':
      return 'primary'
    case 'mean_reversion':
      return 'success'
    case 'arbitrage':
      return 'warning'
    case 'hedge':
      return 'danger'
    default:
      return 'default'
  }
}

const onLoad = () => {
  // 模拟异步加载
  setTimeout(() => {
    loading.value = false
    finished.value = true
  }, 500)
}

// 添加确认弹窗
const showConfirmDialog = (strategy) => {
  // 使用vant的showConfirmDialog方法
  showVantConfirmDialog({
    title: '确认选择',
    message: `确定选择"${strategy.name}"策略吗？`,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
  .then(() => {
    // 用户点击确定
    emit('update:modelValue', strategy)
    emit('select', strategy)
  })
  .catch(() => {
    // 用户点击取消
    // 不做任何操作
  })
}
</script>

<style scoped>
/* 移除了底部弹出层相关样式 */

/* 策略卡片样式 */
.strategy-cards {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0;
  margin-top: 8px;
}

.strategy-card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 16px;
  margin-bottom: 16px;
  margin-left: 12px;
  margin-right: 12px;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 140px;
  position: relative;
  overflow: hidden;
  width: calc(100% - 24px);
}

.strategy-card:active {
  transform: scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.card-title {
  margin: 0;
  font-size: 15px;
  font-weight: bold;
  color: var(--text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90%;
}

.strategy-badge {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 0 8px 0 8px;
  color: white;
  font-weight: 500;
  z-index: 1;
}

.badge-new {
  background-color: #1989fa;
}

.badge-hot {
  background-color: #ff976a;
}

.badge-popular {
  background-color: #07c160;
}

.card-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-bottom: 0;
}

.card-description {
  color: var(--text-color-secondary);
  font-size: 13px;
  margin-bottom: 5px;
  flex-grow: 0;
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 减少为1行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 2px;
  margin-top: 2px;
}

.type-tag,
.market-tag {
  transform: scale(0.95);
  transform-origin: left center;
  font-size: 11px;
}

.card-metrics {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding-top: 4px;
  border-top: 1px solid var(--border-color-light);
  margin-top: 4px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 0 0 25%; /* 四个指标，每个占25%宽度 */
  margin-bottom: 2px;
}

.metric-value {
  font-size: 13px;
  font-weight: bold;
  color: var(--primary-color);
  line-height: 1.2;
}

.metric-label {
  font-size: 9px;
  color: var(--text-color-secondary);
  margin-top: 1px;
  text-align: center;
  width: 100%;
}


</style>
