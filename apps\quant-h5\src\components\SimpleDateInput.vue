<!--
  简易日期输入组件 (SimpleDateInput)

  该组件提供一个简单的日期输入框，支持手动输入日期，
  自动格式化为YYYY-MM-DD格式，并提供日期验证功能。

  功能：
  1. 支持手动输入日期，自动添加分隔符
  2. 提供日期格式和范围验证
  3. 支持最小/最大日期限制
  4. 显示清晰的错误提示
  5. 支持v-model双向绑定

  使用方式：
  <SimpleDateInput
    v-model="date"
    label="开始日期"
    :min-date="minDate"
    :max-date="maxDate"
  />
-->
<template>
  <div class="simple-date-input">
    <!-- 标签显示 -->
    <label v-if="label" class="date-label">{{ label }}</label>

    <!-- 输入框容器 -->
    <div class="date-input-container">
      <!-- 日期输入框 -->
      <input
        type="text"
        inputmode="numeric"
        pattern="\d{4}-\d{2}-\d{2}"
        :placeholder="placeholder || 'YYYY-MM-DD'"
        :value="formattedValue"
        @input="handleInput"
        @blur="handleBlur"
        class="date-input"
        :class="{ 'has-error': hasError }"
        autocomplete="off"
      />

      <!-- 清除按钮 -->
      <van-icon
        name="cross"
        v-if="formattedValue && !hasError"
        @click="clearInput"
        class="clear-icon"
        role="button"
        tabindex="0"
        aria-label="清除日期"
      />

      <!-- 错误图标 -->
      <van-icon
        name="warning-o"
        v-if="hasError"
        class="error-icon"
        role="img"
        aria-label="输入错误"
      />
    </div>

    <!-- 错误消息 -->
    <div v-if="hasError" class="error-message" role="alert">{{ errorMessage }}</div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

/**
 * 组件属性定义
 *
 * @property {String|Date} modelValue - 绑定的日期值
 * @property {String} label - 输入框标签
 * @property {String} placeholder - 输入框占位文本
 * @property {String|Date} minDate - 允许的最小日期
 * @property {String|Date} maxDate - 允许的最大日期
 */
const props = defineProps({
  modelValue: {
    type: [String, Date],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  minDate: {
    type: [String, Date],
    default: null
  },
  maxDate: {
    type: [String, Date],
    default: null
  }
})

/**
 * 组件事件
 */
const emit = defineEmits(['update:modelValue'])

// 内部状态
const inputValue = ref('')
const hasError = ref(false)
const errorMessage = ref('')

/**
 * 格式化日期为 YYYY-MM-DD 格式
 *
 * @param {String|Date} date - 要格式化的日期
 * @returns {String} 格式化后的日期字符串，格式为YYYY-MM-DD
 */
const formatDate = (date) => {
  if (!date) return ''

  if (typeof date === 'string') {
    // 如果已经是格式化的字符串，直接返回
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return date
    }

    // 尝试解析字符串为日期
    date = new Date(date)
  }

  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return ''
  }

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

/**
 * 计算属性：格式化后的值
 * 优先使用用户输入的值，如果没有则使用props中的值
 */
const formattedValue = computed(() => {
  if (inputValue.value) {
    return inputValue.value
  }
  return formatDate(props.modelValue)
})

/**
 * 验证日期格式和范围
 *
 * @param {String} value - 要验证的日期字符串
 * @returns {Boolean} 验证结果，true表示有效，false表示无效
 */
const validateDateFormat = (value) => {
  if (!value) return true

  // 验证格式：YYYY-MM-DD
  const regex = /^\d{4}-\d{2}-\d{2}$/
  if (!regex.test(value)) {
    errorMessage.value = '请使用 YYYY-MM-DD 格式'
    return false
  }

  // 解析年月日
  const [year, month, day] = value.split('-').map(Number)

  // 验证月份
  if (month < 1 || month > 12) {
    errorMessage.value = '月份必须在 1-12 之间'
    return false
  }

  // 验证日期
  const daysInMonth = new Date(year, month, 0).getDate()
  if (day < 1 || day > daysInMonth) {
    errorMessage.value = `该月份只有 ${daysInMonth} 天`
    return false
  }

  // 验证最小日期
  if (props.minDate) {
    const minDate = new Date(formatDate(props.minDate))
    const currentDate = new Date(value)
    if (currentDate < minDate) {
      errorMessage.value = `日期不能早于 ${formatDate(props.minDate)}`
      return false
    }
  }

  // 验证最大日期
  if (props.maxDate) {
    const maxDate = new Date(formatDate(props.maxDate))
    const currentDate = new Date(value)
    if (currentDate > maxDate) {
      errorMessage.value = `日期不能晚于 ${formatDate(props.maxDate)}`
      return false
    }
  }

  return true
}

/**
 * 处理输入事件
 * 自动添加连字符，限制长度，验证格式
 *
 * @param {Event} e - 输入事件对象
 */
const handleInput = (e) => {
  let value = e.target.value

  // 自动添加连字符
  if (value.length === 4 && !value.includes('-')) {
    value += '-'
  } else if (value.length === 7 && value.indexOf('-', 5) === -1) {
    value += '-'
  }

  // 限制长度为 10 (YYYY-MM-DD)
  if (value.length > 10) {
    value = value.slice(0, 10)
  }

  // 更新内部值并重置错误状态
  inputValue.value = value
  hasError.value = false

  // 如果格式正确，更新模型值
  if (validateDateFormat(value)) {
    emit('update:modelValue', value)
  }
}

/**
 * 处理失焦事件
 * 在输入框失去焦点时验证日期
 */
const handleBlur = () => {
  if (inputValue.value) {
    hasError.value = !validateDateFormat(inputValue.value)
  } else {
    hasError.value = false
  }
}

/**
 * 清除输入
 * 清空输入值并更新模型
 */
const clearInput = () => {
  inputValue.value = ''
  hasError.value = false
  emit('update:modelValue', '')
}

/**
 * 监听模型值变化
 * 当外部更新modelValue时，同步更新内部输入值
 */
watch(() => props.modelValue, (newValue) => {
  if (!inputValue.value || newValue !== inputValue.value) {
    inputValue.value = formatDate(newValue)
  }
})
</script>

<style scoped>
.simple-date-input {
  margin-bottom: var(--spacing-md);
  width: 100%;
}

.date-label {
  display: block;
  font-size: var(--font-size-md);
  color: var(--text-color-regular);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.date-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.date-input {
  width: 100%;
  height: var(--button-height);
  padding: 0 var(--spacing-lg);
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
  background-color: var(--background-color-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.date-input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.date-input.has-error {
  border-color: var(--danger-color);
}

.date-input.has-error:focus {
  box-shadow: 0 0 0 2px rgba(238, 10, 36, 0.1);
}

.clear-icon,
.error-icon {
  position: absolute;
  right: var(--spacing-md);
  font-size: var(--font-size-lg);
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.clear-icon {
  color: var(--text-color-secondary);
  background-color: rgba(150, 151, 153, 0.1);
}

.clear-icon:active {
  background-color: rgba(150, 151, 153, 0.2);
  transform: scale(0.95);
}

.error-icon {
  color: var(--danger-color);
  background-color: rgba(238, 10, 36, 0.1);
}

.error-message {
  font-size: var(--font-size-sm);
  color: var(--danger-color);
  margin-top: var(--spacing-xs);
  line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .date-input {
    font-size: var(--font-size-sm);
    padding: 0 var(--spacing-md);
  }
}
</style>
