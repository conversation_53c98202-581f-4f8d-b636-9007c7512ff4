from fastapi import APIRouter, Depends
from motor.motor_asyncio import AsyncIOMotorDatabase

from apps.admin.services.record import LoginRecordDal, OperationRecordDal, SMSSendRecordDal
from utils.response import SuccessResponse
from apps.admin.services.auth import AllUserAuth
from apps.admin.schemas.auth import Auth
from apps.admin.params.record import LoginParams, OperationParams, SMSParams
from core.database import mongo_getter

app = APIRouter()


###########################################################
#    日志管理
###########################################################
@app.get("/logins", summary="获取登录日志列表")
async def get_record_login(p: LoginParams = Depends(), auth: Auth = Depends(AllUserAuth())):
    datas, count = await LoginRecordDal(auth.db).get_datas(**p.dict(), v_return_count=True)
    return SuccessResponse(datas, count=count)


@app.get("/operations", summary="获取操作日志列表")
async def get_record_operation(
        p: OperationParams = Depends(),
        db: AsyncIOMotorDatabase = Depends(mongo_getter),
        auth: Auth = Depends(AllUserAuth())
):
    count = await OperationRecordDal(db).get_count(**p.to_count())
    datas = await OperationRecordDal(db).get_datas(**p.dict())
    return SuccessResponse(datas, count=count)


@app.get("/sms/send/list", summary="获取短信发送列表")
async def get_sms_send_list(p: SMSParams = Depends(), auth: Auth = Depends(AllUserAuth())):
    datas, count = await SMSSendRecordDal(auth.db).get_datas(**p.dict(), v_return_count=True)
    return SuccessResponse(datas, count=count)


###########################################################
#    日志分析
###########################################################
@app.get("/analysis/user/login/distribute", summary="获取用户登录分布情况列表")
async def get_user_login_distribute(auth: Auth = Depends(AllUserAuth())):
    return SuccessResponse(await LoginRecordDal(auth.db).get_user_distribute())
