from pydantic import BaseModel, ConfigDict, field_validator, Field
from pydantic_core.core_schema import FieldValidationInfo
from core.data_types import Telephone, DatetimeStr, Email


# 基础数据模型类
class User(BaseModel):
    name: str
    telephone: Telephone
    email: Email | None = None
    nickname: str | None = None
    avatar: str | None = None
    is_active: bool | None = True
    is_staff: bool | None = True
    gender: str | None = "0"
    is_wx_server_openid: bool | None = False


class Role(BaseModel):
    name: str
    disabled: bool = False
    order: int | None = None
    desc: str | None = None
    data_range: int = 4
    role_key: str
    is_admin: bool = False


class Dept(BaseModel):
    name: str
    dept_key: str
    disabled: bool = False
    order: int | None = None
    desc: str | None = None
    owner: str | None = None
    phone: str | None = None
    email: str | None = None
    parent_id: int | None = None


class Menu(BaseModel):
    title: str
    icon: str | None = None
    component: str | None = None
    redirect: str | None = None
    path: str | None = None
    disabled: bool = False
    hidden: bool = False
    order: int | None = None
    perms: str | None = None
    parent_id: int | None = None
    menu_type: str
    alwaysShow: bool | None = True
    noCache: bool | None = False


class Meta(BaseModel):
    title: str
    icon: str | None = None
    hidden: bool = False
    noCache: bool | None = False
    breadcrumb: bool | None = True
    affix: bool | None = False
    noTagsView: bool | None = False
    canTo: bool | None = False
    alwaysShow: bool | None = True


class ResetPwd(BaseModel):
    password: str
    password_two: str

    @field_validator('password_two')
    def check_passwords_match(cls, v, info: FieldValidationInfo):
        if 'password' in info.data and v != info.data['password']:
            raise ValueError('两次密码不一致!')
        return v


# 输入/请求模型类
class UserIn(User):
    """
    创建用户
    """
    role_ids: list[int] = []
    dept_ids: list[int] = []
    password: str | None = ""


class UserUpdateBaseInfo(BaseModel):
    """
    更新用户基本信息
    """
    name: str
    telephone: Telephone
    email: Email | None = None
    nickname: str | None = None
    gender: str | None = "0"


class UserUpdate(User):
    """
    更新用户详细信息
    """
    name: str | None = None
    telephone: Telephone
    email: Email | None = None
    nickname: str | None = None
    avatar: str | None = None
    is_active: bool | None = True
    is_staff: bool | None = False
    gender: str | None = "0"
    role_ids: list[int] = []
    dept_ids: list[int] = []


class RoleIn(Role):
    menu_ids: list[int] = []
    dept_ids: list[int] = []


# 输出/响应模型类
class UserSimpleOut(User):
    model_config = ConfigDict(from_attributes=True)

    id: int
    update_datetime: DatetimeStr
    create_datetime: DatetimeStr

    is_reset_password: bool | None = None
    last_login: DatetimeStr | None = None
    last_ip: str | None = None


class UserPasswordOut(UserSimpleOut):
    model_config = ConfigDict(from_attributes=True)

    password: str


class RoleSimpleOut(Role):
    model_config = ConfigDict(from_attributes=True)

    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


class DeptSimpleOut(Dept):
    model_config = ConfigDict(from_attributes=True)

    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


class UserOut(UserSimpleOut):
    model_config = ConfigDict(from_attributes=True)

    roles: list[RoleSimpleOut] = []
    depts: list[DeptSimpleOut] = []


class MenuSimpleOut(Menu):
    model_config = ConfigDict(from_attributes=True)

    id: int
    create_datetime: DatetimeStr
    update_datetime: DatetimeStr


class RoleOut(RoleSimpleOut):
    model_config = ConfigDict(from_attributes=True)

    menus: list[MenuSimpleOut] = []
    depts: list[DeptSimpleOut] = []


class DeptTreeListOut(DeptSimpleOut):
    model_config = ConfigDict(from_attributes=True)

    children: list[dict] = []


class MenuTreeListOut(MenuSimpleOut):
    model_config = ConfigDict(from_attributes=True)

    children: list[dict] = []


class RouterOut(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str | None = None
    component: str | None = None
    path: str
    redirect: str | None = None
    meta: Meta | None = None
    order: int | None = None
    children: list[dict] = []


# 选项类
class RoleOptionsOut(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    label: str = Field(alias='name')
    value: int = Field(alias='id')
    disabled: bool
