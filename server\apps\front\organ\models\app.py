from sqlalchemy.orm import Mapped, mapped_column
from models.base import BaseModel
from sqlalchemy import String, Integer, ForeignKey, Boolean, Text


class AppSettings(BaseModel):
    __tablename__ = "app_settings"
    __table_args__ = ({'comment': 'app配置表'})

    config_label: Mapped[str] = mapped_column(String(255), comment="配置表标签（名称）")
    config_key: Mapped[str] = mapped_column(String(255), index=True, nullable=False, unique=True, comment="配置表键（key）")
    config_value: Mapped[str | None] = mapped_column(Text, comment="配置表内容（value）")
    can_values: Mapped[str | None] = mapped_column(String(255), comment="可选值（value,value..）")
    remark: Mapped[str | None] = mapped_column(String(255), comment="备注信息")
    disabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否禁用")

    app_id: Mapped[int] = mapped_column(
        Inte<PERSON>,
        ForeignKey("app_base.app_id", ondelete='CASCADE'),
        comment="关联AppID"
    )


class AppBase(BaseModel):
    __tablename__ = "app_base"
    __table_args__ = ({'comment': 'app基础表'})

    app_id: Mapped[str] = mapped_column(String(255), index=True, nullable=False, unique=True, comment="app唯一标识")
    name: Mapped[str] = mapped_column(String(255), index=True, nullable=False, comment="app名称")
    version: Mapped[str] = mapped_column(String(255), index=True, nullable=False, comment="app版本")
    remark: Mapped[str | None] = mapped_column(String(255), comment="备注信息")
    disabled: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否禁用")
