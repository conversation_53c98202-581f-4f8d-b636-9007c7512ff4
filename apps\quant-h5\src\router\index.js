import { createRouter, createWebHistory } from 'vue-router'
import { isWechatEnvironment, setWechatTitle } from '@/utils/wechat'
import { useUserStore } from '@/stores/user'

// Improved lazy loading with chunk names for better caching
const Backtest = () => import(/* webpackChunkName: "backtest" */ '../views/testnow/index.vue')
const Strategy = () => import(/* webpackChunkName: "strategy" */ '../views/square/index.vue')
// 使用相对路径导入
const BacktestDetail = () => import(/* webpackChunkName: "backtest-detail" */ '../views/square/detail.vue')
const Learn = () => import(/* webpackChunkName: "learn" */ '../views/learn/index.vue')
const CourseDetail = () => import(/* webpackChunkName: "course-detail" */ '../views/learn/detail.vue')
const User = () => import(/* webpackChunkName: "user" */ '../views/mine/index.vue')
const Login = () => import(/* webpackChunkName: "login" */ '../views/login/index.vue')
const NotFound = () => import(/* webpackChunkName: "error" */ '../views/NotFound.vue')

// Define routes with better organization
const routes = [
  // Main tab routes
  {
    path: '/testnow',
    name: 'Backtest',
    component: Backtest,
    meta: {
      title: '立即回测',
      keepAlive: true,
      tabItem: true,
      tabIndex: 0
    }
  },
  {
    path: '/square',
    name: 'Strategy',
    component: Strategy,
    meta: {
      title: '回测广场',
      keepAlive: true,
      tabItem: true,
      tabIndex: 1
    }
  },
  {
    path: '/square/detail/:id',
    name: 'BacktestDetail',
    component: BacktestDetail,
    meta: {
      title: '回测详情',
      keepAlive: false
    }
  },
  {
    path: '/learn',
    name: 'Learn',
    component: Learn,
    meta: {
      title: '学习课堂',
      keepAlive: true,
      tabItem: true,
      tabIndex: 2
    }
  },
  {
    path: '/learn/detail/:id',
    name: 'CourseDetail',
    component: CourseDetail,
    meta: {
      title: '课程详情',
      keepAlive: false
    }
  },
  {
    path: '/mine',
    name: 'User',
    component: User,
    meta: {
      title: '个人中心',
      keepAlive: true,
      tabItem: true,
      tabIndex: 3
    }
  },

  // 登录页面
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录/注册',
      keepAlive: false,
      requiresAuth: false
    }
  },

  // Redirect root to default tab
  {
    path: '/',
    redirect: '/testnow'
  },

  // 404 route
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面不存在',
      keepAlive: false
    }
  }
]

// Create router instance
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  // Improved scroll behavior
  scrollBehavior(to, _from, savedPosition) {
    if (savedPosition) {
      // Return to previous position if available (browser back/forward)
      return savedPosition
    } else if (to.hash) {
      // Scroll to hash if present
      return { el: to.hash, behavior: 'smooth' }
    } else {
      // Otherwise scroll to top
      return { top: 0, behavior: 'smooth' }
    }
  }
})

// Global navigation guards
router.beforeEach((to, _from, next) => {
  // Set page title
  const title = to.meta.title || '量化回测系统'

  // 使用微信环境工具函数设置标题
  if (isWechatEnvironment()) {
    setWechatTitle(title)
  } else {
    document.title = title
  }

  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)

  // 获取用户状态
  const userStore = useUserStore()
  const isLoggedIn = userStore.isLoggedIn
  const isGuest = userStore.userInfo.isGuest

  // 如果路由需要认证且用户未登录（包括游客模式）
  if (requiresAuth && !isLoggedIn && !isGuest && to.path !== '/login') {
    // 重定向到登录页
    next({ path: '/login' })
  } else {
    // 继续导航
    next()
  }
})

// After navigation complete
router.afterEach((_to, _from) => {
  // Track page view for analytics
  if (import.meta.env.PROD) {
    // Example: track page view
    // analytics.trackPageView(to.fullPath)
  }
})

export default router
