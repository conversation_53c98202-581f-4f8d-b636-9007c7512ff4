from fastapi import Body
import copy


class QueryParams:
    """
    查询参数基类
    
    提供通用的查询参数处理功能，包括：
    1. 参数转换字典
    2. 参数过滤
    3. 计数查询参数生成
    
    属性:
        page: int - 当前页码
        limit: int - 每页数量
        v_order: str - 排序方式(asc/desc)
        v_order_field: str - 排序字段名
        
    子类应继承此类并添加特定查询参数
    """
    def __init__(self, params=None):
        if params:
            self.page = params.page
            self.limit = params.limit
            self.v_order = params.v_order
            self.v_order_field = params.v_order_field

    def dict(self, exclude: list[str] = None) -> dict:
        """
        将查询参数转换为字典
        
        参数:
            exclude: list[str] - 需要排除的参数名列表
            
        返回:
            dict: 过滤后的参数字典
            
        示例:
            >>> params = QueryParams()
            >>> params.dict(exclude=["page"])
            {"limit": 10, "v_order": None, "v_order_field": None}
        """
        result = copy.deepcopy(self.__dict__)
        if exclude:
            for item in exclude:
                try:
                    del result[item]
                except KeyError:
                    pass
        return result

    def to_count(self, exclude: list[str] = None) -> dict:
        """
        生成用于计数查询的参数
        
        自动排除分页和排序相关参数
        
        参数:
            exclude: list[str] - 额外需要排除的参数名列表
            
        返回:
            dict: 适用于计数查询的参数字典
            
        示例:
            >>> params = QueryParams()
            >>> params.to_count()
            {}
        """
        params = self.dict(exclude=exclude)
        del params["page"]
        del params["limit"]
        del params["v_order"]
        del params["v_order_field"]
        return params


class Paging(QueryParams):
    """
    标准分页查询参数
    
    继承自QueryParams，实现标准分页参数处理
    用于FastAPI路由的依赖注入
    
    参数:
        page: int - 当前页码(默认1)
        limit: int - 每页数量(默认10)
        v_order_field: str - 排序字段名(可选)
        v_order: str - 排序方式(asc/desc, 可选)
        
    使用示例:
        @app.get("/items/")
        async def read_items(paging: Paging = Depends()):
            skip = (paging.page - 1) * paging.limit
            return await Item.find().skip(skip).limit(paging.limit)
    """
    def __init__(self, page: int = 1, limit: int = 10, v_order_field: str = None, v_order: str = None):
        super().__init__()
        self.page = page
        self.limit = limit
        self.v_order = v_order
        self.v_order_field = v_order_field


class IdList:
    """
    ID列表请求体
    
    用于接收批量ID操作的请求体
    通过FastAPI的Body接收JSON数组
    
    参数:
        ids: list[int] - ID列表(通过请求体传入)
        
    使用示例:
        @app.post("/items/delete")
        async def delete_items(ids: IdList = Depends()):
            await Item.delete_many({"_id": {"$in": ids.ids}})
            return {"message": "删除成功"}
            
    注意:
        需要在请求体中传入JSON数组，如: [1, 2, 3]
    """
    def __init__(self, ids: list[int] = Body(..., title="ID 列表")):
        self.ids = ids
