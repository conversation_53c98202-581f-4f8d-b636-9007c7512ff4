// 定义getters，用于从state中获取数据
const getters = {
  // 用户相关状态
  isUser: (state) => state.auth.isUser, // 判断用户是否登录
  isUserOpenid: (state) => state.auth.isUserOpenid, // 获取用户的OpenID
  isResetPassword: (state) => state.auth.isResetPassword, // 判断是否需要重置密码
  token: (state) => state.auth.token, // 获取用户的token
  avatar: (state) => state.auth.avatar, // 获取用户的头像
  name: (state) => state.auth.name, // 获取用户的名称
  roles: (state) => state.auth.roles, // 获取用户的角色
  permissions: (state) => state.auth.permissions, // 获取用户的权限
  telephone: (state) => state.auth.telephone, // 获取用户的电话号码

  // 应用相关状态
  version: (state) => state.app.version, // 获取应用版本
  title: (state) => state.app.title, // 获取应用标题
  logo: (state) => state.app.logo, // 判断是否显示logo
  logoImage: (state) => state.app.logoImage, // 获取logo图片地址
  footer: (state) => state.app.footer, // 判断是否显示页脚
  footerContent: (state) => state.app.footerContent, // 获取页脚内容
  icpNumber: (state) => state.app.icpNumber, // 获取备案号
  privacy: (state) => state.app.privacy, // 获取隐私政策
  agreement: (state) => state.app.agreement, // 获取用户协议
  siteUrl: (state) => state.app.siteUrl, // 获取源码地址
  WXEmail: (state) => state.app.WXEmail, // 获取官方邮箱
  WXPhone: (state) => state.app.WXPhone, // 获取服务热线

  // 字典相关状态
  dictObj: (state) => state.dict.dictObj // 获取字典对象
}

// 导出getters
export default getters