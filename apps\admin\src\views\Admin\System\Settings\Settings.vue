<script setup lang="ts">
// 导入Element Plus的标签页组件
import { ElTabs, ElTabPane } from 'element-plus'
// 导入Vue的响应式API
import { ref } from 'vue'
// 导入各个设置子组件
import Basic from './components/Basic.vue' // 基础设置
import Privacy from './components/Privacy.vue' // 隐私设置
import Agreement from './components/Agreement.vue' // 协议设置
import WXClient from './components/WechatServer.vue' // 微信服务设置
import Email from './components/Email.vue' // 邮件设置
import SMS from './components/SMS.vue' // 短信设置
// 导入内容包裹组件
import { ContentWrap } from '@/components/ContentWrap'
// 导入获取系统设置标签页的API
import { getSystemSettingsTabsApi } from '@/api/admin/system/settings'

// 定义组件选项
defineOptions({ name: 'SystemSettings' }) // 组件名称

// 当前激活的标签页名称，默认显示'web_basic'(基础设置)
const activeName = ref('web_basic')

// 存储标签页数据的响应式数组
const tabs = ref([] as Recordable[])

/**
 * 获取标签页列表数据
 * 从API获取'web'和'aliyun'类别的设置标签页
 */
const getList = async () => {
  const res = await getSystemSettingsTabsApi(['web', 'aliyun'])
  if (res) {
    tabs.value = res.data // 更新标签页数据
  }
}

// 组件创建时自动调用获取标签页数据
getList()
</script>

<template>
  <!-- 内容包裹容器 -->
  <ContentWrap>
    <!-- Element Plus的标签页组件，绑定当前激活的标签页 -->
    <ElTabs v-model="activeName">
      <!-- 遍历标签页数据 -->
      <template v-for="item in tabs" :key="item.id">
        <!-- 如果标签页不隐藏则显示 -->
        <ElTabPane v-if="!item.hidden" :name="item.tab_name" :label="item.tab_label">
          <!-- 根据标签页名称动态加载对应的子组件 -->
          <Basic v-if="item.tab_name === 'web_basic'" :tab-id="item.id" />
          <Privacy v-else-if="item.tab_name === 'web_privacy'" :tab-id="item.id" />
          <Agreement v-else-if="item.tab_name === 'web_agreement'" :tab-id="item.id" />
          <WXClient v-else-if="item.tab_name === 'wx_server'" :tab-id="item.id" />
          <Email v-else-if="item.tab_name === 'web_email'" :tab-id="item.id" />
          <SMS v-else-if="item.tab_name === 'aliyun_sms'" :tab-id="item.id" />
        </ElTabPane>
      </template>
    </ElTabs>
  </ContentWrap>
</template>

<!-- 组件样式部分(当前为空) -->
<style scoped lang="less"></style>
