<template>
  <div class="course-card" @click="$emit('click', course)">
    <div class="course-image">
      <img :src="course.image" alt="课程封面" />
      <div class="course-duration">{{ course.duration }}</div>
      <div class="course-type-badge" :class="typeClass">
        <van-icon :name="course.type === 'video' ? 'play-circle-o' : 'description'" />
        <span>{{ getTypeText(course.type) }}</span>
      </div>
    </div>
    <div class="course-content">
      <div class="course-title">{{ course.title }}</div>
      <div class="course-info">
        <span class="course-author">{{ course.author }}</span>
        <span class="course-level" :class="levelClass">{{ course.level }}</span>
      </div>
      <div class="course-stats">
        <div class="stat-item">
          <van-icon name="eye-o" />
          <span>{{ formatNumber(course.views) }}</span>
        </div>
        <div class="stat-item">
          <van-icon name="like-o" />
          <span>{{ formatNumber(course.likes) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  course: {
    type: Object,
    required: true
  }
})

// 根据课程级别设置不同的样式类
const levelClass = computed(() => {
  const levelMap = {
    '入门': 'level-basic',
    '进阶': 'level-advanced',
    '实战': 'level-case'
  }
  return levelMap[props.course.level] || 'level-basic'
})

// 根据课程类型设置不同的样式类
const typeClass = computed(() => {
  const typeMap = {
    'video': 'type-video',
    'article': 'type-article'
  }
  return typeMap[props.course.type] || 'type-article'
})

// 获取课程类型文本
const getTypeText = (type) => {
  const typeMap = {
    'video': '视频课程',
    'article': '图文教程'
  }
  return typeMap[type] || '图文教程'
}

// 格式化数字
const formatNumber = (num) => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num
}
</script>

<style scoped>
.course-card {
  margin-bottom: 16px;
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.course-image {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.course-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.course-content {
  padding: 12px;
}

.course-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin-bottom: 8px;
  line-height: 1.4;
}

.course-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.course-author {
  font-size: 14px;
  color: var(--text-color-regular);
}

.course-level {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.level-basic {
  background-color: #e8f4ff;
  color: #1989fa;
}

.level-advanced {
  background-color: #fef0e5;
  color: #ff6b00;
}

.level-case {
  background-color: #e8f7ef;
  color: #07c160;
}

.course-type-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.course-type-badge .van-icon {
  margin-right: 4px;
  font-size: 14px;
}

.type-video {
  background-color: rgba(92, 107, 192, 0.8);
  color: white;
}

.type-article {
  background-color: rgba(255, 152, 0, 0.8);
  color: white;
}

.course-stats {
  display: flex;
  font-size: 12px;
  color: var(--text-color-secondary);
}

.stat-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.stat-item .van-icon {
  margin-right: 4px;
  font-size: 14px;
}
</style>
