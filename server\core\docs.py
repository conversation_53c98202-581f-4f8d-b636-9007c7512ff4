from fastapi import FastAPI
from fastapi.openapi.docs import (
    get_redoc_html,
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html,
)


def custom_api_docs(app: FastAPI):
    """
    自定义配置接口本地静态文档

    此函数用于对FastAPI应用（app）的接口文档进行自定义配置，
    包含Swagger UI和ReDoc两种文档形式的配置，同时设置OAuth2重定向。
    """

    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        """
        自定义Swagger UI文档页面的路由处理函数
        """
        return get_swagger_ui_html(
            openapi_url=app.openapi_url,
            title=app.title + " - Swagger UI",
            oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
            swagger_js_url="/media/swagger_ui/swagger-ui-bundle.js",
            swagger_css_url="/media/swagger_ui/swagger-ui.css",
        )

    # 检查是否启用了OAuth2重定向
    if app.swagger_ui_oauth2_redirect_url:
        @app.get(app.swagger_ui_oauth2_redirect_url, include_in_schema=False)
        async def swagger_ui_redirect():
            """
            OAuth2重定向的处理函数
            """
            return get_swagger_ui_oauth2_redirect_html()

    @app.get("/redoc", include_in_schema=False)
    async def custom_redoc_html():
        """
        自定义ReDoc文档页面的路由处理函数
        """
        return get_redoc_html(
            openapi_url=app.openapi_url,
            title=app.title + " - ReDoc",
            redoc_js_url="/media/redoc_ui/redoc.standalone.js",
        )
