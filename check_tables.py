import pymysql

# 数据库连接参数
db_config = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '1234',
    'database': 'mxtt',
    'charset': 'utf8mb4'
}

try:
    # 连接到mxtt数据库
    connection = pymysql.connect(**db_config)
    print("成功连接到'mxtt'数据库")

    # 获取所有表
    with connection.cursor() as cursor:
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]

        if tables:
            print(f"数据库中有 {len(tables)} 个表:")
            for table in tables:
                print(f"- {table}")
        else:
            print("数据库中没有表")

    connection.close()

except Exception as e:
    print(f"操作失败: {e}")
