# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2021/10/19 15:47
# @File           : middleware.py
# @IDE            : PyCharm
# @desc           : 中间件

"""
官方文档——中间件：https://fastapi.tiangolo.com/tutorial/middleware/
官方文档——高级中间件：https://fastapi.tiangolo.com/advanced/middleware/
"""
import datetime
import json
import time
from fastapi import Request, Response
from core.logger import logger
from fastapi import FastAPI
from fastapi.routing import APIRoute
from user_agents import parse
from config.settings import OPERATION_RECORD_METHOD, MONGO_DB_ENABLE, IGNORE_OPERATION_FUNCTION, \
    DEMO_WHITE_LIST_PATH, DEMO, DEMO_BLACK_LIST_PATH
from utils.response import ErrorResponse
from apps.admin.services.record import OperationRecordDal
from core.database import mongo_getter
from utils import status


def write_request_log(request: Request, response: Response):
    """
    写入请求日志到日志文件
    
    记录格式:
    '{method} {url} {http_version}' {status_code} {charset} {content_length} {process_time}
    
    参数:
        request: Request - FastAPI请求对象
        response: Response - FastAPI响应对象
        
    日志示例:
    'GET /api/v1/users http/1.1' 200 None 123 0.123
    """
    http_version = f"http/{request.scope['http_version']}"
    content_length = response.raw_headers[0][1]
    process_time = response.headers["X-Process-Time"]
    content = f"basehttp.log_message: '{request.method} {request.url} {http_version}' {response.status_code}" \
              f"{response.charset} {content_length} {process_time}"
    logger.info(content)


def register_request_log_middleware(app: FastAPI):
    """
    注册请求日志中间件
    
    功能:
    1. 记录所有请求的访问日志
    2. 计算请求处理时间
    3. 在响应头中添加X-Process-Time
    
    参数:
        app: FastAPI - FastAPI应用实例
        
    中间件流程:
    1. 记录请求开始时间
    2. 调用下一个中间件或路由处理
    3. 计算处理时间并添加到响应头
    4. 写入访问日志
    
    日志内容包含:
    - 请求方法
    - 请求URL
    - HTTP版本
    - 响应状态码
    - 响应字符集
    - 响应内容长度
    - 处理时间
    """

    @app.middleware("http")
    async def request_log_middleware(request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        write_request_log(request, response)
        return response


def register_operation_record_middleware(app: FastAPI):
    """
    注册操作记录中间件
    
    功能:
    1. 记录认证用户的操作到MongoDB
    2. 记录详细的操作信息
    3. 支持配置忽略特定路由
    
    参数:
        app: FastAPI - FastAPI应用实例
        
    记录条件:
    1. 必须启用MongoDB(MONGO_DB_ENABLE=True)
    2. 请求必须包含认证信息(telephone)
    3. 请求方法必须在OPERATION_RECORD_METHOD中
    4. 路由名称不在IGNORE_OPERATION_FUNCTION中
    
    记录内容包含:
    - 用户信息(电话,ID,姓名)
    - 请求信息(URL,方法,IP)
    - 系统信息(操作系统,浏览器)
    - API信息(路径,摘要,标签)
    - 响应信息(状态码,内容长度)
    - 参数信息(查询参数,路径参数,请求体)
    - 处理时间
    - 创建时间
    """

    @app.middleware("http")
    async def operation_record_middleware(request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        if not MONGO_DB_ENABLE:
            return response
        telephone = request.scope.get('telephone', None)
        user_id = request.scope.get('user_id', None)
        user_name = request.scope.get('user_name', None)
        route = request.scope.get('route')
        if not telephone:
            return response
        elif request.method not in OPERATION_RECORD_METHOD:
            return response
        elif route.name in IGNORE_OPERATION_FUNCTION:
            return response
        process_time = time.time() - start_time
        user_agent = parse(request.headers.get("user-agent"))
        system = f"{user_agent.os.family} {user_agent.os.version_string}"
        browser = f"{user_agent.browser.family} {user_agent.browser.version_string}"
        query_params = dict(request.query_params.multi_items())
        path_params = request.path_params
        if isinstance(request.scope.get('body'), str):
            body = request.scope.get('body')
        else:
            body = request.scope.get('body').decode()
            if body:
                body = json.loads(body)
        params = {
            "body": body,
            "query_params": query_params if query_params else None,
            "path_params": path_params if path_params else None,
        }
        content_length = response.raw_headers[0][1]
        assert isinstance(route, APIRoute)
        document = {
            "process_time": process_time,
            "telephone": telephone,
            "user_id": user_id,
            "user_name": user_name,
            "request_api": request.url.__str__(),
            "client_ip": request.client.host,
            "system": system,
            "browser": browser,
            "request_method": request.method,
            "api_path": route.path,
            "summary": route.summary,
            "description": route.description,
            "tags": route.tags,
            "route_name": route.name,
            "status_code": response.status_code,
            "content_length": content_length,
            "create_datetime": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "params": json.dumps(params)
        }
        await OperationRecordDal(mongo_getter(request)).create_data(document)
        return response


def register_demo_env_middleware(app: FastAPI):
    """
    注册演示环境中间件
    
    功能:
    1. 在演示环境(DEMO=True)下限制非GET请求
    2. 支持黑名单(DEMO_BLACK_LIST_PATH)和白名单(DEMO_WHITE_LIST_PATH)
    
    参数:
        app: FastAPI - FastAPI应用实例
        
    限制规则:
    1. 仅对非GET请求生效
    2. 如果路径在黑名单中，返回403禁止访问
    3. 如果路径不在白名单中，返回禁止操作提示
    
    使用场景:
    用于保护演示环境数据不被修改
    """

    @app.middleware("http")
    async def demo_env_middleware(request: Request, call_next):
        path = request.scope.get("path")
        if request.method != "GET":
            print("路由：", path, request.method)
        if DEMO and request.method != "GET":
            if path in DEMO_BLACK_LIST_PATH:
                return ErrorResponse(
                    status=status.HTTP_403_FORBIDDEN,
                    code=status.HTTP_403_FORBIDDEN,
                    msg="演示环境，禁止操作"
                )
            elif path not in DEMO_WHITE_LIST_PATH:
                return ErrorResponse(msg="演示环境，禁止操作")
        return await call_next(request)


def register_jwt_refresh_middleware(app: FastAPI):
    """
    注册JWT刷新中间件
    
    功能:
    1. 在响应头中添加if-refresh标记
    2. 标记是否需要刷新JWT令牌
    
    参数:
        app: FastAPI - FastAPI应用实例
        
    工作原理:
    1. 从请求scope中获取if-refresh标记
    2. 将标记添加到响应头中
    3. 前端根据此标记决定是否刷新令牌
    
    使用场景:
    用于实现JWT令牌的无感刷新机制
    """

    @app.middleware("http")
    async def jwt_refresh_middleware(request: Request, call_next):
        response = await call_next(request)
        refresh = request.scope.get('if-refresh', 0)
        response.headers["if-refresh"] = str(refresh)
        return response
