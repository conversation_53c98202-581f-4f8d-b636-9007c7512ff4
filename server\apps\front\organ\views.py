#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : 视图路由

from fastapi import APIRouter, Depends, UploadFile
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession
from core.database import db_getter, redis_getter
from core.exception import CustomException
from apps.front.auth import AllUserAuth, OpenAuth
from . import crud, schemas
from utils.response import SuccessResponse, ErrorResponse
from utils import status

app = APIRouter()


# ---------------------------------------- 应用管理 ----------------------------------------
@app.get("/app/info", summary="获取应用信息", dependencies=[Depends(OpenAuth())], response_model=None)
async def get_app_info(
    app_id: int,
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """获取应用完整信息"""
    app_dal = crud.AppDal(db)
    result = await app_dal.get_app_full_info(app_id)
    return SuccessResponse(data=result)


# ---------------------------------------- 用户管理 ----------------------------------------
@app.post("/user/register", summary="用户注册", dependencies=[Depends(OpenAuth())], response_model=None)
async def user_register(
    data: schemas.UserIn,
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """用户注册"""
    user_dal = crud.UserDal(db)
    result = await user_dal.create_data(data)
    return SuccessResponse(data=result)


@app.get("/user/info", summary="获取当前用户信息", dependencies=[Depends(AllUserAuth())], response_model=None)
async def get_current_user_info(
    auth = Depends(AllUserAuth()),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """获取当前用户信息"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)
    user_dal = crud.UserDal(db)
    result = await user_dal.get_data(auth.user.id)
    return SuccessResponse(data=result)


@app.put("/user/info", summary="更新当前用户信息", dependencies=[Depends(AllUserAuth())], response_model=None)
async def update_current_user_info(
    data: schemas.UserUpdateBaseInfo,
    auth = Depends(AllUserAuth()),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """更新当前用户信息"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)
    user_dal = crud.UserDal(db)
    result = await user_dal.update_current_info(auth.user, data)
    return SuccessResponse(data=result)


@app.post("/user/password/reset", summary="重置密码", dependencies=[Depends(AllUserAuth())])
async def reset_password(
    data: schemas.ResetPwd,
    auth = Depends(AllUserAuth()),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """重置密码"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)
    user_dal = crud.UserDal(db)
    await user_dal.reset_current_password(auth.user, data)
    return SuccessResponse(msg="重置成功")


@app.post("/user/avatar", summary="更新头像", dependencies=[Depends(AllUserAuth())])
async def update_avatar(
    file: UploadFile,
    auth = Depends(AllUserAuth()),
    db: AsyncSession = Depends(db_getter)
) -> SuccessResponse:
    """更新头像"""
    if not auth.user:
        raise CustomException("用户未登录", code=401)
    # TODO: 实现头像上传逻辑
    return SuccessResponse(msg="上传成功")
