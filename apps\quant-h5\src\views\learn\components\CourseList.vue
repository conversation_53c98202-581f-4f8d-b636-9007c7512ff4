<template>
  <div class="course-list">
    <van-empty v-if="courses.length === 0" description="暂无课程" />
    <div v-else>
      <CourseCard
        v-for="course in courses"
        :key="course.id"
        :course="course"
        @click="viewCourseDetail(course)"
      />
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
import { useRouter } from 'vue-router'
import CourseCard from './CourseCard.vue'

const props = defineProps({
  courses: {
    type: Array,
    required: true
  }
})

const router = useRouter()

// 查看课程详情
const viewCourseDetail = (course) => {
  // 跳转到课程详情页面
  router.push(`/learn/detail/${course.id}`)
}
</script>

<style scoped>
.course-list {
  padding: 12px;
}
</style>
