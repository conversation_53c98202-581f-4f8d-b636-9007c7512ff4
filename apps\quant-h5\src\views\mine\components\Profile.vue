<template>
  <div class="user-profile">
    <!-- 用户信息头部 -->
    <div class="profile-header">
          <div class="profile-content">
        <div class="avatar-wrapper">
          <van-uploader :after-read="uploadAvatar" :max-count="1" :preview-size="80">
            <van-image
              round
              width="80"
              height="80"
              :src="userInfo.avatar || (userInfo.isGuest ? guestDefaultAvatar : userDefaultAvatar)"
            />
            <div class="avatar-edit-icon">
              <van-icon name="photograph" />
            </div>
          </van-uploader>
        </div>
        <div class="profile-info">
          <div class="nickname-wrapper">
            <h3>{{ userInfo.nickname }}</h3>
            <van-icon name="edit" @click="showNicknameDialog = true" />

            <!-- 操作按钮 -->
            <div class="actions-dropdown">
              <div class="settings-button" @click.stop="toggleActionsDropdown">
                <van-icon name="setting-o" size="16" />
                <span>设置操作</span>
                <van-icon name="arrow-down" size="12" class="dropdown-arrow" :class="{ 'dropdown-arrow-active': isActionsDropdownVisible }" />
              </div>
              <div v-if="isActionsDropdownVisible" class="actions-dropdown-content">
                <van-cell-group>
                  <van-cell v-if="!userInfo.isGuest" title="修改密码" icon="lock" @click.stop="handleEditPassword" />
                  <van-cell title="清除缓存" icon="delete" @click.stop="handleClearCache" />
                  <van-cell title="关于我们" icon="info-o" @click.stop="handleAbout" />
                  <van-cell v-if="!userInfo.isGuest" title="退出登录" icon="close" @click.stop="handleLogout" class="logout-cell" />
                </van-cell-group>
              </div>
              <div v-if="isActionsDropdownVisible" class="dropdown-overlay" @click.stop="toggleActionsDropdown"></div>
            </div>
          </div>

          <!-- 用户回测算力信息 -->
          <div class="user-compute-power">
            <div class="compute-power-info">
              <span class="compute-power-text">
                回测算力: <span class="compute-power-value">{{ userInfo.computePower }} 点</span>
                <van-icon name="question-o" class="help-icon" @click.stop="showComputePowerHelp" />
              </span>
            </div>
            <van-button
              size="mini"
              type="primary"
              class="recharge-button"
              @click="showRechargeDialog = true"
            >
              获取算力
            </van-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 游客提示 (仅对游客显示) -->
    <div v-if="userInfo.isGuest" class="guest-tip-card">
      <div class="guest-tip-header">
        <van-icon name="warning-o" class="tip-icon" />
        <div class="tip-title">游客模式</div>
      </div>

      <div class="guest-tip-content">
        <div class="tip-desc">
          <p>您当前为游客模式，<span class="highlight">回测算力值为0</span>，无法进行回测</p>
          <p>注册账号即可获得<span class="highlight">10点免费回测算力</span></p>
          <p>不同策略、不同参数会消耗不同的算力值，具体消耗将在回测执行页面显示</p>
        </div>

        <div class="tip-benefits">
          <div class="benefit-item">
            <van-icon name="gift-o" class="benefit-icon" />
            <span>免费回测算力</span>
          </div>
          <div class="benefit-item">
            <van-icon name="star-o" class="benefit-icon" />
            <span>收藏策略</span>
          </div>
          <div class="benefit-item">
            <van-icon name="chart-trending-o" class="benefit-icon" />
            <span>回测记录</span>
          </div>
        </div>
      </div>

      <div class="guest-tip-footer">
        <van-button type="primary" block @click="goToLogin" class="login-button">登录/注册</van-button>
      </div>
    </div>

    <!-- 昵称修改弹窗 -->
    <van-dialog
      v-model="showNicknameDialog"
      title="修改昵称"
      show-cancel-button
      @confirm="saveNickname"
    >
      <van-field
        v-model="newNickname"
        placeholder="请输入新昵称"
        maxlength="20"
        show-word-limit
      />
    </van-dialog>

    <!-- 算力充值弹窗 -->
    <van-popup
      v-model="showRechargeDialog"
      round
      position="bottom"
      :style="{ height: '40%' }"
    >
      <div class="recharge-dialog">
        <h3>回测算力充值</h3>
        <p class="recharge-desc">选择充值回测算力数量，不同策略和参数消耗的算力不同</p>

        <div class="recharge-options">
          <div
            v-for="option in rechargeOptions"
            :key="option.value"
            class="recharge-option"
            :class="{ active: rechargeAmount === option.value.toString() }"
            @click="rechargeAmount = option.value.toString()"
          >
            <div class="option-value">{{ option.value }}点</div>
            <div class="option-price">¥{{ option.price }}</div>
          </div>
        </div>

        <div class="recharge-actions">
          <van-button block type="primary" @click="rechargeComputePower" class="recharge-btn" :loading="rechargeLoading">
            立即充值算力
          </van-button>
          <van-button block plain type="default" @click="showRechargeDialog = false" class="cancel-btn">
            取消
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useBacktestStore } from '@/stores/backtest'
import { useStrategyStore } from '@/stores/strategy'
import { showToast, showSuccessToast, showConfirmDialog, showDialog } from 'vant'
import guestDefaultAvatar from '@/assets/avatars/guest-default.svg'
import userDefaultAvatar from '@/assets/avatars/user-default.svg'

// 获取store和路由
const router = useRouter()
const userStore = useUserStore()
const backtestStore = useBacktestStore()
const strategyStore = useStrategyStore()

// 定义emit
const emit = defineEmits(['refresh'])

// 用户信息
const userInfo = ref(userStore.userInfo)

// 弹窗控制
const showNicknameDialog = ref(false)
const showRechargeDialog = ref(false)
const showAboutDialog = ref(false)
const isActionsDropdownVisible = ref(false)

// 表单数据
const newNickname = ref('')
const rechargeAmount = ref('10')
const rechargeLoading = ref(false)

// 充值选项
const rechargeOptions = [
  { value: 10, price: 10 },
  { value: 50, price: 45 },
  { value: 100, price: 80 },
  { value: 200, price: 150 }
]

// 监听用户信息变化
watch(() => userStore.userInfo, (newValue) => {
  userInfo.value = newValue
}, { deep: true })

// 统计数据
const backtestCount = computed(() => backtestStore.recordCount || 0)
const collectionCount = computed(() => strategyStore.collectedStrategies.length || 0)

// 方法
const uploadAvatar = (file) => {
  // 实际项目中应上传到服务器
  userInfo.value.avatar = file.content
  userStore.updateAvatar(file.content)
  showSuccessToast('头像更新成功')
}

const saveNickname = () => {
  if (newNickname.value.trim()) {
    userInfo.value.nickname = newNickname.value
    userStore.updateNickname(newNickname.value)
    showSuccessToast('昵称修改成功')
  } else {
    showToast('昵称不能为空')
  }
}

// 充值算力
const rechargeComputePower = async () => {
  if (!rechargeAmount.value || parseFloat(rechargeAmount.value) <= 0) {
    showToast('请输入有效的充值数量')
    return
  }

  try {
    rechargeLoading.value = true

    // 调用API充值算力
    const amount = parseInt(rechargeAmount.value)
    const res = await userStore.rechargeComputePower(amount)

    if (res) {
      showRechargeDialog.value = false
      showSuccessToast(`成功充值${amount}点算力`)
      emit('refresh') // 通知父组件刷新数据
    } else {
      showToast('充值失败，请稍后重试')
    }
  } catch (error) {
    console.error('充值失败:', error)
    showToast('充值失败，请稍后重试')
  } finally {
    rechargeLoading.value = false
  }
}

// 跳转到登录页面
const goToLogin = () => {
  router.push('/login')
}

// 显示算力帮助提示
const showComputePowerHelp = () => {
  // 创建一个自定义弹窗，因为 Vant 的 showDialog 不支持直接渲染 HTML
  const helpContent = `<div class="help-dialog-content"><div class="help-section"><div class="help-title">回测算力介绍</div><p class="help-text">回测算力是进行量化策略回测的必要资源，每次回测都会消耗一定量的算力值。</p></div><div class="help-section"><div class="help-title">使用消耗规则</div><p class="help-text">• 单选标的策略：基础消耗1点<br>• 多选标的策略：基础消耗1点，每增加1个标的增加0.5点<br>• 动态标的策略：基础消耗2点<br>• 回测时间范围越长，消耗的算力也越多<br>• 具体消耗将在回测执行页面显示</p></div><div class="help-section"><div class="help-title">获取回测算力</div><p class="help-text">• 注册账号可获得10点免费回测算力<br>• 可通过"获取算力"按钮充值获取更多算力<br>• 参与活动可获得奖励算力</p></div></div>`;

  // 使用普通文本，不包含 HTML 标签
  showDialog({
    title: '回测算力说明',
    message: '加载中...',
    theme: 'round-button',
    confirmButtonText: '我知道了',
    confirmButtonColor: '#1989fa'
  });

  // 手动替换弹窗内容
  setTimeout(() => {
    const dialogMessage = document.querySelector('.van-dialog__message');
    if (dialogMessage) {
      dialogMessage.innerHTML = helpContent;
      // 添加样式
      const style = document.createElement('style');
      style.textContent = `
        .van-dialog__message {
          padding: 12px 16px !important;
        }
        .help-dialog-content {
          text-align: left;
          padding: 0;
          margin: 0;
        }
        .help-section {
          margin-bottom: 10px;
        }
        .help-section:last-child {
          margin-bottom: 0;
        }
        .help-title {
          font-weight: bold;
          color: #1989fa;
          margin: 0 0 4px 0;
          padding: 0;
          font-size: 15px;
          line-height: 1.2;
        }
        .help-text {
          margin: 0;
          padding: 0;
          font-size: 14px;
          line-height: 1.3;
        }
        .help-text br {
          line-height: 1.8;
        }
      `;
      document.head.appendChild(style);
    }
  }, 50);
}

// 监听自定义事件
const setupEventListeners = () => {
  // 监听显示充值弹窗事件
  document.addEventListener('show-recharge-dialog', () => {
    showRechargeDialog.value = true
  })
}

// 移除事件监听
const removeEventListeners = () => {
  document.removeEventListener('show-recharge-dialog', () => {
    showRechargeDialog.value = true
  })
}

// 操作菜单处理方法
const toggleActionsDropdown = () => {
  isActionsDropdownVisible.value = !isActionsDropdownVisible.value;

  // 如果打开下拉菜单，添加点击外部关闭的事件监听
  if (isActionsDropdownVisible.value) {
    document.addEventListener('click', handleOutsideClick);
    // 设置下拉菜单位置
    setTimeout(() => {
      const button = document.querySelector('.settings-button');
      const dropdown = document.querySelector('.actions-dropdown-content');
      if (button && dropdown) {
        const rect = button.getBoundingClientRect();
        dropdown.style.top = `${rect.bottom + 5}px`;
        dropdown.style.right = `${window.innerWidth - rect.right}px`;
      }
    }, 0);
  } else {
    document.removeEventListener('click', handleOutsideClick);
  }
}

// 处理点击外部关闭下拉菜单
const handleOutsideClick = (event) => {
  const button = document.querySelector('.settings-button');
  const dropdownContent = document.querySelector('.actions-dropdown-content');

  // 如果点击的不是按钮也不是下拉内容，则关闭下拉菜单
  if (button && !button.contains(event.target) &&
      dropdownContent && !dropdownContent.contains(event.target)) {
    isActionsDropdownVisible.value = false;
    document.removeEventListener('click', handleOutsideClick);
  }
}

const handleEditPassword = () => {
  isActionsDropdownVisible.value = false;
  showToast('修改密码功能开发中');
}

const handleClearCache = () => {
  isActionsDropdownVisible.value = false;
  showConfirmDialog({
    title: '清除缓存',
    message: '确定要清除应用缓存吗？这将不会删除您的账号数据。',
  }).then(() => {
    // 清除本地缓存
    localStorage.clear();
    sessionStorage.clear();
    showToast('缓存已清除');
  }).catch(() => {
    // 取消操作
  });
}

const handleAbout = () => {
  isActionsDropdownVisible.value = false;
  showDialog({
    title: '关于量化回测系统',
    message: '量化回测系统是一款专为量化交易爱好者设计的移动端应用，提供多种量化策略的回测功能，帮助用户验证策略有效性，优化交易决策。\n\n版本: v1.0.0',
    theme: 'round-button',
    confirmButtonText: '关闭'
  });
}

const handleLogout = () => {
  isActionsDropdownVisible.value = false;
  showConfirmDialog({
    title: '退出登录',
    message: '确定要退出登录吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    userStore.logout();
    showToast('已退出登录');
  }).catch(() => {
    // 取消操作
  });
}

// 初始化
onMounted(() => {
  // 初始化昵称输入框
  newNickname.value = userInfo.value.nickname

  // 设置事件监听
  setupEventListeners()

  // 添加类名以便父组件查找
  const rechargeBtn = document.querySelector('.recharge-button')
  if (rechargeBtn) {
    rechargeBtn.classList.add('recharge-button')
  }
})

// 组件销毁前清理事件监听
onBeforeUnmount(() => {
  // 移除事件监听
  removeEventListeners()

  // 移除下拉菜单的点击外部关闭事件
  document.removeEventListener('click', handleOutsideClick)
})
</script>

<style scoped>
.user-profile {
  padding: 0;
}

/* 用户信息头部 */
.profile-header {
  padding: 20px 16px;
  background-color: #fff;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  position: relative;
}

.profile-content {
  display: flex;
  align-items: center;
}

/* 操作下拉菜单 */
.actions-dropdown {
  position: relative;
  margin-left: auto;
  z-index: 2000;
}

.settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 13px;
  color: #323233;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  white-space: nowrap;
  width: auto;
}

.settings-button:hover {
  background-color: #e8e9eb;
}

.settings-button span {
  margin: 0 4px;
}

.dropdown-arrow {
  transition: transform 0.3s;
}

.dropdown-arrow-active {
  transform: rotate(180deg);
}

.actions-dropdown-content {
  position: fixed;
  width: 140px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  z-index: 2001;
  overflow: hidden;
}

:deep(.van-cell) {
  font-size: 14px;
  padding: 10px 12px;
  line-height: 22px;
}

:deep(.van-cell__title) {
  flex: 1;
}

:deep(.van-icon) {
  font-size: 16px;
}

.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1999;
}

.logout-cell {
  color: #ee0a24;
}

.avatar-wrapper {
  position: relative;
}

.avatar-edit-icon {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 24px;
  height: 24px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.profile-info {
  margin-left: 20px;
  flex: 1;
}

.nickname-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  width: 100%;
}

.nickname-wrapper h3 {
  margin: 0 8px 0 0;
  font-size: 17px;
  font-weight: bold;
  color: #323233;
}

.nickname-wrapper .van-icon[name="edit"] {
  color: #969799;
  font-size: 16px;
  padding: 4px;
}

.user-compute-power {
  margin-top: 6px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.compute-power-info {
  display: flex;
  align-items: center;
}



.compute-power-text {
  font-size: 14px;
  color: #323233;
  display: flex;
  align-items: center;
}

.compute-power-value {
  font-size: 15px;
  font-weight: bold;
  color: #ff976a;
  margin: 0 4px;
}

.help-icon {
  font-size: 16px;
  margin-left: 4px;
  color: #1989fa;
  vertical-align: middle;
  opacity: 0.8;
  padding: 2px;
}

.recharge-button {
  height: 28px;
  padding: 0 10px;
  font-size: 12px;
  border-radius: 14px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(25, 137, 250, 0.2);
  white-space: nowrap;
}

/* 游客提示卡片 */
.guest-tip-card {
  margin-top: 16px;
  background-color: #fff;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.guest-tip-header {
  background-color: #ff976a;
  color: white;
  padding: 14px 16px;
  display: flex;
  align-items: center;
}

.tip-icon {
  font-size: 22px;
  margin-right: 10px;
}

.tip-title {
  font-size: 17px;
  font-weight: bold;
}

.guest-tip-content {
  padding: 18px;
}

.tip-desc {
  font-size: 15px;
  color: #323233;
  line-height: 1.6;
  margin-bottom: 20px;
}

.tip-desc p {
  margin: 10px 0;
}

.tip-desc .highlight {
  color: #ff976a;
  font-weight: bold;
}

.tip-benefits {
  display: flex;
  justify-content: space-around;
  margin: 20px 0;
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #323233;
}

.benefit-icon {
  font-size: 28px;
  color: #ff976a;
  margin-bottom: 10px;
}

.guest-tip-footer {
  padding: 0 16px 20px;
}

.login-button {
  font-weight: 500;
  height: 44px;
  border-radius: 22px;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(255, 151, 106, 0.3);
}

/* 充值弹窗样式 */
.recharge-dialog {
  padding: 20px;
}

.recharge-dialog h3 {
  text-align: center;
  margin-bottom: 10px;
}

.recharge-desc {
  text-align: center;
  color: #969799;
  font-size: 14px;
  margin-bottom: 20px;
}

.recharge-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin: 20px 0;
}

.recharge-option {
  border: 1px solid #ebedf0;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.recharge-option.active {
  border-color: var(--primary-color);
  background-color: rgba(25, 137, 250, 0.05);
}

.option-value {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
}

.option-price {
  font-size: 14px;
  color: #969799;
  margin-top: 4px;
}

.recharge-actions {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.recharge-btn {
  border-radius: var(--border-radius-md);
}

.cancel-btn {
  border-radius: var(--border-radius-md);
}


</style>
