from sqlalchemy.ext.asyncio import AsyncSession

from apps.admin.schemas.help import IssueSimpleOut, IssueCategorySimpleOut
from core.crud import DalBase
from models.admin.help.issue import AdminIssue, AdminIssueCategory


class IssueDal(DalBase):

    def __init__(self, db: AsyncSession):
        super(IssueDal, self).__init__()
        self.db = db
        self.model = AdminIssue
        self.schema = IssueSimpleOut

    async def add_view_number(self, data_id: int) -> None:
        """
        更新常见问题查看次数+1
        """
        obj: AdminIssue = await self.get_data(data_id)
        obj.view_number = obj.view_number + 1 if obj.view_number else 1
        await self.flush(obj)


class IssueCategoryDal(DalBase):

    def __init__(self, db: AsyncSession):
        super(IssueCategoryDal, self).__init__()
        self.db = db
        self.model = AdminIssueCategory
        self.schema = IssueCategorySimpleOut
