# 标准库导入
import os
import subprocess
from enum import Enum

# 第三方库导入
from sqlalchemy import insert

# 本地应用/库导入
from config.settings import BASE_DIR, VERSION
from core.database import db_getter
from models.admin.help.issue import AdminIssueCategory, AdminIssue
from models.admin.organ.dept import AdminDept
from models.admin.organ.m2m import admin_auth_user_depts, admin_auth_user_roles
from models.admin.organ.menu import AdminMenu
from models.admin.organ.role import AdminRole
from models.admin.organ.user import AdminUser
from models.admin.system.dict import AdminDictType, AdminDictDetails
from models.admin.system.settings import AdminSystemSettingsTab, AdminSystemSettings
from utils.excel.excel_manage import ExcelManage


class Environment(str, Enum):
    """系统运行环境枚举类
    
    Attributes:
        dev (str): 开发环境
        pro (str): 生产环境
    """
    dev = "dev"
    pro = "pro"


class InitializeData:
    """系统数据初始化工具类
    
    提供从Excel文件读取数据并初始化数据库的功能。
    
    主要功能:
        - 执行数据库迁移(alembic)
        - 从Excel文件读取初始化数据
        - 批量插入数据到数据库表
        
    支持的数据表类型:
        - 权限管理相关表(部门、用户、角色、菜单等)
        - 系统配置相关表
        - 字典管理相关表
        - 帮助中心相关表
        
    使用示例:
        >>> init = InitializeData()
        >>> asyncio.run(init.run(Environment.dev))  # 初始化开发环境数据
        
    注意事项:
        - 需要提前配置好Excel数据文件(scripts/initialize/data/admin.xlsx)
        - 会执行数据库迁移操作(alembic revision --autogenerate)
        - 会清空并重新插入目标表数据
    """

    SCRIPT_DIR = os.path.join(BASE_DIR, 'scripts', 'initialize')

    def __init__(self):
        """初始化方法
        
        初始化Excel处理对象和数据存储结构，并立即加载数据
        """
        self.sheet_names: list[str] = []
        self.datas: dict[str, list[dict]] = {}
        self.ex: ExcelManage = None
        self.db = None

        self.__load_excel_data()
        self.__extract_sheet_data()

    @classmethod
    def migrate_model(cls, env: Environment = Environment.dev) -> None:
        """
        执行数据库迁移
        
        参数:
            env (Environment): 目标环境(dev/pro)
            
        功能:
        1. 生成迁移脚本
        2. 执行数据库迁移
        
        注意:
            - 使用alembic进行迁移
            - 会根据模型变化自动生成迁移脚本
        """
        subprocess.check_call(['alembic', '--name', f'{env.value}', 'revision', '--autogenerate', '-m', f'{VERSION}'],
                              cwd=BASE_DIR)
        subprocess.check_call(['alembic', '--name', f'{env.value}', 'upgrade', 'head'], cwd=BASE_DIR)
        print(f"环境：{env}  {VERSION} 数据库表迁移完成")

    def __load_excel_data(self) -> None:
        """加载并解析Excel初始化数据
        
        Raises:
            FileNotFoundError: 如果Excel文件不存在
            Exception: 如果Excel文件打开失败
            
        注意:
            - Excel文件路径: scripts/initialize/data/admin.xlsx
            - 使用只读模式打开Excel文件
        """
        self.ex = ExcelManage()
        excel_path = os.path.join(self.SCRIPT_DIR, 'data', 'admin.xlsx')
        print(f"DEBUG - Loading Excel file from: {excel_path}")
        print(f"DEBUG - Excel file exists: {os.path.exists(excel_path)}")
        self.ex.open_workbook(excel_path, read_only=True)
        self.sheet_names = self.ex.get_sheets()
        print(f"DEBUG - Found sheets: {self.sheet_names}")

    def __extract_sheet_data(self) -> None:
        """从各工作表中提取数据
        
        处理流程:
            1. 遍历所有工作表
            2. 读取表头和数据行
            3. 将每行数据转换为字典格式
            4. 按工作表名称存储数据
            
        数据结构示例:
            {
                "sheet_name": [
                    {"column1": value1, "column2": value2},
                    ...
                ]
            }
        """
        for sheet in self.sheet_names:
            sheet_data = []
            self.ex.open_sheet(sheet)
            headers = self.ex.get_header()
            datas = self.ex.readlines(min_row=2, max_col=len(headers))
            for row in datas:
                sheet_data.append(dict(zip(headers, row)))
            self.datas[sheet] = sheet_data

    async def __generate_data(self, table_name: str, model) -> None:
        """向指定表插入数据
        
        Args:
            table_name: 目标表名
            model: SQLAlchemy模型类或表对象
            
        Raises:
            ValueError: 如果表数据为空或格式不正确
            SQLAlchemyError: 如果数据库操作失败
            
        注意:
            - 会先清空表数据
            - 支持模型类和表对象两种形式
        """
        async_session = db_getter()
        db = await async_session.__anext__()
        datas = self.datas.get(table_name)

        if not datas:
            print(f"警告: {table_name} 表无数据可插入")
            return

        print(f"[DEBUG] 正在插入 {table_name} 表数据...")
        print(f"数据字段: {datas[0].keys()}")

        try:
            if hasattr(model, '__table__'):  # 模型类
                print(f"模型字段: {model.__table__.columns.keys()}")
                await db.execute(insert(model), datas)
            else:  # 表对象
                print(f"表字段: {model.columns.keys()}")
                await db.execute(model.insert(), datas)

            await db.flush()
            await db.commit()
            print(f"[SUCCESS] {table_name} 表数据插入完成")
        except Exception as e:
            await db.rollback()
            print(f"[ERROR] {table_name} 表数据插入失败: {str(e)}")
            raise

    async def generate_dept(self) -> None:
        """生成部门详情数据"""
        await self.__generate_data("admin_auth_dept", AdminDept)

    async def generate_user_dept(self) -> None:
        """生成用户关联部门详情数据"""
        await self.__generate_data("admin_auth_user_depts", admin_auth_user_depts)

    async def generate_menu(self) -> None:
        """生成菜单数据"""
        await self.__generate_data("admin_auth_menu", AdminMenu)

    async def generate_role(self) -> None:
        """生成角色数据"""
        await self.__generate_data("admin_auth_role", AdminRole)

    async def generate_user(self) -> None:
        """生成用户数据"""
        await self.__generate_data("admin_auth_user", AdminUser)

    async def generate_user_role(self) -> None:
        """生成用户角色关联数据"""
        await self.__generate_data("admin_auth_user_roles", admin_auth_user_roles)

    async def generate_system_tab(self) -> None:
        """生成系统配置分类数据"""
        await self.__generate_data("admin_system_settings_tab", AdminSystemSettingsTab)

    async def generate_system_config(self) -> None:
        """生成系统配置数据"""
        await self.__generate_data("admin_system_settings", AdminSystemSettings)

    async def generate_dict_type(self) -> None:
        """生成字典类型数据"""
        await self.__generate_data("admin_system_dict_type", AdminDictType)

    async def generate_dict_details(self) -> None:
        """生成字典详情数据"""
        await self.__generate_data("admin_system_dict_details", AdminDictDetails)

    async def generate_help_issue_category(self) -> None:
        """生成常见问题类别数据"""
        await self.__generate_data("admin_help_issue_category", AdminIssueCategory)

    async def generate_help_issue(self) -> None:
        """生成常见问题详情数据"""
        await self.__generate_data("admin_help_issue", AdminIssue)

    async def _init_auth_data(self) -> None:
        """初始化权限相关数据"""
        await self.generate_menu()
        await self.generate_role()
        await self.generate_dept()
        await self.generate_user()
        await self.generate_user_dept()
        await self.generate_user_role()

    async def _init_system_data(self) -> None:
        """初始化系统配置数据"""
        await self.generate_system_tab()
        await self.generate_system_config()
        await self.generate_dict_type()
        await self.generate_dict_details()

    async def _init_help_data(self) -> None:
        """初始化帮助中心数据"""
        await self.generate_help_issue_category()
        await self.generate_help_issue()

    async def run(self, env: Environment = Environment.pro) -> None:
        """执行完整初始化流程
        
        Args:
            env: 目标环境(dev/pro), 默认为生产环境
            
        Raises:
            RuntimeError: 如果初始化过程中出现错误
            
        执行流程:
            1. 执行数据库迁移
            2. 初始化权限相关数据
            3. 初始化系统配置数据
            4. 初始化帮助中心数据
        """
        try:
            print(f"[INFO] 开始 {env.value} 环境数据初始化...")
            self.migrate_model(env)

            print("[INFO] 正在初始化权限数据...")
            await self._init_auth_data()

            print("[INFO] 正在初始化系统配置数据...")
            await self._init_system_data()

            print("[INFO] 正在初始化帮助中心数据...")
            await self._init_help_data()

            print(f"[SUCCESS] {env.value} 环境 {VERSION} 数据初始化完成")
        except Exception as e:
            print(f"[ERROR] 数据初始化失败: {str(e)}")
            raise RuntimeError(f"数据初始化失败: {str(e)}")


if __name__ == "__main__":
    import asyncio

    print("开始执行数据初始化...")
    init = InitializeData()
    asyncio.run(init.run())
    print("数据初始化执行完成")
