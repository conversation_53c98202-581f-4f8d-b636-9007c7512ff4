<!--
  应用根组件 (App)

  该组件是整个应用的入口点，负责以下功能：
  1. 全局布局结构和导航
  2. 页面过渡动画
  3. 网络状态监测
  4. 全局错误处理
  5. 移动端适配优化
  6. 全局加载状态管理

  组件结构：
  - 错误边界：捕获应用中的所有错误
  - 网络状态提示：显示离线状态
  - 路由视图：显示当前页面内容
  - 底部导航栏：主要导航功能
  - 全局加载遮罩：异步操作的加载指示器
-->
<template>
  <div id="app">
    <!-- 全局错误边界 - 捕获应用中的所有错误 -->
    <ErrorBoundary :fallback="reloadApp">
      <!-- 网络状态提示 - 当网络断开时显示 -->
      <div v-if="!isOnline" class="offline-tip" role="alert">
        <van-icon name="warning-o" />
        <span>网络连接已断开，请检查网络设置</span>
      </div>

      <!-- 页面内容区域 - 使用过渡动画增强用户体验 -->
      <router-view v-slot="{ Component, route }">
        <transition name="fade" mode="out-in">
          <!-- 使用keep-alive缓存主要页面组件，提高性能 -->
          <keep-alive :include="['Backtest', 'Strategy', 'Learn', 'User']">
            <component :is="Component" :key="route.path" />
          </keep-alive>
        </transition>
      </router-view>

      <!-- 底部导航栏 - 主要导航功能 -->
      <van-tabbar
        v-model="active"
        route
        v-memo="[active]"
        fixed
        safe-area-inset-bottom
        class="app-tabbar"
      >
        <van-tabbar-item replace to="/testnow" icon="chart-trending-o">
          立即回测
        </van-tabbar-item>
        <van-tabbar-item replace to="/square" icon="apps-o">
          回测广场
        </van-tabbar-item>
        <van-tabbar-item replace to="/learn" icon="bookmark-o">
          学习课堂
        </van-tabbar-item>
        <van-tabbar-item replace to="/mine" icon="user-o">
          个人中心
        </van-tabbar-item>
      </van-tabbar>

      <!-- 全局加载遮罩 - 显示异步操作的进度 -->
      <van-overlay :show="globalLoading" z-index="2000">
        <div class="global-loading">
          <van-loading size="48px" color="#fff" vertical>加载中...</van-loading>
        </div>
      </van-overlay>
    </ErrorBoundary>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, provide } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import { showToast, showDialog } from 'vant'

/**
 * 路由和状态管理
 */
const route = useRoute()
const router = useRouter()

/**
 * 应用状态
 */
const active = ref(0)                           // 当前活动标签页索引
const isOnline = ref(navigator.onLine)          // 网络连接状态
const globalLoading = ref(false)                // 全局加载状态

/**
 * 提供全局加载状态控制
 *
 * 通过依赖注入向子组件提供全局加载状态控制方法，
 * 子组件可以通过inject('globalLoading')获取这些方法。
 */
provide('globalLoading', {
  show: () => { globalLoading.value = true },   // 显示全局加载遮罩
  hide: () => { globalLoading.value = false }   // 隐藏全局加载遮罩
})

/**
 * 路由到标签页索引的映射
 *
 * 用于根据当前路由路径确定应该激活哪个底部标签。
 */
const routeToTabMap = {
  '/testnow': 0,  // 立即回测
  '/square': 1,   // 策略广场
  '/learn': 2,    // 学习课堂
  '/mine': 3      // 个人中心
}

/**
 * 监听路由变化，更新活动标签
 *
 * 当路由路径变化时，根据路径前缀更新底部导航栏的活动标签。
 * 只有主标签页路径才会触发更新。
 */
watch(() => route.path, (path) => {
  // 提取基础路由路径（第一级路径）
  const baseRoute = '/' + path.split('/')[1]
  // 更新活动标签，如果映射中不存在则保持当前值
  active.value = routeToTabMap[baseRoute] ?? active.value
}, { immediate: true })

/**
 * 移动端视口高度修复
 *
 * 解决移动端浏览器中100vh不准确的问题，
 * 通过设置CSS变量--vh为视口高度的1%。
 */
const setAppHeight = () => {
  document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`)
}

/**
 * 网络状态变化处理
 *
 * 当网络状态变化时更新状态并显示提示。
 */
const handleNetworkChange = () => {
  isOnline.value = navigator.onLine

  if (!isOnline.value) {
    // 网络断开提示
    showToast({
      message: '网络连接已断开',
      type: 'fail',
      duration: 3000
    })
  } else {
    // 网络恢复提示
    showToast({
      message: '网络已恢复',
      type: 'success',
      duration: 2000
    })
  }
}

/**
 * 重新加载应用
 *
 * 当应用发生错误时，提示用户并重新加载页面。
 * 作为ErrorBoundary的fallback函数。
 */
const reloadApp = () => {
  showDialog({
    title: '应用出错',
    message: '应用遇到了一些问题，需要重新加载。',
    confirmButtonText: '重新加载',
    showCancelButton: false
  }).then(() => {
    window.location.reload()
  })
}

// MutationObserver实例，用于监听DOM变化
let appMutationObserver = null

/**
 * 优化页面滚动行为
 *
 * 使用工具函数确保在所有环境中页面都可以正常滚动
 */
const checkPageScrollable = () => {
  // 使用工具函数确保页面可滚动
  ensureScrollable()
}

/**
 * 优化页面滚动行为
 *
 * 处理触摸事件，优化在微信和普通浏览器中的滚动体验。
 * 只在极少数特定情况下阻止默认行为，其他情况允许正常滚动。
 *
 * @param {TouchEvent} e - 触摸事件对象
 */
const preventOverscroll = (e) => {
  // 获取触摸的目标元素
  const el = e.target

  // 如果是表单元素，不干预其默认行为
  if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA' || el.tagName === 'SELECT') {
    return
  }

  // 在微信环境中，不干预滚动行为
  if (isWechatEnvironment()) {
    return
  }

  // 默认允许滚动，只有在极少数情况下才阻止
  // 例如，只有当用户在不可滚动元素上尝试滚动且会导致页面弹跳时才阻止

  // 检查是否是特殊情况（例如，在某些特定元素上的滚动可能需要阻止）
  // 但在大多数情况下，我们希望允许滚动
  const shouldPrevent = false // 默认不阻止滚动

  if (shouldPrevent && e.cancelable) {
    e.preventDefault()
  }
}

// 导入微信环境工具函数
import { isWechatEnvironment, setWechatTitle, ensureScrollable } from '@/utils/wechat'

/**
 * 监听路由标题变化
 * 确保在微信环境中正确设置标题
 */
const setupTitleWatcher = () => {
  // 在微信环境中，监听路由变化，确保标题正确设置
  if (isWechatEnvironment()) {
    watch(() => route.meta.title, (title) => {
      if (title) {
        // 使用专门的微信标题设置函数
        setWechatTitle(title)
      }
    }, { immediate: true })
  }
}

/**
 * 组件挂载时设置事件监听器和初始状态
 */
onMounted(() => {
  // 设置视口高度变量
  setAppHeight()

  // 设置标题监听
  setupTitleWatcher()

  // 添加窗口大小变化监听
  window.addEventListener('resize', setAppHeight)

  // 添加网络状态变化监听
  window.addEventListener('online', handleNetworkChange)
  window.addEventListener('offline', handleNetworkChange)

  // 添加触摸事件处理，优化滚动体验
  // 在微信环境中使用 passive: true 提高性能
  document.body.addEventListener('touchmove', preventOverscroll, {
    passive: isWechatEnvironment()
  })

  // 添加滚动检测
  checkPageScrollable()
  window.addEventListener('resize', checkPageScrollable)

  // 监听DOM变化，检测内容高度变化
  appMutationObserver = new MutationObserver(() => {
    // 延迟执行，避免频繁调用
    setTimeout(checkPageScrollable, 100)
  })
  appMutationObserver.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true
  })

  // 路由变化时隐藏全局加载并检查滚动
  router.beforeEach(() => {
    globalLoading.value = false
    // 路由变化后检查滚动状态
    setTimeout(checkPageScrollable, 300)
    return true
  })
})

/**
 * 组件卸载时清理事件监听器和资源
 */
onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', setAppHeight)

  // 移除网络状态变化监听
  window.removeEventListener('online', handleNetworkChange)
  window.removeEventListener('offline', handleNetworkChange)

  // 移除滚动检测
  window.removeEventListener('resize', checkPageScrollable)

  // 移除触摸事件处理
  document.body.removeEventListener('touchmove', preventOverscroll)

  // 断开MutationObserver连接
  if (appMutationObserver) {
    appMutationObserver.disconnect()
    appMutationObserver = null
  }
})
</script>

<style scoped>
/* 底部导航栏样式 */
.app-tabbar {
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03); /* 更柔和的阴影 */
  z-index: 100;
  border-top: 1px solid rgba(0, 0, 0, 0.05); /* 添加顶部边框 */
}

/* 页面过渡动画 */
:deep(.fade-enter-active),
:deep(.fade-leave-active) {
  transition: opacity 0.2s ease;
}

:deep(.fade-enter-from),
:deep(.fade-leave-to) {
  opacity: 0;
}

/* 全局加载样式 */
.global-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 离线提示 */
.offline-tip {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--warning-color);
  color: #fff;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  z-index: 2001;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.offline-tip .van-icon {
  margin-right: 8px;
  font-size: 16px;
}
</style>
