<template>
  <div class="backtest-square">
    <van-nav-bar
      title="回测广场"
      fixed
      placeholder
      class="nav-bar"
    />

    <div class="content-container">
      <!-- 筛选器 -->
      <Filter
        v-model="filterParams"
        @filter-change="handleFilterChange"
        class="filter-container"
      />

      <!-- 使用错误边界和加载容器 -->
      <ErrorBoundary>
        <LoadingContainer :loading="loading">
          <!-- 空状态 -->
          <div v-if="filteredBacktests.length === 0" class="empty-container">
            <van-empty
              description="没有找到符合条件的回测案例"
              image="search"
            >
              <template #bottom>
                <van-button
                  round
                  type="primary"
                  size="small"
                  @click="resetFilters"
                  icon="replay"
                  class="reset-button"
                >
                  重置筛选条件
                </van-button>
              </template>
            </van-empty>
          </div>

          <!-- 回测列表 -->
          <template v-else>
            <List
              :backtests="filteredBacktests"
              :loading="loadingMore"
              :has-more="hasMore"
              class="backtest-list"
              @item-click="navigateToDetail"
              @like="handleLike"
              @collect="toggleCollect"
              @comment="navigateToDetailWithComment"
              @view-detail="navigateToDetail"
              @load-more="loadMoreBacktests"
            />
          </template>
        </LoadingContainer>
      </ErrorBoundary>
    </div>

    <!-- 操作反馈提示 -->
    <van-toast id="backtest-toast" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, inject } from 'vue'
import { useRouter } from 'vue-router'
import { useStrategyStore } from '@/stores/strategy'
import { useUserStore } from '@/stores/user'
import { showToast } from 'vant'

/**
 * 回测广场页面
 * @module BacktestSquare
 * @description 展示回测案例列表，支持筛选、排序和详情查看
 */
import Filter from './components/Filter.vue'
import List from './components/List.vue'
import LoadingContainer from '@/components/LoadingContainer.vue'
import ErrorBoundary from '@/components/ErrorBoundary.vue'

// 路由和状态管理
const router = useRouter()
const strategyStore = useStrategyStore()
const userStore = useUserStore()

// 全局加载状态
const globalLoading = inject('globalLoading', {
  show: () => {},
  hide: () => {}
})

// 加载状态
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)

// 筛选参数
const filterParams = ref({
  strategyType: 'all',    // 策略类型筛选
  sortBy: 'latest',       // 排序方式: latest|likes|favorites|return
  returnRange: 'all',     // 收益率范围: all|above10|above20|above50
  keywords: ''            // 关键词搜索
})

// 回测列表
const backtests = ref([])

/**
 * 筛选和排序后的回测列表
 * @computed filteredBacktests
 * @returns {Array} 处理后的回测数组
 * @description 根据筛选条件和排序规则处理回测列表
 */
const filteredBacktests = computed(() => {
  let list = backtests.value

  // 应用策略类型筛选
  if (filterParams.value.strategyType !== 'all') {
    list = list.filter(b => b.strategy.type === filterParams.value.strategyType)
  }

  // 应用收益率范围筛选
  if (filterParams.value.returnRange !== 'all') {
    const minReturn = {
      'above10': 10,
      'above20': 20,
      'above50': 50
    }[filterParams.value.returnRange] || 0

    list = list.filter(b => parseFloat(b.result.finalReturn) >= minReturn)
  }

  // 应用排序
  if (filterParams.value.sortBy === 'likes') {
    list.sort((a, b) => strategyStore.getLikeCount(b.id) - strategyStore.getLikeCount(a.id))
  } else if (filterParams.value.sortBy === 'favorites') {
    // 这里使用模拟的收藏数进行排序
    list.sort((a, b) => (b.collectCount || 0) - (a.collectCount || 0))
  } else if (filterParams.value.sortBy === 'return') {
    list.sort((a, b) => parseFloat(b.result.finalReturn) - parseFloat(a.result.finalReturn))
  } else {
    // 默认按最新发布排序
    list.sort((a, b) => new Date(b.date) - new Date(a.date))
  }

  // 关键字搜索
  if (filterParams.value.keywords) {
    const kw = filterParams.value.keywords.toLowerCase()
    list = list.filter(b =>
      b.strategy.name.toLowerCase().includes(kw) ||
      (b.description && b.description.toLowerCase().includes(kw)) ||
      b.strategy.tags.some(tag => tag.toLowerCase().includes(kw))
    )
  }

  return list
})

/**
 * 处理筛选条件变化
 * @method handleFilterChange
 * @param {Object} params - 新的筛选参数
 */
const handleFilterChange = (params) => {
  filterParams.value = params
  // 重新加载数据
  fetchBacktests()
}

/**
 * 重置筛选条件
 * @method resetFilters
 */
const resetFilters = () => {
  filterParams.value = {
    strategyType: 'all',
    sortBy: 'latest',
    returnRange: 'all',
    keywords: ''
  }
  // 重新加载数据
  fetchBacktests()
}

/**
 * 导航到回测详情页
 * @method navigateToDetail
 * @param {Object} backtest - 回测对象
 */
const navigateToDetail = (backtest) => {
  router.push({
    path: `/square/detail/${backtest.id}`
  })
}

/**
 * 导航到回测详情页并滚动到评论区
 * @method navigateToDetailWithComment
 * @param {Object} backtest - 回测对象
 */
const navigateToDetailWithComment = (backtest) => {
  router.push({
    path: `/square/detail/${backtest.id}`,
    query: { scrollToComment: 'true' }
  })
}

/**
 * 切换收藏状态
 * @method toggleCollect
 * @param {Number} backtestId - 回测ID
 */
const toggleCollect = (backtestId) => {
  const backtest = backtests.value.find(b => b.id === backtestId)
  if (!backtest) return

  const isCollected = strategyStore.toggleCollect(backtest.strategy.id)

  // 更新收藏数
  if (isCollected) {
    backtest.collectCount = (backtest.collectCount || 0) + 1
  } else if (backtest.collectCount > 0) {
    backtest.collectCount -= 1
  }

  showToast({
    type: isCollected ? 'success' : 'primary',
    message: isCollected ? '已添加到收藏' : '已取消收藏',
    position: 'bottom'
  })
}

/**
 * 处理点赞操作
 * @method handleLike
 * @param {Object} data - 点赞参数
 * @param {Number} data.backtestId - 回测ID
 */
const handleLike = ({ backtestId }) => {
  const backtest = backtests.value.find(b => b.id === backtestId)
  if (!backtest) return

  const isLiked = strategyStore.toggleLike(backtest.id)

  showToast({
    type: 'success',
    message: isLiked ? '点赞成功' : '已取消点赞',
    position: 'bottom'
  })
}

/**
 * 加载更多回测
 * @method loadMoreBacktests
 */
const loadMoreBacktests = async () => {
  if (loadingMore.value || !hasMore.value) return

  loadingMore.value = true

  try {
    // 模拟加载更多数据
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 生成更多模拟数据
    const moreBacktests = generateMockBacktests(5)
    backtests.value.push(...moreBacktests)

    // 如果数据超过30条，设置没有更多
    if (backtests.value.length > 30) {
      hasMore.value = false
    }
  } catch (error) {
    console.error('加载更多回测失败:', error)
    showToast({
      type: 'fail',
      message: '加载更多失败，请稍后重试',
      position: 'bottom'
    })
  } finally {
    loadingMore.value = false
  }
}

/**
 * 获取回测列表
 * @method fetchBacktests
 */
const fetchBacktests = async () => {
  try {
    loading.value = true
    globalLoading.show()

    // 确保策略数据已加载
    if (strategyStore.strategies.length === 0) {
      await strategyStore.fetchStrategies()
    }

    // 模拟API请求延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    // 生成模拟回测数据
    backtests.value = generateMockBacktests(15)
    hasMore.value = true

  } catch (error) {
    console.error('加载回测失败:', error)
    showToast({
      type: 'fail',
      message: '加载回测失败，请稍后重试',
      position: 'middle'
    })

    // 将错误传递给错误边界
    throw new Error('加载回测数据失败，请稍后重试')
  } finally {
    loading.value = false
    globalLoading.hide()
  }
}

/**
 * 生成模拟回测数据
 * @method generateMockBacktests
 * @param {Number} count - 生成数量
 * @returns {Array} 模拟回测数组
 */
const generateMockBacktests = (count) => {
  const result = []
  const strategies = strategyStore.strategies

  // 如果没有策略数据，返回空数组
  if (!strategies || strategies.length === 0) {
    console.warn('没有可用的策略数据')
    return result
  }

  for (let i = 0; i < count; i++) {
    const strategy = strategies[Math.floor(Math.random() * strategies.length)]
    const finalReturn = (Math.random() * 50 - 10).toFixed(2)
    const annualReturn = (Math.random() * 40 - 5).toFixed(2)
    const maxDrawdown = (Math.random() * 30).toFixed(2)
    const winRate = (Math.random() * 30 + 50).toFixed(2)

    // 生成随机日期（过去30天内）
    const date = new Date()
    date.setDate(date.getDate() - Math.floor(Math.random() * 30))

    const backtest = {
      id: Date.now() + i,
      strategy: { ...strategy },
      asset: strategy.type === 'trend' ? { code: '000001', name: '上证指数' } :
             [{ code: '000001', name: '上证指数' }, { code: '399001', name: '深证成指' }],
      parameters: strategy.parameters.map(p => ({
        label: p.label,
        value: p.default
      })),
      result: {
        finalReturn,
        annualReturn,
        maxDrawdown,
        winRate
      },
      date: date,
      userName: userStore.userInfo.nickname,
      description: getRandomDescription(),
      collectCount: Math.floor(Math.random() * 50)
    }

    result.push(backtest)

    // 初始化点赞数据 - 使用 setInitialLikes 方法
    if (!strategyStore.getLikeCount(backtest.id)) {
      // 设置一个随机的初始点赞数
      const initialLikes = Math.floor(Math.random() * 100)
      strategyStore.setInitialLikes(backtest.id, initialLikes)
    }
  }

  return result
}

/**
 * 获取随机描述文本
 * @method getRandomDescription
 * @returns {String} 随机描述文本
 */
const getRandomDescription = () => {
  const descriptions = [
    '这个策略在大盘震荡时表现良好，适合稳健型投资者。',
    '通过均线交叉信号进行交易，回测结果显示在牛市中表现优异。',
    '结合了动量和趋势因子，能够有效捕捉市场转折点。',
    '基于波动率的策略，在高波动市场中表现出色，但需要注意风险控制。',
    '这是我优化后的版本，相比原策略提高了胜率和年化收益。',
    '适合长期投资，交易频率较低但胜率较高。',
    '在不同市场环境下都有稳定表现，最大回撤控制得当。',
    '结合了基本面和技术面分析，能够有效规避市场风险。',
    '这个策略在A股市场测试效果良好，推荐大家尝试。',
    '经过多次优化调整的策略，参数已经相对稳定。'
  ]

  return descriptions[Math.floor(Math.random() * descriptions.length)]
}

/**
 * 加载数据
 * @lifecycle onMounted
 */
onMounted(async () => {
  await fetchBacktests()
})
</script>

<style scoped>
.backtest-square {
  min-height: 100vh;
  background-color: var(--background-color);
  padding-bottom: calc(var(--tabbar-height) + var(--safe-area-inset-bottom));
}

.nav-bar {
  background-color: var(--background-color-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03); /* 更柔和的阴影 */
  margin-bottom: 10px; /* 减少底部边距 */
  padding: 0; /* 移除内边距 */
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 0; /* 移除组件之间的间距，由子组件自己控制边距 */
}

.filter-container {
  margin: 0;
  border-radius: 0;
  box-shadow: none;
  position: sticky;
  top: calc(var(--header-height) + var(--safe-area-inset-top));
  z-index: 10;
  background-color: #fff;
}

.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  padding: var(--spacing-xl);
}

/* 这个样式在List组件中已定义，这里可以移除 */

.strategy-detail-sheet {
  max-height: 90vh;
  overflow-y: auto;
}

:deep(.van-action-sheet__header) {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-lg);
  color: var(--text-color-primary);
}

.reset-button {
  box-shadow: 0 2px 8px rgba(25, 137, 250, 0.2);
}

.reset-button .van-icon {
  font-size: 14px;
  margin-right: 2px;
}
</style>
