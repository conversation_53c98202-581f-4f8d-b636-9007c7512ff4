<!--
  错误边界组件 (ErrorBoundary)

  该组件用于捕获子组件树中的 JavaScript 错误，防止整个应用崩溃，
  并显示备用 UI。类似于 React 的 Error Boundaries 功能。

  功能：
  1. 捕获子组件中的所有 JavaScript 错误
  2. 显示友好的错误提示界面
  3. 提供重试和返回首页的选项
  4. 向子组件提供错误捕获方法

  使用方式：
  <ErrorBoundary :fallback="自定义重试函数">
    <YourComponent />
  </ErrorBoundary>
-->
<template>
  <div class="error-boundary">
    <!-- 当没有错误时，渲染子组件 -->
    <slot v-if="!error"></slot>

    <!-- 错误状态显示 -->
    <div v-else class="error-container">
      <van-empty
        :image="errorImage"
        :description="errorMessage"
      >
        <template #bottom>
          <div class="error-actions">
            <van-button
              round
              type="primary"
              size="small"
              @click="retry"
              class="retry-button"
            >
              重试
            </van-button>
            <van-button
              round
              plain
              type="primary"
              size="small"
              @click="goHome"
              class="home-button"
            >
              返回首页
            </van-button>
          </div>
        </template>
      </van-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, onErrorCaptured, provide } from 'vue'
import { useRouter } from 'vue-router'

/**
 * 组件属性定义
 *
 * @property {Function} fallback - 可选的自定义重试函数，当用户点击重试按钮时调用
 */
const props = defineProps({
  fallback: {
    type: Function,
    default: null
  }
})

// 路由实例，用于导航回首页
const router = useRouter()

// 错误状态管理
const error = ref(null)                           // 捕获的错误对象
const errorMessage = ref('出错了，请稍后重试')     // 显示给用户的错误消息
const errorImage = ref('error')                   // 错误图标类型

/**
 * 重置错误状态
 * 清除错误对象和错误消息，恢复到初始状态
 */
const resetError = () => {
  error.value = null
  errorMessage.value = '出错了，请稍后重试'
}

/**
 * 重试操作处理
 * 重置错误状态，并调用自定义的回退函数（如果提供）
 */
const retry = () => {
  resetError()
  if (props.fallback) {
    props.fallback()
  }
}

/**
 * 返回首页操作处理
 * 重置错误状态，并导航回应用首页
 */
const goHome = () => {
  resetError()
  router.replace('/')
}

/**
 * 错误捕获钩子
 * 捕获子组件树中的所有 JavaScript 错误
 *
 * @param {Error} err - 捕获的错误对象
 * @param {Component} _instance - 触发错误的组件实例
 * @param {string} info - 错误信息
 * @returns {boolean} 返回 false 阻止错误继续向上传播
 */
onErrorCaptured((err, _instance, info) => {
  console.error('ErrorBoundary 捕获到错误:', err)
  console.error('错误信息:', info)

  // 保存错误对象
  error.value = err

  // 提取错误消息
  if (err.message) {
    errorMessage.value = `${err.message}`
  } else if (typeof err === 'string') {
    errorMessage.value = err
  }

  // 阻止错误继续传播
  return false
})

/**
 * 提供错误处理方法给子组件
 * 允许子组件主动触发错误边界
 */
provide('errorBoundary', {
  /**
   * 捕获错误方法
   *
   * @param {Error|string} err - 错误对象或错误字符串
   * @param {string} customMessage - 可选的自定义错误消息
   */
  captureError: (err, customMessage) => {
    error.value = err
    if (customMessage) {
      errorMessage.value = customMessage
    } else if (err.message) {
      errorMessage.value = `${err.message}`
    }
  }
})
</script>

<style scoped>
.error-boundary {
  width: 100%;
  height: 100%;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: var(--spacing-xl);
}

.error-actions {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-md);
}

.home-button {
  margin-left: var(--spacing-md);
}

.retry-button:active,
.home-button:active {
  transform: scale(0.98);
}
</style>
