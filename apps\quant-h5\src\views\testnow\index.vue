<template>
  <div class="backtest-container">
    <!-- 页面标题 -->
    <van-nav-bar
      class="nav-bar"
      fixed
      placeholder
      title="立即回测"
    />

    <div class="content-container">
      <!-- 步骤指示器 -->
      <div class="custom-steps-nav">
        <!-- 步骤线 -->
        <div class="steps-line"></div>

        <!-- 步骤项 - 使用HTML图标代替Vant图标 -->
        <div :class="{ active: activeStep >= 0, completed: activeStep > 0 }" class="step-item">
          <div class="step-icon-container">
            <van-icon name="setting-o" />
          </div>
          <div class="step-text">策略</div>
        </div>

        <div :class="{ active: activeStep >= 1, completed: activeStep > 1 }" class="step-item">
          <div class="step-icon-container">
            <van-icon name="chart-trending-o" />
          </div>
          <div class="step-text">标的</div>
        </div>

        <div :class="{ active: activeStep >= 2, completed: activeStep > 2 }" class="step-item">
          <div class="step-icon-container">
            <van-icon name="records" />
          </div>
          <div class="step-text">参数</div>
        </div>

        <div :class="{ active: activeStep >= 3, completed: activeStep > 3 }" class="step-item">
          <div class="step-icon-container">
            <van-icon name="play-circle-o" />
          </div>
          <div class="step-text">执行</div>
        </div>

        <div :class="{ active: activeStep >= 4 || showResult, completed: activeStep > 4 }" class="step-item">
          <div class="step-icon-container">
            <van-icon name="description" />
          </div>
          <div class="step-text">报告</div>
        </div>
      </div>

      <!-- 步骤内容区 -->
      <div class="step-content">
        <!-- 使用错误边界包装组件 -->
        <ErrorBoundary>
          <!-- 使用 Transition 组件添加过渡效果 -->
          <transition mode="out-in" name="slide-fade">
            <LoadingContainer :loading="stepLoading" :text="loadingText">
              <StrategySelect
                v-if="activeStep === 0"
                key="strategy-select"
                v-model="backtestData.strategy"
                @select="nextStep"
              />

              <template v-else-if="activeStep === 1">
                <!-- 根据策略类型决定使用单选或多选标的组件 -->
                <MultiAssetSelect
                  v-if="isMultiAssetStrategy"
                  key="multi-asset-select"
                  v-model="backtestData.asset"
                  :strategy="backtestData.strategy"
                />
                <AssetSelect
                  v-else
                  key="asset-select"
                  v-model="backtestData.asset"
                  :strategy="backtestData.strategy"
                />
              </template>

              <ParameterForm
                v-else-if="activeStep === 2"
                key="parameter-form"
                :asset="backtestData.asset"
                :initial-params="backtestData.parameters"
                :strategy="backtestData.strategy"
                @submit="saveParameters"
              />

              <Execution
                v-else-if="activeStep === 3"
                key="execution"
                :backtest-data="backtestData"
                @complete="onBacktestComplete"
              />

              <TestReport
                v-else-if="activeStep === 4"
                key="test-report"
                :record-id="currentRecordId"
                @reset="handleResetBacktest"
              />
            </LoadingContainer>
          </transition>
        </ErrorBoundary>
      </div>

      <!-- 导航按钮 - 仅在非第一步时显示 -->
      <div v-if="activeStep > 0" class="action-buttons">
        <!-- 使用div代替van-button，完全自定义按钮 -->
        <button
          :disabled="isLoading"
          class="custom-button prev-button"
          @click="prevStep"
        >
          <span class="button-text">上一步</span>
        </button>

        <button
          v-if="activeStep < 3 && canProceed"
          :disabled="isLoading"
          class="custom-button next-button"
          @click="nextStep"
        >
          <span class="button-text">下一步</span>
        </button>

        <button
          v-if="activeStep === 3 && status === 'idle'"
          :disabled="isLoading"
          class="custom-button next-button"
          @click="startBacktest"
        >
          <span class="button-text">开始回测</span>
        </button>
      </div>
    </div> <!-- 闭合content-container -->

    <!-- 进度指示器 -->
    <van-overlay :show="isLoading" z-index="1000">
      <div class="loading-container">
        <van-loading color="var(--primary-color)" size="48px" type="spinner" />
        <p class="loading-text">
          {{ loadingText }}
        </p>
      </div>
    </van-overlay>
  </div>
</template>

<script setup>
  // ==================== 导入部分 ====================
  // Vue 核心
  import { computed, inject, onMounted, ref, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  // 组件
  import AssetSelect from './components/AssetSelect/OneSelect.vue'
  import MultiAssetSelect from './components/AssetSelect/MultiSelect.vue'
  import StrategySelect from './components/StrategySelect.vue'
  import ParameterForm from './components/TestParams/MacdForm.vue'
  import Execution from './components/Execution.vue'
  import TestReport from './components/TestReport.vue'
  import LoadingContainer from '@/components/LoadingContainer.vue'
  import ErrorBoundary from '@/components/ErrorBoundary.vue'

  // 状态管理
  import { useAssetStore } from '@/stores/asset'
  import { useStrategyStore } from '@/stores/strategy'
  import { useBacktestStore } from '@/stores/backtest'
  import { useUserStore } from '@/stores/user'

  // UI 组件
  import { showToast } from 'vant'

  // ==================== 状态定义 ====================
  const route = useRoute()
  const router = useRouter()
  const assetStore = useAssetStore()
  const strategyStore = useStrategyStore()
  const backtestStore = useBacktestStore()
  const userStore = useUserStore()
  const globalLoading = inject('globalLoading', {
    show: () => {
    }, hide: () => {
    }
  })

  // 视图状态
  const showResult = ref(false) // 是否显示结果页面
  const currentRecordId = ref(null) // 当前回测记录ID

  // 步骤状态
  const activeStep = ref(0) // 当前步骤索引 (0-4)
  const stepLoading = ref(false) // 步骤切换加载状态

  // 执行状态
  const status = ref('idle') // 回测状态: idle | running | completed

  // 加载状态
  const isLoading = ref(false) // 全局加载状态
  const loadingText = ref('加载中...') // 加载提示文本

  // 回测数据
  const backtestData = ref({
    asset: null, // 标的 (单个对象或数组)
    strategy: null, // 策略对象
    parameters: { // 回测参数
      startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 3, new Date().getDate()), // 默认3个月前
      endDate: new Date(), // 默认当前日期
      initialCapital: userStore.userInfo?.settings?.defaultCapital || 100000, // 默认10万
      strategyParams: {} // 策略特定参数
    },
    result: null // 回测结果
  })

  // ==================== 计算属性 ====================
  /**
   * 判断当前策略是否支持多标的
   * @returns {boolean} 是否支持多标的
   */
  const isMultiAssetStrategy = computed(() => {
    if (!backtestData.value.strategy) return false

    return backtestData.value.strategy.multiAsset === true ||
      backtestData.value.strategy.type === 'arbitrage' ||
      backtestData.value.strategy.type === 'hedge'
  })

  /**
   * 判断是否可以进行下一步
   * @returns {boolean} 是否可以继续
   */
  const canProceed = computed(() => {
    if (isLoading.value) return false

    switch (activeStep.value) {
      case 0:
        return !!backtestData.value.strategy // 必须选择策略
      case 1:
        return !!backtestData.value.asset // 必须选择标的
      case 2:
        return true // 参数配置由表单组件自己验证
      case 3:
        return false // 执行回测步骤由执行组件控制
      case 4:
        return false // 结果查看步骤不能继续前进
      default:
        return true
    }
  })

  // ==================== 生命周期钩子 ====================
  onMounted(async () => {
    try {
      isLoading.value = true
      loadingText.value = '加载数据中...'

      // 并行加载资产和策略数据
      await Promise.all([
        assetStore.fetchAssets(),
        strategyStore.fetchStrategies()
      ])
    } catch (error) {
      console.error('初始化数据失败:', error)
      showToast({
        type: 'fail',
        message: '数据加载失败',
        position: 'middle'
      })
    } finally {
      isLoading.value = false
    }
  })

  // ==================== 监听器 ====================
  /**
   * 监听路由参数变化
   * 用于处理直接通过URL进入特定回测记录或策略的情况
   */
  watch(() => route.query, (query) => {
    // 处理回测记录ID
    if (query.id) {
      currentRecordId.value = parseInt(query.id)
      showResult.value = true
      return
    }

    // 处理策略ID
    if (query.strategyId) {
      const strategy = strategyStore.strategies.find(s => s.id === parseInt(query.strategyId))
      if (strategy) {
        backtestData.value.strategy = strategy
        backtestData.value.asset = null
        activeStep.value = 1 // 跳转到选择标的步骤
        showResult.value = false
      }
    }
  }, { immediate: true, deep: true })

  // ==================== 方法定义 ====================
  /**
   * 进入下一步
   */
  const nextStep = async () => {
    if (activeStep.value < 4 && canProceed.value) {
      stepLoading.value = true

      try {
        // 如果是从参数配置进入执行回测，显示加载状态
        if (activeStep.value === 2) {
          isLoading.value = true
          loadingText.value = '准备回测环境...'
          await new Promise(resolve => setTimeout(resolve, 800)) // 模拟加载
          isLoading.value = false
        } else {
          loadingText.value = '准备下一步...'
          await new Promise(resolve => setTimeout(resolve, 300)) // 模拟延迟
        }

        activeStep.value++ // 切换到下一步
      } catch (error) {
        console.error('切换步骤失败:', error)
        showToast({
          type: 'fail',
          message: '操作失败，请重试',
          position: 'bottom'
        })
      } finally {
        stepLoading.value = false
      }
    }
  }

  /**
   * 返回上一步
   */
  const prevStep = async () => {
    if (activeStep.value > 0 && !isLoading.value) {
      stepLoading.value = true
      loadingText.value = '返回上一步...'

      try {
        await new Promise(resolve => setTimeout(resolve, 300)) // 模拟延迟

        // 如果当前在报告页面，需要重置状态
        if (activeStep.value === 4) {
          showResult.value = false
          status.value = 'idle'
          backtestData.value.result = null
        }

        activeStep.value-- // 返回上一步
      } catch (error) {
        console.error('切换步骤失败:', error)
        showToast({
          type: 'fail',
          message: '操作失败，请重试',
          position: 'bottom'
        })
      } finally {
        stepLoading.value = false
      }
    }
  }

  /**
   * 保存参数并进入下一步
   * @param {Object} params - 参数对象
   */
  const saveParameters = (params) => {
    backtestData.value.parameters = params
    nextStep()
  }

  /**
   * 开始回测
   */
  const startBacktest = async () => {
    try {
      status.value = 'running'
      await simulateBacktest() // 模拟回测过程

      // 生成随机回测结果
      const result = {
        finalReturn: (Math.random() * 50 - 10).toFixed(2),
        annualizedReturn: (Math.random() * 40 - 5).toFixed(2),
        maxDrawdown: (Math.random() * 30).toFixed(2),
        winRate: (Math.random() * 30 + 50).toFixed(2)
      }

      await onBacktestComplete(result) // 处理回测完成
      status.value = 'completed'
    } catch (error) {
      console.error('回测执行失败:', error)
      showToast({
        type: 'fail',
        message: '回测执行失败',
        position: 'middle'
      })
      status.value = 'idle'
    }
  }

  /**
   * 模拟回测过程
   */
  const simulateBacktest = async () => {
    const steps = [
      { name: '准备数据', progress: 25 },
      { name: '执行回测', progress: 50 },
      { name: '分析结果', progress: 75 },
      { name: '生成报告', progress: 100 }
    ]

    isLoading.value = true

    // 模拟每个步骤的进度
    for (const step of steps) {
      loadingText.value = step.name + '...'
      await new Promise(resolve => setTimeout(resolve, 600))
    }

    isLoading.value = false
  }

  /**
   * 回测完成处理
   * @param {Object} result - 回测结果
   */
  const onBacktestComplete = async (result) => {
    try {
      backtestData.value.result = result

      // 保存回测记录
      const recordId = backtestStore.addRecord({
        asset: backtestData.value.asset,
        strategy: backtestData.value.strategy,
        parameters: backtestData.value.parameters,
        result: backtestData.value.result,
        date: new Date()
      })

      // 显示回测结果
      currentRecordId.value = recordId || backtestStore.history[0].id
      showResult.value = true
      activeStep.value = 4 // 设置当前步骤为报告查看
    } catch (error) {
      console.error('生成报告失败:', error)
      showToast({
        type: 'fail',
        message: '生成报告失败',
        position: 'middle'
      })
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 处理重置回测
   * @param {Object} params - 重置参数 (可选)
   */
  const handleResetBacktest = (params = {}) => {
    showResult.value = false

    // 如果有策略ID，则设置策略并跳转到参数配置步骤
    if (params.strategyId) {
      const strategy = strategyStore.strategies.find(s => s.id === parseInt(params.strategyId))
      if (strategy) {
        backtestData.value.strategy = strategy
        backtestData.value.asset = null
        activeStep.value = 1
      }
    } else {
      // 否则重置回测数据并跳转到第一步
      backtestData.value = {
        asset: null,
        strategy: null,
        parameters: {
          startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 3, new Date().getDate()),
          endDate: new Date(),
          initialCapital: userStore.userInfo?.settings?.defaultCapital || 100000,
          strategyParams: {}
        },
        result: null
      }
      activeStep.value = 0
    }

    // 更新URL参数
    const newQuery = { ...route.query }
    delete newQuery.id
    if (params.strategyId) {
      newQuery.strategyId = params.strategyId
    } else {
      delete newQuery.strategyId
    }
    router.replace({ query: newQuery })
  }


</script>

<style scoped>
  /* ========== 布局样式 ========== */
  .backtest-container {
    min-height: 100vh;
    background-color: var(--background-color);
    padding-bottom: calc(var(--tabbar-height) + var(--safe-area-inset-bottom));
    --primary-color-rgb: 25, 137, 250;
    position: relative;
  }

  .content-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .step-content {
    flex: 1;
    min-height: 400px;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  /* ========== 步骤指示器样式 ========== */
  .custom-steps-nav {
    position: sticky;
    top: calc(var(--header-height) + var(--safe-area-inset-top));
    z-index: 10;
    display: flex;
    justify-content: space-between;
    padding: 16px 12px;
    margin: 0 12px;
    margin-top: 12px;
    margin-bottom: 12px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-lg);
  }

  .steps-line {
    position: absolute;
    top: 32px;
    left: 15%;
    right: 15%;
    height: 2px;
    background-color: var(--border-color);
    z-index: 1;
  }

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
    padding: 0 4px;
    flex: 1;
  }

  .step-icon-container {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
  }

  .step-item.active .step-icon-container {
    background-color: var(--primary-color);
  }

  .step-item.active .step-icon-container :deep(.van-icon) {
    color: white;
  }

  .step-item.completed .step-icon-container {
    background-color: var(--success-color);
  }

  .step-item.completed .step-icon-container :deep(.van-icon) {
    color: white;
  }

  .step-text {
    font-size: 12px;
    color: var(--text-secondary-color);
    text-align: center;
    width: 100%;
    white-space: nowrap;
  }

  .step-item.active .step-text {
    color: var(--text-color);
    font-weight: 500;
  }

  /* ========== 按钮样式 ========== */
  .action-buttons {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background-color: #fff;
    margin: 0;
    border-radius: 0;
    box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);
  }

  .custom-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 14px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    flex: 1;
    max-width: 45%;
    font-size: 17px;
    font-weight: 600;
    letter-spacing: 0.5px;
    border: none;
    outline: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .custom-button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .next-button {
    background-color: var(--primary-color);
    color: white;
  }

  .prev-button {
    background-color: white;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .custom-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .button-text {
    font-weight: 500;
  }

  /* ========== 加载状态样式 ========== */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .loading-text {
    margin-top: 12px;
    color: white;
  }
</style>
