<template>
  <div class="message-center">
    <!-- 消息列表 -->
    <div v-if="!hasMessages" class="empty-state">
      <van-empty description="暂无消息通知" image="comment" />
    </div>

    <MessageList
      v-else
      :messages="allMessages"
      @read="markAsRead"
      @delete="deleteMessage"
      @view-detail="viewMessageDetail"
    />

    <!-- 批量操作 -->
    <div v-if="hasMessages" class="button-container">
      <van-button
        type="primary"
        size="normal"
        icon="eye-o"
        :disabled="unreadCount === 0"
        @click="markAllAsRead"
        class="action-button read-button"
      >
        全部已读
      </van-button>
      <van-button
        plain
        type="danger"
        size="normal"
        icon="delete-o"
        @click="showClearConfirm"
        class="action-button clear-button"
      >
        清空消息
      </van-button>
    </div>

    <!-- 消息详情弹窗 -->
    <van-popup
      v-model="showMessageDetail"
      round
      position="bottom"
      :style="{ height: '60%' }"
      class="detail-popup"
    >
      <div class="message-detail" v-if="currentMessage">
        <div class="detail-header">
          <div class="header-content">
            <div class="message-icon-large" :class="getIconClass(currentMessage.type)">
              <van-icon :name="getIconName(currentMessage.type)" size="24" />
            </div>
            <div class="header-text">
              <h3>{{ currentMessage.title }}</h3>
              <div class="detail-meta">
                <span class="detail-type">{{ getMessageTypeText(currentMessage.type) }}</span>
                <span class="detail-time">{{ formatDetailTime(currentMessage.time) }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="detail-content">
          <p class="content-text">{{ currentMessage.content }}</p>

          <!-- 策略更新消息特殊处理 -->
          <template v-if="currentMessage.type === 'strategy'">
            <div class="strategy-update-info">
              <h4>更新内容</h4>
              <van-cell-group inset class="info-group">
                <van-cell title="策略名称" :value="currentMessage.data?.strategyName" />
                <van-cell title="更新内容" :label="currentMessage.data?.updateContent" />
              </van-cell-group>
              <div class="update-actions">
                <van-button type="primary" block @click="viewStrategy(currentMessage.data?.strategyId)" class="detail-action-button">查看策略</van-button>
              </div>
            </div>
          </template>

          <!-- 互动消息特殊处理 -->
          <template v-else-if="currentMessage.type === 'interaction'">
            <div class="interaction-info">
              <h4>互动详情</h4>
              <van-cell-group inset class="info-group">
                <van-cell title="用户" :value="currentMessage.data?.userName" />
                <van-cell title="互动类型" :value="getInteractionTypeText(currentMessage.data?.actionType)" />
                <van-cell title="策略" :value="currentMessage.data?.strategyName" />
              </van-cell-group>
              <div class="interaction-actions">
                <van-button type="primary" block @click="viewInteraction(currentMessage)" class="detail-action-button">查看详情</van-button>
              </div>
            </div>
          </template>
        </div>
        <div class="detail-actions">
          <van-button block type="default" @click="showMessageDetail = false" class="detail-action-button close-button">关闭</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import MessageList from './MessageList.vue'

const router = useRouter()
const showMessageDetail = ref(false)
const currentMessage = ref(null)

// 模拟消息数据
const messages = ref([
  {
    id: 1,
    type: 'system',
    title: '系统维护通知',
    content: '系统将于今晚23:00-24:00进行维护升级，届时可能会出现短暂的服务不可用，请您合理安排使用时间。',
    time: new Date('2023-06-15 10:30:00'),
    read: false
  },
  {
    id: 2,
    type: 'interaction',
    title: '策略被收藏',
    content: '用户"量化小达人"收藏了您的策略"双均线策略"',
    time: new Date('2023-06-14 15:20:00'),
    read: false,
    data: {
      userName: '量化小达人',
      actionType: 'collect',
      strategyId: 1,
      strategyName: '双均线策略'
    }
  },
  {
    id: 3,
    type: 'system',
    title: '新版本发布',
    content: 'quant-h5 v1.2.0 版本已发布，新增多项功能和优化，包括策略回测性能提升、UI界面优化等。',
    time: new Date('2023-06-10 09:15:00'),
    read: true
  },
  {
    id: 4,
    type: 'interaction',
    title: '策略评论回复',
    content: '用户"交易高手"回复了您对策略"趋势追踪"的评论',
    time: new Date(new Date().getTime() - 86400000), // 1天前
    read: false,
    data: {
      userName: '交易高手',
      actionType: 'reply',
      strategyId: 3,
      strategyName: '趋势追踪'
    }
  }
])

// 计算属性
const allMessages = computed(() => messages.value)
const unreadCount = computed(() => messages.value.filter(m => !m.read).length)
const hasMessages = computed(() => messages.value.length > 0)

// 获取消息图标
const getIconName = (type) => {
  const iconMap = {
    system: 'info-o',
    interaction: 'friends-o',
    strategy: 'chart-trending-o'
  }
  return iconMap[type] || 'bell'
}

// 获取图标样式类
const getIconClass = (type) => {
  const classMap = {
    system: 'icon-system',
    interaction: 'icon-interaction',
    strategy: 'icon-strategy'
  }
  return classMap[type] || ''
}

const markAsRead = (id) => {
  const message = messages.value.find(m => m.id === id)
  if (message) {
    message.read = true
  }
}

const markAllAsRead = () => {
  if (unreadCount.value === 0) return

  messages.value.forEach(m => m.read = true)
  showToast('已将全部消息标记为已读')
}

const deleteMessage = (id) => {
  messages.value = messages.value.filter(m => m.id !== id)

  if (showMessageDetail.value && currentMessage.value?.id === id) {
    showMessageDetail.value = false
  }

  showToast('消息已删除')
}

const showClearConfirm = () => {
  showConfirmDialog({
    title: '清空消息',
    message: '确定要清空所有消息吗？此操作不可恢复。',
    confirmButtonText: '清空',
    cancelButtonText: '取消'
  }).then(() => {
    clearAllMessages()
  }).catch(() => {
    // 取消操作
  })
}

const clearAllMessages = () => {
  messages.value = []
  showToast('已清空所有消息')
}

const viewMessageDetail = (message) => {
  currentMessage.value = message
  showMessageDetail.value = true

  // 标记为已读
  if (!message.read) {
    markAsRead(message.id)
  }
}

const formatDetailTime = (time) => {
  if (!time) return ''

  const date = new Date(time)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

const getMessageTypeText = (type) => {
  const typeMap = {
    system: '系统通知',
    interaction: '互动消息',
    strategy: '策略更新'
  }
  return typeMap[type] || '未知类型'
}

const getInteractionTypeText = (type) => {
  const typeMap = {
    collect: '收藏',
    like: '点赞',
    comment: '评论',
    reply: '回复'
  }
  return typeMap[type] || '互动'
}

const viewStrategy = (strategyId) => {
  if (!strategyId) return

  router.push({
    path: '/square',
    query: { strategyId }
  })

  showMessageDetail.value = false
}

const viewInteraction = (message) => {
  if (!message?.data?.strategyId) return

  router.push({
    path: '/square',
    query: {
      strategyId: message.data.strategyId,
      showComments: true
    }
  })

  showMessageDetail.value = false
}
</script>

<style scoped>
.message-center {
  padding: 8px var(--content-padding) 24px;
}

.message-tabs {
  margin-bottom: 16px;
}

.message-tabs :deep(.van-tabs__wrap) {
  background-color: var(--background-color-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 46px;
}

.message-tabs :deep(.van-tabs__nav) {
  padding: 0 16px;
}

.message-tabs :deep(.van-tab) {
  font-size: 15px;
  padding: 0 12px;
  font-weight: 500;
}

.message-tabs :deep(.van-tabs__line) {
  bottom: 8px;
  height: 3px;
  border-radius: 3px;
}

.message-tabs :deep(.van-tabs__content) {
  padding-top: 12px;
  background-color: transparent;
}



.empty-state {
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-state .van-empty {
  padding-bottom: 32px;
}

.empty-state :deep(.van-empty__image) {
  width: 120px;
  height: 120px;
}

.empty-state :deep(.van-empty__description) {
  font-size: 15px;
  color: var(--text-color-secondary);
  margin-top: 16px;
}

.button-container {
  display: flex;
  justify-content: space-between;
  padding: 0 var(--content-padding);
  margin-top: 24px;
  margin-bottom: 24px;
  width: 100%;
}

.action-button {
  flex: 1;
  margin: 0 8px;
  border-radius: 22px;
  font-size: 15px;
  height: 44px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.action-button:first-child {
  margin-left: 0;
}

.action-button:last-child {
  margin-right: 0;
}

.read-button, .clear-button {
  min-width: 120px;
}

.read-button {
  color: white;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.2);
}

.read-button :deep(.van-icon) {
  color: white;
  font-size: 16px;
  margin-right: 4px;
}

.clear-button :deep(.van-icon) {
  font-size: 16px;
  margin-right: 4px;
}

.read-button:disabled {
  opacity: 0.6;
  background-color: var(--primary-color);
}

.read-button:disabled :deep(.van-icon),
.read-button:disabled :deep(.van-button__text) {
  color: white;
  opacity: 0.8;
}

.detail-popup :deep(.van-popup__close-icon) {
  color: var(--text-color-secondary);
  font-size: 22px;
  top: 16px;
  right: 16px;
}

.message-detail {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color-light);
}

.header-content {
  display: flex;
  align-items: center;
}

.message-icon-large {
  width: 52px;
  height: 52px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 18px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.icon-system {
  background-color: var(--primary-color);
}

.icon-interaction {
  background-color: var(--warning-color);
}

.icon-strategy {
  background-color: var(--success-color);
}

.header-text {
  flex: 1;
}

.header-text h3 {
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color-primary);
  line-height: 1.3;
}

.detail-meta {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.detail-type {
  background-color: #f5f7fa;
  padding: 3px 10px;
  border-radius: 14px;
  margin-right: 12px;
  font-size: 13px;
  font-weight: 500;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 4px;
}

.content-text {
  line-height: 1.7;
  color: var(--text-color-regular);
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 24px;
}

.detail-actions {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color-light);
}

.detail-action-button {
  height: 44px;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.close-button {
  border: 1px solid var(--border-color);
}

.strategy-update-info,
.interaction-info {
  margin-top: 28px;
  background-color: #f9fafb;
  border-radius: 14px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.strategy-update-info h4,
.interaction-info h4 {
  margin: 0 0 16px 0;
  font-size: 17px;
  font-weight: 600;
  color: var(--text-color-primary);
}

.info-group {
  margin-bottom: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.info-group :deep(.van-cell) {
  padding: 14px 16px;
  background-color: #fff;
}

.info-group :deep(.van-cell__title) {
  font-size: 15px;
  color: var(--text-color-regular);
  font-weight: 500;
}

.info-group :deep(.van-cell__value) {
  color: var(--text-color-primary);
  font-weight: 500;
  font-size: 15px;
}

.update-actions,
.interaction-actions {
  margin-top: 16px;
}
</style>
