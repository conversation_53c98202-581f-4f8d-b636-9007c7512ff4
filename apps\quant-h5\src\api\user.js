import { get, post } from '@/utils/request'

/**
 * 用户登录
 * @param {Object} data - 登录信息
 * @param {string} data.telephone - 手机号
 * @param {string} data.password - 密码
 * @param {string} data.method - 登录方式 (password/code/wx)
 * @param {string} data.app_id - 应用ID
 * @returns {Promise}
 */
export function login(data) {
  return post('/auth/login', {
    telephone: data.telephone,
    password: data.password,
    method: data.method || 'password',
    app_id: data.app_id || 1, // 默认应用ID
    platform: '2' // 移动端平台
  })
}

/**
 * 发送验证码
 * @param {Object} data - 验证码信息
 * @param {string} data.telephone - 手机号
 * @param {string} data.type - 验证码类型 (login/register/reset)
 * @param {string} data.app_id - 应用ID
 * @returns {Promise}
 */
export function sendCode(data) {
  return post('/auth/send/code', {
    telephone: data.telephone,
    type: data.type,
    app_id: data.app_id || 1
  })
}

/**
 * 用户注册
 * @param {Object} data - 注册信息
 * @param {string} data.telephone - 手机号
 * @param {string} data.password - 密码
 * @param {string} data.code - 验证码
 * @param {string} data.app_id - 应用ID
 * @returns {Promise}
 */
export function register(data) {
  return post('/auth/register', {
    telephone: data.telephone,
    password: data.password,
    code: data.code,
    app_id: data.app_id || 1
  })
}

/**
 * 刷新Token
 * @param {string} refreshToken - 刷新令牌
 * @returns {Promise}
 */
export function refreshToken(refreshToken) {
  return post('/auth/refresh', { refresh_token: refreshToken })
}

/**
 * 获取当前用户信息
 * @returns {Promise}
 */
export function getUserInfo() {
  return get('/auth/user/info')
}

/**
 * 更新用户信息
 * @param {Object} data - 用户信息
 * @returns {Promise}
 */
export function updateUserInfo(data) {
  return post('/auth/user/update', data)
}

/**
 * 退出登录
 * @returns {Promise}
 */
export function logout() {
  return post('/auth/logout')
}

/**
 * 获取当前算力
 * @returns {Promise}
 */
export function getComputePower() {
  return get('/auth/compute-power')
}

/**
 * 充值算力
 * @param {number} amount - 充值数量
 * @returns {Promise}
 */
export function rechargeComputePower(amount) {
  return post('/auth/compute-power/recharge', { amount })
}

/**
 * 消费算力
 * @param {number} amount - 消费数量
 * @returns {Promise}
 */
export function consumeComputePower(amount) {
  return post('/auth/compute-power/consume', { amount })
}
