/**
 * 微信环境相关工具函数
 */

/**
 * 检测是否在微信环境中
 * @returns {boolean} 是否在微信环境
 */
export const isWechatEnvironment = () => {
  // 优先使用全局变量，这样可以确保整个应用使用一致的环境检测结果
  if (typeof window !== 'undefined' && window.isWechatEnvironment !== undefined) {
    return window.isWechatEnvironment;
  }

  // 如果全局变量不存在，则通过UA检测
  return typeof navigator !== 'undefined' && /MicroMessenger/i.test(navigator.userAgent);
};

/**
 * 设置微信环境下的页面标题
 * 微信浏览器中设置标题需要特殊处理
 * @param {string} title - 要设置的标题
 */
export const setWechatTitle = (title) => {
  if (!title) return;

  // 普通环境直接设置
  document.title = title;

  // 微信环境下，可能需要额外处理
  if (isWechatEnvironment()) {
    // 有些情况下微信会忽略直接设置的标题，使用iframe可以解决这个问题
    // 但在现代版本的微信中，这个hack通常不再需要
    // 如果发现标题仍然不生效，可以取消下面的注释
    /*
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.onload = () => {
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 10);
    };
    document.body.appendChild(iframe);
    */

    // 使用延时确保标题设置生效
    setTimeout(() => {
      document.title = title;
    }, 50);
  }
};

/**
 * 初始化微信环境
 * 在应用启动时调用，设置全局环境标识
 */
export const initWechatEnvironment = () => {
  const isWechat = isWechatEnvironment();

  if (isWechat && typeof window !== 'undefined') {
    // 设置全局标识
    window.isWechatEnvironment = true;

    // 添加微信环境类名
    document.body.classList.add('wechat-environment');

    // 确保微信环境中的滚动正常
    document.documentElement.style.overflow = '';
    document.body.style.overflow = '';
    document.body.style.position = 'static';

    console.log('微信环境初始化完成');
  } else if (typeof window !== 'undefined') {
    // 非微信环境，确保页面可以正常滚动
    window.isWechatEnvironment = false;

    // 确保普通环境中的滚动正常
    document.documentElement.style.overflow = '';
    document.body.style.overflow = '';
  }

  return isWechat;
};

/**
 * 确保页面可滚动
 * 在任何环境下都确保页面可以正常滚动
 */
export const ensureScrollable = () => {
  if (typeof document === 'undefined') return;

  // 确保页面可以滚动
  document.documentElement.style.overflow = '';
  document.body.style.overflow = '';

  // 如果是微信环境，可能需要额外处理
  if (isWechatEnvironment()) {
    document.body.style.position = 'static';
  }
};
