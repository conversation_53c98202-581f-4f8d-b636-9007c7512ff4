<template>
  <div class="filter-container">
    <!-- 搜索框 -->
    <div class="search-box">
      <van-search
        v-model="searchValue"
        placeholder="搜索回测案例"
        shape="round"
        clearable
        @search="onSearch"
        @clear="onClear"
      />
    </div>

    <!-- 筛选选项 -->
    <div class="filter-options">
      <van-dropdown-menu>
        <!-- 策略类型筛选 -->
        <van-dropdown-item
          v-model="filterData.strategyType"
          :options="strategyTypeOptions"
          title="策略类型"
        />

        <!-- 排序方式 -->
        <van-dropdown-item
          v-model="filterData.sortBy"
          :options="sortOptions"
          title="排序方式"
        />

        <!-- 收益率筛选 -->
        <van-dropdown-item
          v-model="filterData.returnRange"
          :options="returnRangeOptions"
          title="收益率"
        />
      </van-dropdown-menu>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

// 定义组件的输入和输出
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      strategyType: 'all',
      sortBy: 'latest',
      returnRange: 'all',
      keywords: ''
    })
  }
})

const emit = defineEmits(['update:modelValue', 'filter-change'])

// 本地状态
const searchValue = ref(props.modelValue.keywords || '')

// 筛选数据
const filterData = reactive({
  strategyType: props.modelValue.strategyType || 'all',
  sortBy: props.modelValue.sortBy || 'latest',
  returnRange: props.modelValue.returnRange || 'all'
})

// 策略类型选项
const strategyTypeOptions = [
  { text: '全部类型', value: 'all' },
  { text: '趋势跟踪', value: 'trend' },
  { text: '均值回归', value: 'mean_reversion' },
  { text: '动量策略', value: 'momentum' },
  { text: '波动率策略', value: 'volatility' }
]

// 排序方式选项
const sortOptions = [
  { text: '最新发布', value: 'latest' },
  { text: '最多点赞', value: 'likes' },
  { text: '最多收藏', value: 'favorites' },
  { text: '收益率高', value: 'return' }
]

// 收益率范围选项
const returnRangeOptions = [
  { text: '全部收益', value: 'all' },
  { text: '10%以上', value: 'above10' },
  { text: '20%以上', value: 'above20' },
  { text: '50%以上', value: 'above50' }
]

// 搜索处理
const onSearch = () => {
  emitFilterChange()
}

// 清除搜索
const onClear = () => {
  searchValue.value = ''
  emitFilterChange()
}

// 监听筛选数据变化
watch(filterData, () => {
  emitFilterChange()
}, { deep: true })

// 发送筛选变更事件
const emitFilterChange = () => {
  const filterParams = {
    strategyType: filterData.strategyType,
    sortBy: filterData.sortBy,
    returnRange: filterData.returnRange,
    keywords: searchValue.value
  }

  emit('update:modelValue', filterParams)
  emit('filter-change', filterParams)
}
</script>

<style scoped>
.filter-container {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--card-margin);
  width: 100%;
}

.search-box {
  padding: 0;
}

.search-box :deep(.van-search) {
  padding: 8px 12px;
}

.search-box :deep(.van-search__content) {
  background-color: #f5f5f5;
}

.filter-options {
  width: 100%;
}

.filter-options :deep(.van-dropdown-menu) {
  box-shadow: none;
  height: 40px;
}

.filter-options :deep(.van-dropdown-menu__item) {
  justify-content: center;
  font-size: 14px;
}
</style>
