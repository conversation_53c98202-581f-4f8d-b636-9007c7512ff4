<template>
  <div class="asset-select-container">
    <!-- 已选信息（可折叠） -->
    <div v-if="strategy" class="card info-card">
      <van-collapse v-model="activeCollapse" class="info-collapse">
        <van-collapse-item name="info" class="info-item">
          <template #title>
            <div class="collapse-title">
              <span>已选信息</span>
            </div>
          </template>

          <div class="selected-info-content">
            <!-- 回测策略 -->
            <div class="info-section">
              <h4 class="info-subtitle">（一）回测策略：</h4>
              <div class="info-item-row">
                <span class="info-label">策略名称：</span>
                <span class="info-value">{{ strategy.name }}（{{ getAssetTypeText(strategy) }}）</span>
              </div>
              <div class="info-item-row">
                <span class="info-label">策略标签：</span>
                <span class="info-value">
                  {{ getStrategyTypeText(strategy.type) }}、
                  <span v-for="(market, index) in strategy.markets" :key="market">
                    {{ market }}{{ index < strategy.markets.length - 1 ? '、' : '' }}
                  </span>
                </span>
              </div>
              <div class="info-item-row">
                <span class="info-label">策略简介：</span>
                <span class="info-value">{{ strategy.description }}</span>
              </div>
            </div>
          </div>
        </van-collapse-item>
      </van-collapse>
    </div>

    <!-- 选择标的区域 -->
    <div class="card asset-select-card">
      <h3 class="section-title-underline">选择标的</h3>

      <van-field
        v-model="searchQuery"
        placeholder="输入代码/拼音/名称搜索"
        clearable
      />

      <van-radio-group v-model="assetType" direction="horizontal">
        <van-radio name="all">全部</van-radio>
        <van-radio name="stock">股票</van-radio>
        <van-radio name="fund">基金</van-radio>
        <van-radio name="index">指数</van-radio>
      </van-radio-group>

      <div class="selection-header">
        <span class="selection-count">已选择 {{ selectedAssets.length }} 个标的</span>
        <van-button
          v-if="selectedAssets.length > 0"
          size="small"
          type="danger"
          plain
          @click="clearSelection"
        >
          清空
        </van-button>
      </div>

      <van-checkbox-group v-model="selectedAssetCodes">
        <van-cell-group>
          <van-cell
            v-for="asset in filteredAssets"
            :key="asset.code"
            :title="`${asset.name} (${asset.code})`"
            clickable
            @click="toggleAsset(asset)"
          >
            <template #right-icon>
              <van-checkbox :name="asset.code" @click.stop />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>

      <div v-if="recentAssets.length > 0" class="recent-assets">
        <h3 class="section-title">最近使用</h3>
        <van-cell-group>
          <van-cell
            v-for="asset in recentAssets"
            :key="asset.code"
            :title="`${asset.name} (${asset.code})`"
            clickable
            @click="toggleAsset(asset)"
          >
            <template #right-icon>
              <van-checkbox :name="asset.code" :checked="selectedAssetCodes.includes(asset.code)" @click.stop />
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <div class="action-buttons">
        <van-button
          type="primary"
          block
          :disabled="selectedAssets.length === 0"
          @click="confirmSelection"
        >
          确认选择 ({{ selectedAssets.length }})
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAssetStore } from '@/stores/asset'
import { useUserStore } from '@/stores/user'
import { pinyin } from 'pinyin-pro'
import { showToast } from 'vant'

/**
 * 组件事件定义
 * @emits update:modelValue 更新v-model绑定值
 * @emits select 选择完成事件(已弃用)
 */
const emit = defineEmits(['update:modelValue', 'select'])

/**
 * 组件属性定义
 * @property {Object|Array} modelValue - 当前选中的资产对象或数组
 * @property {Object} strategy - 关联的策略对象
 * @property {Number} maxSelection - 最大可选数量，默认10
 */
const props = defineProps({
  modelValue: {
    type: [Object, Array],
    default: null
  },
  strategy: {
    type: Object,
    default: null
  },
  maxSelection: {
    type: Number,
    default: 10
  }
})

// Store实例
const assetStore = useAssetStore()
const userStore = useUserStore()

// 响应式状态
const searchQuery = ref('') // 搜索关键词
const selectedAssetCodes = ref([]) // 已选择的资产代码数组
const assetType = ref('all') // 资产类型筛选: all|stock|fund|index
const activeCollapse = ref([]) // 折叠面板激活项

/**
 * 初始化已选择的标的
 * 根据传入的modelValue初始化selectedAssetCodes
 */
onMounted(() => {
  if (props.modelValue) {
    selectedAssetCodes.value = Array.isArray(props.modelValue)
      ? props.modelValue.map(asset => asset.code)
      : [props.modelValue.code]
  }
})

/**
 * 所有资产数据
 * @returns {Array} 资产对象数组
 */
const allAssets = computed(() => assetStore.assets)

/**
 * 用户最近选择的资产
 * @returns {Array} 最近使用的资产对象数组
 */
const recentAssets = computed(() => {
  return userStore.recentAssets
    .map(code => assetStore.assets.find(asset => asset.code === code))
    .filter(Boolean)
})

/**
 * 根据搜索条件过滤资产
 * @param {Array} assets - 待过滤资产数组
 * @param {String} query - 搜索关键词
 * @returns {Array} 过滤后的资产数组
 */
const filterBySearchQuery = (assets, query) => {
  const lowerQuery = query.toLowerCase()
  return assets.filter(asset => {
    // 匹配代码
    if (asset.code.includes(lowerQuery)) return true

    // 匹配中文名称
    if (asset.name.toLowerCase().includes(lowerQuery)) return true

    // 匹配拼音全拼
    const fullPinyin = pinyin(asset.name, { toneType: 'none' }).toLowerCase()
    if (fullPinyin.includes(lowerQuery)) return true

    // 匹配拼音首字母
    const initials = pinyin(asset.name, { toneType: 'none', pattern: 'first' }).toLowerCase()
    return initials.includes(lowerQuery)
  })
}

/**
 * 根据筛选条件过滤后的资产列表
 * @returns {Array} 过滤后的资产对象数组
 */
const filteredAssets = computed(() => {
  if (!allAssets.value.length) return []

  // 按资产类型筛选
  let result = assetType.value === 'all'
    ? [...allAssets.value]
    : allAssets.value.filter(asset => asset.type === assetType.value)

  // 按搜索关键词筛选
  if (searchQuery.value) {
    result = filterBySearchQuery(result, searchQuery.value)
  }

  return result
})

/**
 * 当前选中的资产对象数组
 * @returns {Array} 选中的资产对象数组
 */
const selectedAssets = computed(() => {
  return selectedAssetCodes.value
    .map(code => assetStore.assets.find(asset => asset.code === code))
    .filter(Boolean)
})

// 切换选择资产
const toggleAsset = (asset) => {
  const index = selectedAssetCodes.value.indexOf(asset.code)

  if (index === -1) {
    // 检查是否超过最大选择数量
    if (selectedAssetCodes.value.length >= props.maxSelection) {
      showToast({
        type: 'fail',
        message: `最多只能选择${props.maxSelection}个标的`,
        position: 'bottom'
      })
      return
    }

    selectedAssetCodes.value.push(asset.code)
    userStore.addRecentAsset(asset)
  } else {
    selectedAssetCodes.value.splice(index, 1)
  }

  // 更新v-model
  if (selectedAssetCodes.value.length === 0) {
    emit('update:modelValue', null)
  } else if (selectedAssetCodes.value.length === 1) {
    const singleAsset = assetStore.assets.find(a => a.code === selectedAssetCodes.value[0])
    emit('update:modelValue', singleAsset)
  } else {
    const assets = selectedAssetCodes.value.map(code => {
      return assetStore.assets.find(a => a.code === code)
    }).filter(Boolean)
    emit('update:modelValue', assets)
  }
}

// 清空选择
const clearSelection = () => {
  selectedAssetCodes.value = []
  emit('update:modelValue', null)
}

// 确认选择
const confirmSelection = () => {
  if (selectedAssetCodes.value.length === 0) {
    showToast({
      type: 'fail',
      message: '请至少选择一个标的',
      position: 'bottom'
    })
    return
  }

  // 根据选择数量决定返回单个对象还是数组
  if (selectedAssetCodes.value.length === 1) {
    const singleAsset = assetStore.assets.find(a => a.code === selectedAssetCodes.value[0])
    // 只更新v-model，不再触发select事件
    emit('update:modelValue', singleAsset)
    showToast({
      type: 'success',
      message: '已选择标的',
      position: 'bottom'
    })
  } else {
    const assets = selectedAssetCodes.value.map(code => {
      return assetStore.assets.find(a => a.code === code)
    }).filter(Boolean)
    // 只更新v-model，不再触发select事件
    emit('update:modelValue', assets)
    showToast({
      type: 'success',
      message: `已选择${assets.length}个标的`,
      position: 'bottom'
    })
  }
}

// 获取标的类型文本
const getAssetTypeText = (strategy) => {
  if (strategy.type === 'arbitrage' || strategy.type === 'hedge') {
    return '多标的'
  } else if (strategy.type === 'dynamic') {
    return '动态标的'
  }
  return '单标的'
}

// 获取策略类型文本
const getStrategyTypeText = (type) => {
  switch(type) {
    case 'trend':
      return '趋势型'
    case 'mean_reversion':
      return '均值回归'
    case 'arbitrage':
      return '套利策略'
    case 'hedge':
      return '对冲策略'
    case 'dynamic':
      return '动态策略'
    default:
      return '其他策略'
  }
}

</script>

<style scoped>
/* 主要样式 */

/* 布局和容器样式 */
.asset-select-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 12px;
}

/* 卡片通用样式 */
.card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 16px;
  margin-bottom: 16px;
  overflow: hidden;
}

/* 信息卡片 */
.info-card {
  padding: 0;
}

/* 标的选择卡片 */
.asset-select-card {
  padding: 16px;
}

/* 折叠面板样式 */
.info-collapse {
  width: 100%;
}

/* 折叠面板标题 */
.collapse-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
}

/* 自定义折叠面板样式 */
:deep(.van-collapse-item__title) {
  font-weight: 500 !important;
  color: var(--text-color-primary) !important;
  background-color: white !important;
  padding: 16px !important;
  height: auto !important;
  line-height: 1.5 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

:deep(.van-cell__title) {
  flex: 1 !important;
}

:deep(.van-cell__right-icon) {
  position: static !important;
  margin-left: 4px !important;
  transform: rotate(0deg) !important;
  transition: transform 0.3s !important;
}

:deep(.van-cell--expanded) .van-cell__right-icon {
  transform: rotate(180deg) !important;
}

:deep(.van-collapse-item__content) {
  padding: 0 16px 16px;
  background-color: white;
}

/* 已选信息内容 */
.selected-info-content {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-section {
  margin-bottom: 12px;
}

/* 信息项行 */
.info-item-row {
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-label {
  font-weight: 500;
  color: var(--text-color-secondary);
}

.info-value {
  color: var(--text-color-primary);
}

/* 区块标题 */
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
  padding: 0;
}

/* 子标题 */
.info-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
}

/* 区域标题带下划线 */
.section-title-underline {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin-top: 0;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color-light);
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-md);
  margin-bottom: 12px;
}

.selection-count {
  font-size: var(--font-size-md);
  color: var(--text-color-secondary);
}

/* 区域标题 */
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color-primary);
  margin-top: 0;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color-light);
}

.recent-assets {
  margin-top: var(--spacing-md);
}

.action-buttons {
  margin-top: var(--spacing-lg);
  padding: 0 var(--spacing-md);
}

/* 策略卡片样式 */
.selected-strategy-card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 16px;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.card-title {
  margin: 0;
  font-size: 15px;
  font-weight: bold;
  color: var(--text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90%;
}

.strategy-badge {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 0 8px 0 8px;
  color: white;
  font-weight: 500;
  z-index: 1;
}

.badge-new {
  background-color: #1989fa;
}

.badge-hot {
  background-color: #ff976a;
}

.badge-popular {
  background-color: #07c160;
}

.card-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin-bottom: 0;
}

.card-description {
  color: var(--text-color-secondary);
  font-size: 13px;
  margin-bottom: 5px;
  flex-grow: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 2px;
  margin-top: 2px;
}

.type-tag,
.market-tag {
  transform: scale(0.95);
  transform-origin: left center;
  font-size: 11px;
}
</style>
