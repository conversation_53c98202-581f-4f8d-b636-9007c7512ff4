# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2021/10/19 15:47
# @File           : exception.py
# @IDE            : PyCharm
# @desc           : 全局异常处理

from fastapi.responses import JSONResponse
from starlette.exceptions import HTTPException as StarletteHTTPException
from fastapi.exceptions import RequestValidationError
from starlette import status
from fastapi import Request
from fastapi.encoders import jsonable_encoder
from fastapi import FastAPI
from core.logger import logger
from config.settings import DEBUG


class CustomException(Exception):
    """
    自定义异常基类
    
    用于业务逻辑中的自定义异常，可以指定:
    1. 错误消息(msg)
    2. 业务错误码(code)
    3. HTTP状态码(status_code)
    4. 详细描述(desc)
    
    参数:
        msg: str - 错误消息(必填)
        code: int - 业务错误码(默认400)
        status_code: int - HTTP状态码(默认200)
        desc: str - 详细错误描述(可选)
        
    使用示例:
        raise CustomException(
            msg="用户不存在",
            code=40401,
            status_code=404,
            desc="根据ID查询用户时未找到记录"
        )
    """

    def __init__(
            self,
            msg: str,
            code: int = status.HTTP_400_BAD_REQUEST,
            status_code: int = status.HTTP_200_OK,
            desc: str = None
    ):
        self.msg = msg
        self.code = code
        self.status_code = status_code
        self.desc = desc


def register_exception(app: FastAPI):
    """
    异常捕捉
    """

    @app.exception_handler(CustomException)
    async def custom_exception_handler(request: Request, exc: CustomException):
        """
        自定义异常处理器
        
        处理所有继承自CustomException的异常
        返回格式化的JSON响应:
        {
            "message": "错误消息",
            "code": 业务错误码
        }
        
        参数:
            request: Request - FastAPI请求对象
            exc: CustomException - 捕获的异常对象
            
        返回:
            JSONResponse: 包含错误信息的JSON响应
            
        日志:
            1. 在DEBUG模式下打印请求URL和异常详情
            2. 记录完整的异常堆栈信息
        """
        if DEBUG:
            print("请求地址", request.url.__str__())
            print("捕捉到重写CustomException异常异常：custom_exception_handler")
            print(exc.desc)
            print(exc.msg)
        # 打印栈信息，方便追踪排查异常
        logger.exception(exc)
        return JSONResponse(
            status_code=exc.status_code,
            content={"message": exc.msg, "code": exc.code},
        )

    @app.exception_handler(StarletteHTTPException)
    async def unicorn_exception_handler(request: Request, exc: StarletteHTTPException):
        """
        HTTP异常处理器
        
        处理所有StarletteHTTPException异常
        返回格式化的JSON响应:
        {
            "code": HTTP状态码,
            "message": 错误详情
        }
        
        参数:
            request: Request - FastAPI请求对象
            exc: StarletteHTTPException - 捕获的HTTP异常对象
            
        返回:
            JSONResponse: 包含错误信息的JSON响应
            
        日志:
            1. 在DEBUG模式下打印请求URL和异常详情
            2. 记录完整的异常堆栈信息
        """
        if DEBUG:
            print("请求地址", request.url.__str__())
            print("捕捉到重写HTTPException异常异常：unicorn_exception_handler")
            print(exc.detail)
        # 打印栈信息，方便追踪排查异常
        logger.exception(exc)
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "code": exc.status_code,
                "message": exc.detail,
            }
        )

    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(request: Request, exc: RequestValidationError):
        """
        请求验证异常处理器
        
        处理FastAPI请求参数验证错误
        自动转换常见验证错误为中文提示
        
        支持的错误类型转换:
        1. 必填字段缺失 -> "请求失败，缺少必填项！"
        2. 列表类型错误 -> "类型错误，提交参数应该为列表！"
        3. 整数类型错误 -> "类型错误，提交参数应该为整数！"
        4. 布尔值类型错误 -> "类型错误，提交参数应该为布尔值！"
        
        参数:
            request: Request - FastAPI请求对象
            exc: RequestValidationError - 捕获的验证异常对象
            
        返回:
            JSONResponse: 包含错误信息的JSON响应
            
        日志:
            1. 在DEBUG模式下打印请求URL和异常详情
            2. 记录完整的异常堆栈信息
        """
        if DEBUG:
            print("请求地址", request.url.__str__())
            print("捕捉到重写请求验证异常异常：validation_exception_handler")
            print(exc.errors())
        # 打印栈信息，方便追踪排查异常
        logger.exception(exc)
        msg = exc.errors()[0].get("msg")
        if msg == "field required":
            msg = "请求失败，缺少必填项！"
        elif msg == "value is not a valid list":
            print(exc.errors())
            msg = f"类型错误，提交参数应该为列表！"
        elif msg == "value is not a valid int":
            msg = f"类型错误，提交参数应该为整数！"
        elif msg == "value could not be parsed to a boolean":
            msg = f"类型错误，提交参数应该为布尔值！"
        elif msg == "Input should be a valid list":
            msg = f"类型错误，输入应该是一个有效的列表！"
        return JSONResponse(
            status_code=200,
            content=jsonable_encoder(
                {
                    "message": msg,
                    "body": exc.body,
                    "code": status.HTTP_400_BAD_REQUEST
                }
            ),
        )

    @app.exception_handler(ValueError)
    async def value_exception_handler(request: Request, exc: ValueError):
        """
        捕获值异常
        """
        if DEBUG:
            print("请求地址", request.url.__str__())
            print("捕捉到值异常：value_exception_handler")
            print(exc.__str__())
        # 打印栈信息，方便追踪排查异常
        logger.exception(exc)
        return JSONResponse(
            status_code=200,
            content=jsonable_encoder(
                {
                    "message": exc.__str__(),
                    "code": status.HTTP_400_BAD_REQUEST
                }
            ),
        )

    @app.exception_handler(Exception)
    async def all_exception_handler(request: Request, exc: Exception):
        """
        捕获全部异常
        """
        if DEBUG:
            print("请求地址", request.url.__str__())
            print("捕捉到全局异常：all_exception_handler")
            print(exc.__str__())
        # 打印栈信息，方便追踪排查异常
        logger.exception(exc)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=jsonable_encoder(
                {
                    "message": "接口异常！",
                    "code": status.HTTP_500_INTERNAL_SERVER_ERROR
                }
            ),
        )
