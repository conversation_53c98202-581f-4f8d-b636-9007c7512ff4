#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : pydantic 模型，用于应用相关的数据库序列化操作

from pydantic import BaseModel, ConfigDict
from core.data_types import DatetimeStr


class AppBase(BaseModel):
    """应用基础信息"""
    name: str
    version: str | None = "1.0.0"
    description: str | None = None
    is_active: bool = True


class AppBaseIn(AppBase):
    """创建应用基础信息"""
    pass


class AppBaseUpdate(AppBase):
    """更新应用基础信息"""
    name: str | None = None
    version: str | None = None
    description: str | None = None
    is_active: bool | None = None


class AppBaseOut(AppBase):
    """应用基础信息输出"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    update_datetime: DatetimeStr
    create_datetime: DatetimeStr


class AppSettings(BaseModel):
    """应用设置"""
    app_id: int
    # 登录设置
    allow_guest: bool = False  # 是否允许游客访问
    allow_password_login: bool = True  # 是否允许密码登录
    allow_code_login: bool = True  # 是否允许验证码登录
    allow_wx_login: bool = False  # 是否允许微信登录
    # 微信设置
    wx_app_id: str | None = None  # 微信AppID
    wx_app_secret: str | None = None  # 微信AppSecret
    # 其他设置
    require_real_name: bool = False  # 是否要求实名认证
    require_phone: bool = True  # 是否要求绑定手机号
    require_email: bool = False  # 是否要求绑定邮箱


class AppSettingsIn(AppSettings):
    """创建应用设置"""
    pass


class AppSettingsUpdate(AppSettings):
    """更新应用设置"""
    allow_guest: bool | None = None
    allow_password_login: bool | None = None
    allow_code_login: bool | None = None
    allow_wx_login: bool | None = None
    wx_app_id: str | None = None
    wx_app_secret: str | None = None
    require_real_name: bool | None = None
    require_phone: bool | None = None
    require_email: bool | None = None


class AppSettingsOut(AppSettings):
    """应用设置输出"""
    model_config = ConfigDict(from_attributes=True)

    id: int
    update_datetime: DatetimeStr
    create_datetime: DatetimeStr


class AppFullOut(AppBaseOut):
    """完整应用信息输出"""
    model_config = ConfigDict(from_attributes=True)

    settings: AppSettingsOut | None = None
