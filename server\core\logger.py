import os
import time
from loguru import logger
from config.settings import BASE_DIR

"""
日志配置模块

基于loguru库实现日志记录功能，主要特性:
1. 按日志级别分离文件(info/error)
2. 每日轮转日志文件
3. 保留最近3天的日志
4. 线程安全(enqueue=True)

配置说明:
- 日志路径: {BASE_DIR}/logs/
- 日志文件命名: info_YYYY-MM-DD.log / error_YYYY-MM-DD.log
- 轮转时间: 每天00:00
- 编码: UTF-8

使用示例:
from core.logger import logger

logger.info("这是一条信息日志")
logger.error("这是一条错误日志")

更多配置参考: https://github.com/Delgan/loguru
"""

# 移除控制台输出
logger.remove(handler_id=None)

log_path = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(log_path):
    os.mkdir(log_path)

# 信息日志文件路径，格式: info_YYYY-MM-DD.log
log_path_info = os.path.join(log_path, f'info_{time.strftime("%Y-%m-%d")}.log')

# 错误日志文件路径，格式: error_YYYY-MM-DD.log
log_path_error = os.path.join(log_path, f'error_{time.strftime("%Y-%m-%d")}.log')

# 添加INFO级别日志处理器
# rotation="00:00" - 每天午夜轮转日志文件
# retention="3 days" - 保留最近3天的日志
# enqueue=True - 线程安全
# encoding="UTF-8" - 使用UTF-8编码
# level="INFO" - 记录INFO及以上级别日志
info = logger.add(log_path_info, rotation="00:00", retention="3 days", enqueue=True, encoding="UTF-8", level="INFO")

# 添加ERROR级别日志处理器
# level="ERROR" - 只记录ERROR级别日志
error = logger.add(log_path_error, rotation="00:00", retention="3 days", enqueue=True, encoding="UTF-8", level="ERROR")
