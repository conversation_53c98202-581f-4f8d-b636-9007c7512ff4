:root {
  /* 基础颜色系统 */
  --primary-color: #1989fa;
  --primary-color-light: #e6f7ff;
  --primary-color-dark: #0e5aa7;
  --success-color: #07c160;
  --warning-color: #ff976a;
  --danger-color: #ee0a24;
  --info-color: #909399;

  /* 文本颜色 */
  --text-color-primary: #323233;
  --text-color-regular: #646566;
  --text-color-secondary: #969799;
  --text-color-placeholder: #c8c9cc;
  --text-color-disabled: #c8c9cc;
  --text-color-inverse: #ffffff;

  /* 背景颜色 */
  --background-color: #f7f8fa;
  --background-color-light: #ffffff;
  --background-color-card: #ffffff;

  /* 边框颜色 */
  --border-color: #ebedf0;
  --border-color-light: #f2f3f5;
  --border-color-dark: #dcdee0;

  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.12);

  /* 圆角 */
  --border-radius-sm: 2px;
  --border-radius-md: 4px;
  --border-radius-lg: 8px;
  --border-radius-xl: 16px;
  --border-radius-max: 999px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-xxl: 32px;

  /* 字体大小 */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 20px;

  /* 字体粗细 */
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;

  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-loose: 1.8;

  /* 动画 */
  --transition-duration: 0.3s;
  --transition-timing-function: ease;

  /* 组件特定变量 */
  --header-height: 44px; /* 减小导航栏高度 */
  --tabbar-height: 50px;
  --safe-area-inset-top: env(safe-area-inset-top, 0);
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0);
  --content-padding: 16px;

  /* 移动端视口高度修复 */
  --vh: 1vh;

  /* 卡片样式 */
  --card-padding: 16px;
  --card-margin: 12px;
  --card-border-radius: var(--border-radius-lg);
  --card-shadow: var(--shadow-sm);

  /* 按钮样式 */
  --button-height: 44px;
  --button-font-size: var(--font-size-md);
  --button-border-radius: var(--border-radius-md);

  /* 表单样式 */
  --form-item-margin-bottom: 16px;
  --form-label-width: 90px;
  --form-label-color: var(--text-color-regular);

  /* 图表样式 */
  --chart-height: 300px;
  --chart-background: var(--background-color-light);
  --chart-border-radius: var(--border-radius-md);
  --chart-padding: var(--spacing-md);
}

/* 暗色主题变量 - 为未来做准备 */
.dark-theme {
  --primary-color: #177ddc;
  --primary-color-light: #1f1f1f;
  --primary-color-dark: #0a53be;
  --success-color: #49aa19;
  --warning-color: #d89614;
  --danger-color: #a61d24;
  --info-color: #6b6b6b;

  --text-color-primary: #e6e6e6;
  --text-color-regular: #cccccc;
  --text-color-secondary: #a6a6a6;
  --text-color-placeholder: #666666;
  --text-color-disabled: #666666;
  --text-color-inverse: #1f1f1f;

  --background-color: #141414;
  --background-color-light: #1f1f1f;
  --background-color-card: #1f1f1f;

  --border-color: #303030;
  --border-color-light: #3a3a3a;
  --border-color-dark: #4d4d4d;

  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
}
