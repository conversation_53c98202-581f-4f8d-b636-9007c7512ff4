<!--
  加载容器组件 (LoadingContainer)

  该组件用于包装需要异步加载数据的内容，在加载过程中显示加载指示器，
  加载完成后显示实际内容。支持延迟显示加载状态，避免闪烁。

  功能：
  1. 在加载状态下显示加载指示器
  2. 加载完成后显示子组件内容
  3. 支持自定义加载文本、大小、颜色等
  4. 支持延迟显示加载状态，避免短暂加载导致的闪烁

  使用方式：
  <LoadingContainer :loading="isLoading" text="加载数据中...">
    <YourComponent />
  </LoadingContainer>
-->
<template>
  <div class="loading-wrapper">
    <!-- 加载状态显示 -->
    <div v-if="showLoading" class="loading-container">
      <van-loading
        :size="size"
        :color="color"
        :vertical="vertical"
        class="loading-indicator"
      >
        {{ text }}
      </van-loading>
    </div>

    <!-- 非加载状态显示实际内容 -->
    <slot v-else></slot>
  </div>
</template>

<script setup>
import { ref, watch, onBeforeUnmount } from 'vue'

/**
 * 组件属性定义
 *
 * @property {Boolean} loading - 是否处于加载状态
 * @property {String} text - 加载状态下显示的文本
 * @property {String} size - 加载指示器的大小
 * @property {String} color - 加载指示器的颜色
 * @property {Boolean} vertical - 是否垂直排列图标和文字
 * @property {Number} delay - 显示加载状态的延迟时间(毫秒)
 */
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  text: {
    type: String,
    default: '加载中...'
  },
  size: {
    type: String,
    default: '36px'
  },
  color: {
    type: String,
    default: 'var(--primary-color)'
  },
  vertical: {
    type: Boolean,
    default: true
  },
  delay: {
    type: Number,
    default: 200
  }
})

// 控制加载状态显示
const showLoading = ref(false)
let timer = null

/**
 * 监听加载状态变化
 *
 * 当加载状态变为true时，延迟显示加载指示器，避免闪烁
 * 当加载状态变为false时，立即隐藏加载指示器
 */
watch(() => props.loading, (val) => {
  // 清除之前的定时器
  clearTimeout(timer)

  if (val) {
    // 延迟显示加载状态
    timer = setTimeout(() => {
      showLoading.value = true
    }, props.delay)
  } else {
    // 立即隐藏加载状态
    showLoading.value = false
  }
}, { immediate: true })

/**
 * 组件销毁前清理定时器
 */
onBeforeUnmount(() => {
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
})
</script>

<style scoped>
.loading-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  padding: var(--spacing-xl);
  width: 100%;
}

.loading-indicator {
  opacity: 0;
  animation: fade-in 0.3s ease forwards;
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}
</style>
