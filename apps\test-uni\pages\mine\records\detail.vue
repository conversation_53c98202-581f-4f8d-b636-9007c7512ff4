<template>
  <view class="detail-container">
    <view class="detail-header">
      <text class="strategy-name">{{ detail.strategyName }}</text>
      <text class="backtest-time">{{ detail.createTime }}</text>
    </view>

    <view class="detail-stats">
      <view class="stat-card">
        <text class="stat-title">收益率</text>
        <text :class="['stat-value', detail.profitRate >= 0 ? 'profit' : 'loss']">
          {{ detail.profitRate }}%
        </text>
      </view>
      <view class="stat-card">
        <text class="stat-title">最大回撤</text>
        <text class="stat-value">{{ detail.maxDrawdown }}%</text>
      </view>
      <view class="stat-card">
        <text class="stat-title">胜率</text>
        <text class="stat-value">{{ detail.winRate }}%</text>
      </view>
      <view class="stat-card">
        <text class="stat-title">交易次数</text>
        <text class="stat-value">{{ detail.tradeCount }}</text>
      </view>
    </view>

    <view class="chart-container">
      <view class="section-title">收益曲线</view>
      <image
        src="/static/images/chart-placeholder.png"
        mode="widthFix"
        class="chart-img"
      ></image>
    </view>

    <view class="params-container">
      <view class="section-title">回测参数</view>
      <view class="params-list">
        <view class="param-item" v-for="(value, key) in detail.params" :key="key">
          <text class="param-name">{{ key }}:</text>
          <text class="param-value">{{ value }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      detail: {
        id: 1,
        strategyName: 'MACD金叉策略',
        createTime: '2025-04-20 14:30',
        profitRate: 15.6,
        maxDrawdown: 8.2,
        winRate: 62.5,
        tradeCount: 48,
        params: {
          '基准指数': '沪深300',
          '回测周期': '2020-01-01 至 2025-04-20',
          '初始资金': '100000',
          '交易费用': '0.0015',
          'MACD快线': '12',
          'MACD慢线': '26',
          'MACD信号线': '9'
        }
      }
    }
  },
  onLoad(options) {
    // TODO: 根据options.id获取回测详情数据
  }
}
</script>

<style lang="scss" scoped>
.detail-container {
  padding: 20rpx;

  .detail-header {
    margin-bottom: 30rpx;

    .strategy-name {
      font-size: 36rpx;
      font-weight: bold;
      display: block;
      margin-bottom: 10rpx;
    }

    .backtest-time {
      font-size: 26rpx;
      color: #999;
    }
  }

  .detail-stats {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30rpx;

    .stat-card {
      width: 48%;
      background-color: #fff;
      border-radius: 16rpx;
      padding: 20rpx;
      margin: 1%;
      box-sizing: border-box;

      .stat-title {
        font-size: 26rpx;
        color: #666;
        display: block;
        margin-bottom: 10rpx;
      }

      .stat-value {
        font-size: 32rpx;
        font-weight: bold;

        &.profit {
          color: #f56c6c;
        }

        &.loss {
          color: #67c23a;
        }
      }
    }
  }

  .section-title {
    font-size: 30rpx;
    font-weight: bold;
    margin: 20rpx 0;
    padding-left: 10rpx;
    border-left: 6rpx solid #3c96f3;
  }

  .chart-container {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 20rpx;
    margin-bottom: 30rpx;

    .chart-img {
      width: 100%;
    }
  }

  .params-container {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 20rpx;

    .params-list {
      .param-item {
        display: flex;
        padding: 15rpx 0;
        border-bottom: 1rpx solid #eee;

        .param-name {
          width: 200rpx;
          color: #666;
        }

        .param-value {
          flex: 1;
        }
      }
    }
  }
}
</style>