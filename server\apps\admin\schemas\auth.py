from pydantic import BaseModel, field_validator
from sqlalchemy.ext.asyncio import AsyncSession

from core.validator import vali_telephone
from models.admin.organ.user import AdminUser
from apps.admin.schemas.organ import UserPasswordOut


class Auth(BaseModel):
    user: AdminUser = None
    db: AsyncSession
    data_range: int | None = None
    dept_ids: list | None = []

    class Config:
        # 接收任意类型
        arbitrary_types_allowed = True


class LoginForm(BaseModel):
    telephone: str
    password: str
    method: str = '0'  # 认证方式，0：密码登录，1：短信登录，2：微信一键登录
    platform: str = '0'  # 登录平台，0：PC端管理系统，1：移动端管理系统

    # 重用验证器：https://docs.pydantic.dev/dev-v2/usage/validators/#reuse-validators
    normalize_telephone = field_validator('telephone')(vali_telephone)


class WXLoginForm(BaseModel):
    telephone: str | None = None
    code: str
    method: str = '2'  # 认证方式，0：密码登录，1：短信登录，2：微信一键登录
    platform: str = '1'  # 登录平台，0：PC端管理系统，1：移动端管理系统


class LoginResult(BaseModel):
    status: bool | None = False
    user: UserPasswordOut | None = None
    msg: str | None = None

    class Config:
        arbitrary_types_allowed = True


