#!/usr/bin/python
# -*- coding: utf-8 -*-
# @desc           : 登录相关的数据模型

from pydantic import BaseModel, field_validator
from apps.front.organ import schemas
from core.validator import vali_telephone


class LoginForm(BaseModel):
    """基础登录表单"""
    app_id: int  # 应用ID
    method: str  # 登录方式：password（密码）、code（验证码）、wx（微信）
    telephone: str | None = None  # 手机号（密码登录和验证码登录需要）
    password: str | None = None  # 密码（密码登录需要）
    code: str | None = None  # 验证码（验证码登录和微信登录需要）

    @field_validator('telephone')
    def normalize_telephone(cls, v):
        if v is not None:
            return vali_telephone(v)
        return v

    @field_validator('method')
    def validate_method(cls, v):
        if v not in ['password', 'code', 'wx']:
            raise ValueError('无效的登录方式')
        return v


class LoginResult(BaseModel):
    """登录结果"""
    status: bool = False
    user: schemas.UserOut | None = None
    msg: str | None = None

    class Config:
        arbitrary_types_allowed = True


class SendCodeForm(BaseModel):
    """发送验证码表单"""
    app_id: int
    telephone: str
    type: str = "login"  # 验证码类型：login（登录）、register（注册）、reset（重置密码）

    # 重用验证器
    normalize_telephone = field_validator('telephone')(vali_telephone)

    @field_validator('type')
    def validate_type(cls, v):
        if v not in ['login', 'register', 'reset']:
            raise ValueError('无效的验证码类型')
        return v


class RefreshToken(BaseModel):
    """刷新Token"""
    app_id: int
    refresh_token: str
