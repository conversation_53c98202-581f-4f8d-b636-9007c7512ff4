<template>
  <view class="strategy-container">
    <!-- 搜索框 -->
    <view class="search-box">
      <u-search
        placeholder="搜索策略"
        v-model="searchText"
        :show-action="true"
        action-text="搜索"
        @search="handleSearch"
      ></u-search>
    </view>

    <!-- 分类筛选 -->
    <view class="filter-section">
      <u-tabs
        :list="categoryList"
        :current="currentCategory"
        @change="categoryChange"
      ></u-tabs>
    </view>

    <!-- 策略列表 -->
    <view class="strategy-list">
      <view
        class="strategy-item"
        v-for="(item, index) in strategyList"
        :key="index"
        @click="viewStrategyDetail(item)"
      >
        <view class="strategy-header">
          <text class="strategy-name">{{ item.name }}</text>
          <text class="strategy-author">作者: {{ item.author }}</text>
        </view>
        <view class="strategy-stats">
          <view class="stat-item">
            <u-icon name="thumb-up"></u-icon>
            <text>{{ item.likes }}</text>
          </view>
          <view class="stat-item">
            <u-icon name="star"></u-icon>
            <text>{{ item.collections }}</text>
          </view>
          <view class="stat-item">
            <u-icon name="chat"></u-icon>
            <text>{{ item.comments }}</text>
          </view>
        </view>
        <view class="strategy-desc">{{ item.description }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchText: '',
      currentCategory: 0,
      categoryList: [
        { name: '全部' },
        { name: '趋势' },
        { name: '震荡' },
        { name: '套利' }
      ],
      strategyList: [
        {
          id: 1,
          name: 'MACD金叉策略',
          author: '量化小王子',
          likes: 256,
          collections: 189,
          comments: 42,
          description: '基于MACD指标的金叉买入死叉卖出策略，适合趋势行情'
        },
        {
          id: 2,
          name: 'RSI超买超卖策略',
          author: '量化小公主',
          likes: 189,
          collections: 156,
          comments: 35,
          description: 'RSI指标超买超卖策略，适合震荡行情'
        }
      ]
    }
  },
  methods: {
    handleSearch() {
      // TODO: 实现搜索功能
    },
    categoryChange(index) {
      this.currentCategory = index
      // TODO: 根据分类筛选策略
    },
    viewStrategyDetail(item) {
      uni.navigateTo({
        url: `/pages/strategy/detail?id=${item.id}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.strategy-container {
  padding: 20rpx;

  .search-box {
    margin-bottom: 20rpx;
  }

  .filter-section {
    margin-bottom: 30rpx;
  }

  .strategy-list {
    .strategy-item {
      background-color: #fff;
      border-radius: 16rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;

      .strategy-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15rpx;

        .strategy-name {
          font-size: 32rpx;
          font-weight: bold;
        }

        .strategy-author {
          font-size: 24rpx;
          color: #999;
        }
      }

      .strategy-stats {
        display: flex;
        margin-bottom: 15rpx;

        .stat-item {
          display: flex;
          align-items: center;
          margin-right: 30rpx;
          font-size: 24rpx;
          color: #666;

          text {
            margin-left: 10rpx;
          }
        }
      }

      .strategy-desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
      }
    }
  }
}
</style>
